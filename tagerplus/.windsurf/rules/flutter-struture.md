---
trigger: manual
---

# Flutter Development Rules for AI Assistant

You are a Flutter expert specializing in clean architecture, GetX state management, and maintainable patterns.

## 📁 Project Structure

```
lib/
├── core/                    # App-wide configs & utilities
│   ├── constants/          # app_colors.dart, app_defaults.dart
│   ├── themes/            # app_themes.dart
│   ├── models/            # Data models + generated/
│   └── helpers/           # Helper functions
├── pages/                 # Feature-based UI
│   └── [feature]/[action]/ # e.g., auth/login/
│       ├── [action].dart   # Main UI widget
│       ├── controller.dart # GetX controller
│       └── components/     # Page-specific widgets
├── services/              # Global services (auth.dart, api.dart)
├── components/            # Shared components
└── main.dart
```

## 🎯 Naming Conventions

- **Files/Folders**: `snake_case` (user_profile.dart)
- **Pages**: `[Feature][Action]Page` (UsersUpdatePage)
- **Controllers**: `[Feature][Action]Controller` (UsersUpdateController)
- **Services**: `[Purpose]Service` (AuthService)
- **Variables**: `camelCase`, private: `_camelCase`

## 🎛️ Controller Template

```dart
class [Feature][Action]Controller extends GetxController {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController fieldController = TextEditingController();

  final RxBool _isLoading = false.obs;
  bool get isLoading => _isLoading.value;
  set isLoading(bool value) => _isLoading.value = value;

  String? validateField(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'field_required'.tr;
    }
    return null;
  }

  Future<void> actionMethod() async {
    if (isLoading) return;
    if (!(formKey.currentState?.validate() ?? false)) return;

    isLoading = true;
    try {
      await Service.to.method();
      Get.snackbar('success'.tr, 'operation_successful'.tr);
    } catch (e) {
      if (kDebugMode) print('Error: $e');
      Get.snackbar('error'.tr, e.toString());
    } finally {
      isLoading = false;
    }
  }

  @override
  void onClose() {
    fieldController.dispose();
    super.onClose();
  }
}
```

## 🎨 Page Template

```dart
class [Feature][Action]Page extends StatelessWidget {
  const [Feature][Action]Page({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<[Feature][Action]Controller>();

    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          children: [
            HeaderWidget(),
            Container(
              padding: const EdgeInsets.all(AppDefaults.padding * 2),
              child: MainContentWidget(controller: controller),
            ),
          ],
        ),
      ),
    );
  }
}

// Separate widget classes for complex components
class MainContentWidget extends StatelessWidget {
  final [Feature][Action]Controller controller;

  const MainContentWidget({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Form(
      key: controller.formKey,
      child: Column(
        children: [
          // Form fields and components
        ],
      ),
    );
  }
}
```

## 🎨 Constants Structure

```dart
class AppColors {
  static const Color primary = Color(0xFF00AD48);
  static const Color scaffoldBackground = Color(0xFFFFFFFF);
}

class AppDefaults {
  static const double radius = 15;
  static const double padding = 15;
  static BorderRadius borderRadius = BorderRadius.circular(radius);
}
```

## 📋 Best Practices

### ✅ Always Do

- Use `StatelessWidget` with GetX controllers
- **Create separate widget classes** for complex UI (not private methods)
- **Use absolute imports**: `package:app_name/path/file.dart`
- Use private variables with getters/setters in controllers
- Handle errors with debug info: `if (kDebugMode) print('Error: $e')`
- Use `const` constructors where possible
- Dispose resources in `onClose()`

### ❌ Never Do

- Use `StatefulWidget` with GetX (unless absolutely necessary)
- **Use private methods like `_buildWidget()` for rendering**
- **Use relative imports** (`../../`)
- Access controller variables without Obx/GetBuilder
- Hardcode strings or values
- Mix business logic in UI widgets

## 🚀 Import Organization

```dart
// Flutter SDK
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

// Third-party packages
import 'package:get/get.dart';

// Local imports (absolute paths)
import 'package:app_name/core/constants/app_colors.dart';
import 'package:app_name/services/auth_service.dart';
```

## 🎭 Service Template

```dart
class [Purpose]Service extends GetxService {
  static [Purpose]Service get to => Get.find();

  final RxBool _isInitialized = false.obs;
  bool get isInitialized => _isInitialized.value;

  Future<void> initialize() async {
    // Logic here
    _isInitialized.value = true;
  }
}
```

**Key Principles**: Separation of concerns, feature-based organization, consistency, maintainability, and scalability. Always prioritize readable, maintainable code over brevity.
