import 'dart:convert';
import 'package:get/get.dart';
import 'package:tagerplus/core/api/api.dart';
import 'package:tagerplus/services/storage.dart';

class CartItem {
  final ItemOut item;
  RxInt quantity;

  CartItem({required this.item, required this.quantity});
}

class CartService extends GetxService {
  static CartService get to => Get.find();

  // Map of wholesaler ID to list of items
  final _cart = <int, RxList<CartItem>>{}.obs;

  RxMap<int, RxList<CartItem>> get cart => _cart;

  @override
  void onInit() {
    super.onInit();
    _loadFromStorage();
  }

  void addToCart(int wholesalerId, ItemOut item) {
    if (!_cart.containsKey(wholesalerId)) {
      _cart[wholesalerId] = RxList<CartItem>([]);
    }
    if (_cart[wholesalerId]!.any((element) => element.item.id == item.id)) {
      _cart[wholesalerId]!
          .firstWhere((element) => element.item.id == item.id)
          .quantity++;
      _save();
      return;
    }
    _cart[wholesalerId]!.add(CartItem(item: item, quantity: 1.obs));
    _save();
  }

  void removeFromCart(int wholesalerId, ItemOut item) {
    if (_cart.containsKey(wholesalerId)) {
      _cart[wholesalerId]!
          .firstWhere((element) => element.item.id == item.id)
          .quantity--;
    }
    _cart[wholesalerId]!.removeWhere((element) => element.quantity.value == 0);
    _cart.removeWhere((key, value) => value.isEmpty);
    _save();
  }

  void removeItemFromCart(int wholesalerId, int itemId) {
    if (_cart.containsKey(wholesalerId)) {
      _cart[wholesalerId]!.removeWhere((element) => element.item.id == itemId);
      if (_cart[wholesalerId]!.isEmpty) {
        _cart.remove(wholesalerId);
      }
    }
    _save();
  }

  void clearCart(int wholesalerId) {
    if (_cart.containsKey(wholesalerId)) {
      _cart[wholesalerId]!.clear();
      _cart.remove(wholesalerId);
    }
    _save();
  }

  double getCartTotalPrice(int wholesalerId) {
    if (!_cart.containsKey(wholesalerId)) {
      return 0.0;
    }
    return _cart[wholesalerId]!.fold(
        0.0, (sum, item) => sum + (item.item.basePrice * item.quantity.value));
  }

  int getCartItemCount(int wholesalerId) {
    if (!_cart.containsKey(wholesalerId)) {
      return 0;
    }
    return _cart[wholesalerId]!.length;
  }

  double get totalPriceAcrossAllCarts {
    return _cart.keys
        .fold(0.0, (sum, wholesalerId) => sum + getCartTotalPrice(wholesalerId));
  }

  int get totalItemsAcrossAllCarts {
    return _cart.keys
        .fold(0, (sum, wholesalerId) => sum + getCartItemCount(wholesalerId));
  }

  // Persist/Restore helpers
  void _save() {
    try {
      final data = _serializeCart();
      LocalStorageService.to.cart = jsonEncode(data);
    } catch (_) {
      // ignore serialization errors
    }
  }

  void _loadFromStorage() {
    try {
      final raw = LocalStorageService.to.cart;
      if (raw == null || raw.isEmpty) return;
      final decoded = jsonDecode(raw);
      if (decoded is Map<String, dynamic>) {
        _deserializeCart(decoded);
      }
    } catch (_) {
      // ignore deserialization errors
    }
  }

  Map<String, dynamic> _serializeCart() {
    final Map<String, dynamic> result = {};
    for (final entry in _cart.entries) {
      result[entry.key.toString()] = entry.value
          .map((ci) => {
                'item': ci.item.toJson(),
                'quantity': ci.quantity.value,
              })
          .toList();
    }
    return result;
  }

  void _deserializeCart(Map<String, dynamic> data) {
    _cart.clear();
    data.forEach((key, value) {
      final wholesalerId = int.tryParse(key);
      if (wholesalerId == null) return;
      final list = RxList<CartItem>([]);
      if (value is List) {
        for (final e in value) {
          if (e is Map<String, dynamic>) {
            final itemJson = e['item'];
            final qty = e['quantity'];
            if (itemJson is Map<String, dynamic> && qty is num) {
              list.add(CartItem(
                item: ItemOut.fromJson(itemJson),
                quantity: qty.toInt().obs,
              ));
            }
          }
        }
      }
      if (list.isNotEmpty) {
        _cart[wholesalerId] = list;
      }
    });
  }
}
