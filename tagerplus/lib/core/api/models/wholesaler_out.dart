// Generated model class for WholesalerOut
// This file is auto-generated. Do not edit manually.

/// Simplified wholesaler schema for order responses
class WholesalerOut {
  final int id;

  final String? logo;

  final String title;

  const WholesalerOut({required this.id, this.logo, required this.title});

  factory WholesalerOut.fromJson(Map<String, dynamic> json) {
    return WholesalerOut(
      id: json['id'] as int? ?? 0,
      logo: json['logo'] as String?,
      title: json['title']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'logo': logo, 'title': title};
  }

  @override
  String toString() {
    return 'WholesalerOut(id: $id, logo: $logo, title: $title)';
  }

  @override
  int get hashCode {
    return id.hashCode ^ logo.hashCode ^ title.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! WholesalerOut) return false;
    return id == other.id && logo == other.logo && title == other.title;
  }
}
