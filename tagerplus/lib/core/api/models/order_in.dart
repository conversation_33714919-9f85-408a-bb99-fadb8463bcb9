// Generated model class for OrderIn
// This file is auto-generated. Do not edit manually.

import 'models.dart';

/// Schema for creating a new order
class OrderIn {
  final int wholesalerId;

  final int storeId;

  final List<OrderItemIn> items;

  final String? deliverAt;

  const OrderIn({
    required this.wholesalerId,
    required this.storeId,
    required this.items,
    this.deliverAt,
  });

  factory OrderIn.fromJson(Map<String, dynamic> json) {
    return OrderIn(
      wholesalerId: json['wholesaler_id'] as int? ?? 0,
      storeId: json['store_id'] as int? ?? 0,
      items:
          (json['items'] as List<dynamic>?)
              ?.map(
                (item) => item != null
                    ? OrderItemIn.fromJson(item as Map<String, dynamic>)
                    : OrderItemIn.fromJson({}),
              )
              .where((item) => item != null)
              .toList() ??
          [],
      deliverAt: json['deliver_at'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'wholesaler_id': wholesalerId,
      'store_id': storeId,
      'items': items.map((item) => item.toJson()).toList(),
      'deliver_at': deliverAt,
    };
  }

  @override
  String toString() {
    return 'OrderIn(wholesaler_id: $wholesalerId, store_id: $storeId, items: $items, deliver_at: $deliverAt)';
  }

  @override
  int get hashCode {
    return wholesalerId.hashCode ^
        storeId.hashCode ^
        items.hashCode ^
        deliverAt.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! OrderIn) return false;
    return wholesalerId == other.wholesalerId &&
        storeId == other.storeId &&
        items == other.items &&
        deliverAt == other.deliverAt;
  }
}
