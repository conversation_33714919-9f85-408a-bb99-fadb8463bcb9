// Generated model class for PaginatedOrdersOut
// This file is auto-generated. Do not edit manually.

import 'models.dart';

/// Paginated orders response schema
class PaginatedOrdersOut {
  final List<OrderDetailOut> orders;

  final OrderStatsOut stats;

  final int totalCount;

  final int page;

  final int pageSize;

  final int totalPages;

  final bool hasNext;

  final bool hasPrevious;

  const PaginatedOrdersOut({
    required this.orders,
    required this.stats,
    required this.totalCount,
    required this.page,
    required this.pageSize,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrevious,
  });

  factory PaginatedOrdersOut.fromJson(Map<String, dynamic> json) {
    return PaginatedOrdersOut(
      orders:
          (json['orders'] as List<dynamic>?)
              ?.map(
                (item) => item != null
                    ? OrderDetailOut.fromJson(item as Map<String, dynamic>)
                    : OrderDetailOut.fromJson({}),
              )
              .where((item) => item != null)
              .toList() ??
          [],
      stats: OrderStatsOut.fromJson(
        json['stats'] as Map<String, dynamic>? ?? {},
      ),
      totalCount: json['total_count'] as int? ?? 0,
      page: json['page'] as int? ?? 0,
      pageSize: json['page_size'] as int? ?? 0,
      totalPages: json['total_pages'] as int? ?? 0,
      hasNext: json['has_next'] as bool? ?? false,
      hasPrevious: json['has_previous'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'orders': orders.map((item) => item.toJson()).toList(),
      'stats': stats.toJson(),
      'total_count': totalCount,
      'page': page,
      'page_size': pageSize,
      'total_pages': totalPages,
      'has_next': hasNext,
      'has_previous': hasPrevious,
    };
  }

  @override
  String toString() {
    return 'PaginatedOrdersOut(orders: $orders, stats: $stats, total_count: $totalCount, page: $page, page_size: $pageSize, total_pages: $totalPages, has_next: $hasNext, has_previous: $hasPrevious)';
  }

  @override
  int get hashCode {
    return orders.hashCode ^
        stats.hashCode ^
        totalCount.hashCode ^
        page.hashCode ^
        pageSize.hashCode ^
        totalPages.hashCode ^
        hasNext.hashCode ^
        hasPrevious.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! PaginatedOrdersOut) return false;
    return orders == other.orders &&
        stats == other.stats &&
        totalCount == other.totalCount &&
        page == other.page &&
        pageSize == other.pageSize &&
        totalPages == other.totalPages &&
        hasNext == other.hasNext &&
        hasPrevious == other.hasPrevious;
  }
}
