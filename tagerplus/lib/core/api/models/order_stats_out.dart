// Generated model class for OrderStatsOut
// This file is auto-generated. Do not edit manually.

/// Order statistics schema
class OrderStatsOut {
  final int totalOrders;

  final int pendingOrders;

  final int completedOrders;

  const OrderStatsOut({
    required this.totalOrders,
    required this.pendingOrders,
    required this.completedOrders,
  });

  factory OrderStatsOut.fromJson(Map<String, dynamic> json) {
    return OrderStatsOut(
      totalOrders: json['total_orders'] as int? ?? 0,
      pendingOrders: json['pending_orders'] as int? ?? 0,
      completedOrders: json['completed_orders'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_orders': totalOrders,
      'pending_orders': pendingOrders,
      'completed_orders': completedOrders,
    };
  }

  @override
  String toString() {
    return 'OrderStatsOut(total_orders: $totalOrders, pending_orders: $pendingOrders, completed_orders: $completedOrders)';
  }

  @override
  int get hashCode {
    return totalOrders.hashCode ^
        pendingOrders.hashCode ^
        completedOrders.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! OrderStatsOut) return false;
    return totalOrders == other.totalOrders &&
        pendingOrders == other.pendingOrders &&
        completedOrders == other.completedOrders;
  }
}
