// Generated model class for RegisterResponse
// This file is auto-generated. Do not edit manually.

class RegisterResponse {
  final bool success;

  final int userId;

  final String phone;

  final String message;

  final String token;

  const RegisterResponse({
    required this.success,
    required this.userId,
    required this.phone,
    required this.message,
    required this.token,
  });

  factory RegisterResponse.fromJson(Map<String, dynamic> json) {
    return RegisterResponse(
      success: json['success'] as bool? ?? false,
      userId: json['user_id'] as int? ?? 0,
      phone: json['phone']?.toString() ?? '',
      message: json['message']?.toString() ?? '',
      token: json['token']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'user_id': userId,
      'phone': phone,
      'message': message,
      'token': token,
    };
  }

  @override
  String toString() {
    return 'RegisterResponse(success: $success, user_id: $userId, phone: $phone, message: $message, token: $token)';
  }

  @override
  int get hashCode {
    return success.hashCode ^
        userId.hashCode ^
        phone.hashCode ^
        message.hashCode ^
        token.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! RegisterResponse) return false;
    return success == other.success &&
        userId == other.userId &&
        phone == other.phone &&
        message == other.message &&
        token == other.token;
  }
}
