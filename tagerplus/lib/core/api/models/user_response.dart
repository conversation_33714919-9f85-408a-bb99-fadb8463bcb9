// Generated model class for UserResponse
// This file is auto-generated. Do not edit manually.

/// Response schema for user data
class UserResponse {
  final int id;

  final String username;

  final String? email;

  final String phone;

  final bool phoneVerified;

  final String? firstName;

  final String? lastName;

  final bool isActive;

  final String dateJoined;

  final int? wholesalerId;

  const UserResponse({
    required this.id,
    required this.username,
    this.email,
    required this.phone,
    required this.phoneVerified,
    this.firstName,
    this.lastName,
    required this.isActive,
    required this.dateJoined,
    this.wholesalerId,
  });

  factory UserResponse.fromJson(Map<String, dynamic> json) {
    return UserResponse(
      id: json['id'] as int? ?? 0,
      username: json['username']?.toString() ?? '',
      email: json['email'] as String?,
      phone: json['phone']?.toString() ?? '',
      phoneVerified: json['phone_verified'] as bool? ?? false,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      isActive: json['is_active'] as bool? ?? false,
      dateJoined: json['date_joined']?.toString() ?? '',
      wholesalerId: json['wholesaler_id'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'phone': phone,
      'phone_verified': phoneVerified,
      'first_name': firstName,
      'last_name': lastName,
      'is_active': isActive,
      'date_joined': dateJoined,
      'wholesaler_id': wholesalerId,
    };
  }

  @override
  String toString() {
    return 'UserResponse(id: $id, username: $username, email: $email, phone: $phone, phone_verified: $phoneVerified, first_name: $firstName, last_name: $lastName, is_active: $isActive, date_joined: $dateJoined, wholesaler_id: $wholesalerId)';
  }

  @override
  int get hashCode {
    return id.hashCode ^
        username.hashCode ^
        email.hashCode ^
        phone.hashCode ^
        phoneVerified.hashCode ^
        firstName.hashCode ^
        lastName.hashCode ^
        isActive.hashCode ^
        dateJoined.hashCode ^
        wholesalerId.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! UserResponse) return false;
    return id == other.id &&
        username == other.username &&
        email == other.email &&
        phone == other.phone &&
        phoneVerified == other.phoneVerified &&
        firstName == other.firstName &&
        lastName == other.lastName &&
        isActive == other.isActive &&
        dateJoined == other.dateJoined &&
        wholesalerId == other.wholesalerId;
  }
}
