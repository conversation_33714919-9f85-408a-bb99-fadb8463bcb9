// Generated model class for CompanySchema
// This file is auto-generated. Do not edit manually.

class CompanySchema {
  final int id;

  final String name;

  final String title;

  final String slug;

  const CompanySchema({
    required this.id,
    required this.name,
    required this.title,
    required this.slug,
  });

  factory CompanySchema.fromJson(Map<String, dynamic> json) {
    return CompanySchema(
      id: json['id'] as int? ?? 0,
      name: json['name']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      slug: json['slug']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'name': name, 'title': title, 'slug': slug};
  }

  @override
  String toString() {
    return 'CompanySchema(id: $id, name: $name, title: $title, slug: $slug)';
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ title.hashCode ^ slug.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CompanySchema) return false;
    return id == other.id &&
        name == other.name &&
        title == other.title &&
        slug == other.slug;
  }
}
