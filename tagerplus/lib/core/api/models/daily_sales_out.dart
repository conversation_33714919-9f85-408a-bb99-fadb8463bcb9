// Generated model class for DailySalesOut
// This file is auto-generated. Do not edit manually.

/// Daily sales data schema
class DailySalesOut {
  final String date;

  final double sales;

  const DailySalesOut({required this.date, required this.sales});

  factory DailySalesOut.fromJson(Map<String, dynamic> json) {
    return DailySalesOut(
      date: json['date']?.toString() ?? '',
      sales: (json['sales'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {'date': date, 'sales': sales};
  }

  @override
  String toString() {
    return 'DailySalesOut(date: $date, sales: $sales)';
  }

  @override
  int get hashCode {
    return date.hashCode ^ sales.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! DailySalesOut) return false;
    return date == other.date && sales == other.sales;
  }
}
