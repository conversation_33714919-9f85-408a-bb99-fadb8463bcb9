// Generated model class for WholesalerSchema
// This file is auto-generated. Do not edit manually.

class WholesalerSchema {
  final int id;

  final String title;

  final String username;

  final String? backgroundImage;

  final String? logo;

  const WholesalerSchema({
    required this.id,
    required this.title,
    required this.username,
    required this.backgroundImage,
    required this.logo,
  });

  factory WholesalerSchema.fromJson(Map<String, dynamic> json) {
    return WholesalerSchema(
      id: json['id'] as int? ?? 0,
      title: json['title']?.toString() ?? '',
      username: json['username']?.toString() ?? '',
      backgroundImage: json['background_image'] as String?,
      logo: json['logo'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'username': username,
      'background_image': backgroundImage,
      'logo': logo,
    };
  }

  @override
  String toString() {
    return 'WholesalerSchema(id: $id, title: $title, username: $username, background_image: $backgroundImage, logo: $logo)';
  }

  @override
  int get hashCode {
    return id.hashCode ^
        title.hashCode ^
        username.hashCode ^
        backgroundImage.hashCode ^
        logo.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! WholesalerSchema) return false;
    return id == other.id &&
        title == other.title &&
        username == other.username &&
        backgroundImage == other.backgroundImage &&
        logo == other.logo;
  }
}
