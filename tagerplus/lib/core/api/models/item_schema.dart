// Generated model class for ItemSchema
// This file is auto-generated. Do not edit manually.

import 'models.dart';

class ItemSchema {
  final int id;

  final double basePrice;

  final int inventoryCount;

  final int minimumOrderQuantity;

  final int? maximumOrderQuantity;

  final ItemProductSchema? product;

  final int wholesalerId;

  final WholesalerSchema? wholesaler;

  const ItemSchema({
    required this.id,
    required this.basePrice,
    required this.inventoryCount,
    required this.minimumOrderQuantity,
    required this.maximumOrderQuantity,
    this.product,
    required this.wholesalerId,
    this.wholesaler,
  });

  factory ItemSchema.fromJson(Map<String, dynamic> json) {
    return ItemSchema(
      id: json['id'] as int? ?? 0,
      basePrice: (json['base_price'] as num?)?.toDouble() ?? 0.0,
      inventoryCount: json['inventory_count'] as int? ?? 0,
      minimumOrderQuantity: json['minimum_order_quantity'] as int? ?? 0,
      maximumOrderQuantity: json['maximum_order_quantity'] as int?,
      product: json['product'] != null
          ? ItemProductSchema.fromJson(json['product'] as Map<String, dynamic>)
          : null,
      wholesalerId: json['wholesaler_id'] as int? ?? 0,
      wholesaler: json['wholesaler'] != null
          ? WholesalerSchema.fromJson(
              json['wholesaler'] as Map<String, dynamic>,
            )
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'base_price': basePrice,
      'inventory_count': inventoryCount,
      'minimum_order_quantity': minimumOrderQuantity,
      'maximum_order_quantity': maximumOrderQuantity,
      'product': product?.toJson(),
      'wholesaler_id': wholesalerId,
      'wholesaler': wholesaler?.toJson(),
    };
  }

  @override
  String toString() {
    return 'ItemSchema(id: $id, base_price: $basePrice, inventory_count: $inventoryCount, minimum_order_quantity: $minimumOrderQuantity, maximum_order_quantity: $maximumOrderQuantity, product: $product, wholesaler_id: $wholesalerId, wholesaler: $wholesaler)';
  }

  @override
  int get hashCode {
    return id.hashCode ^
        basePrice.hashCode ^
        inventoryCount.hashCode ^
        minimumOrderQuantity.hashCode ^
        maximumOrderQuantity.hashCode ^
        product.hashCode ^
        wholesalerId.hashCode ^
        wholesaler.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ItemSchema) return false;
    return id == other.id &&
        basePrice == other.basePrice &&
        inventoryCount == other.inventoryCount &&
        minimumOrderQuantity == other.minimumOrderQuantity &&
        maximumOrderQuantity == other.maximumOrderQuantity &&
        product == other.product &&
        wholesalerId == other.wholesalerId &&
        wholesaler == other.wholesaler;
  }
}
