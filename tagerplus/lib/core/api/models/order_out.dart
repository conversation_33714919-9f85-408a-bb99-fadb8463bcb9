// Generated model class for OrderOut
// This file is auto-generated. Do not edit manually.

import 'models.dart';

class OrderOut {
  final int id;

  final WholesalerOut wholesaler;

  final StoreOrderOut store;

  final List<OrderItemOut2> orderItems;

  final double totalPrice;

  final double fees;

  final double productsTotalPrice;

  final int productsTotalQuantity;

  final String status;

  final String? statusReason;

  final double? finalCompletedPrice;

  final String? deliverAt;

  final String? completedAt;

  final String createdAt;

  const OrderOut({
    required this.id,
    required this.wholesaler,
    required this.store,
    required this.orderItems,
    required this.totalPrice,
    required this.fees,
    required this.productsTotalPrice,
    required this.productsTotalQuantity,
    required this.status,
    this.statusReason,
    this.finalCompletedPrice,
    this.deliverAt,
    this.completedAt,
    required this.createdAt,
  });

  factory OrderOut.fromJson(Map<String, dynamic> json) {
    return OrderOut(
      id: json['id'] as int? ?? 0,
      wholesaler: WholesalerOut.fromJson(
        json['wholesaler'] as Map<String, dynamic>? ?? {},
      ),
      store: StoreOrderOut.fromJson(
        json['store'] as Map<String, dynamic>? ?? {},
      ),
      orderItems:
          (json['order_items'] as List<dynamic>?)
              ?.map(
                (item) => item != null
                    ? OrderItemOut2.fromJson(item as Map<String, dynamic>)
                    : OrderItemOut2.fromJson({}),
              )
              .where((item) => item != null)
              .toList() ??
          [],
      totalPrice: (json['total_price'] as num?)?.toDouble() ?? 0.0,
      fees: (json['fees'] as num?)?.toDouble() ?? 0.0,
      productsTotalPrice:
          (json['products_total_price'] as num?)?.toDouble() ?? 0.0,
      productsTotalQuantity: json['products_total_quantity'] as int? ?? 0,
      status: json['status']?.toString() ?? '',
      statusReason: json['status_reason'] as String?,
      finalCompletedPrice: (json['final_completed_price'] as num?)?.toDouble(),
      deliverAt: json['deliver_at'] as String?,
      completedAt: json['completed_at'] as String?,
      createdAt: json['created_at']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'wholesaler': wholesaler.toJson(),
      'store': store.toJson(),
      'order_items': orderItems.map((item) => item.toJson()).toList(),
      'total_price': totalPrice,
      'fees': fees,
      'products_total_price': productsTotalPrice,
      'products_total_quantity': productsTotalQuantity,
      'status': status,
      'status_reason': statusReason,
      'final_completed_price': finalCompletedPrice,
      'deliver_at': deliverAt,
      'completed_at': completedAt,
      'created_at': createdAt,
    };
  }

  @override
  String toString() {
    return 'OrderOut(id: $id, wholesaler: $wholesaler, store: $store, order_items: $orderItems, total_price: $totalPrice, fees: $fees, products_total_price: $productsTotalPrice, products_total_quantity: $productsTotalQuantity, status: $status, status_reason: $statusReason, final_completed_price: $finalCompletedPrice, deliver_at: $deliverAt, completed_at: $completedAt, created_at: $createdAt)';
  }

  @override
  int get hashCode {
    return id.hashCode ^
        wholesaler.hashCode ^
        store.hashCode ^
        orderItems.hashCode ^
        totalPrice.hashCode ^
        fees.hashCode ^
        productsTotalPrice.hashCode ^
        productsTotalQuantity.hashCode ^
        status.hashCode ^
        statusReason.hashCode ^
        finalCompletedPrice.hashCode ^
        deliverAt.hashCode ^
        completedAt.hashCode ^
        createdAt.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! OrderOut) return false;
    return id == other.id &&
        wholesaler == other.wholesaler &&
        store == other.store &&
        orderItems == other.orderItems &&
        totalPrice == other.totalPrice &&
        fees == other.fees &&
        productsTotalPrice == other.productsTotalPrice &&
        productsTotalQuantity == other.productsTotalQuantity &&
        status == other.status &&
        statusReason == other.statusReason &&
        finalCompletedPrice == other.finalCompletedPrice &&
        deliverAt == other.deliverAt &&
        completedAt == other.completedAt &&
        createdAt == other.createdAt;
  }
}
