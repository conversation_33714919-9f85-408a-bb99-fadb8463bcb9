// Generated model class for RegisterRequest
// This file is auto-generated. Do not edit manually.

class RegisterRequest {
  final String name;

  final String password;

  final String phone;

  final String? email;

  const RegisterRequest({
    required this.name,
    required this.password,
    required this.phone,
    this.email,
  });

  factory RegisterRequest.fromJson(Map<String, dynamic> json) {
    return RegisterRequest(
      name: json['name']?.toString() ?? '',
      password: json['password']?.toString() ?? '',
      phone: json['phone']?.toString() ?? '',
      email: json['email'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {'name': name, 'password': password, 'phone': phone, 'email': email};
  }

  @override
  String toString() {
    return 'RegisterRequest(name: $name, password: $password, phone: $phone, email: $email)';
  }

  @override
  int get hashCode {
    return name.hashCode ^ password.hashCode ^ phone.hashCode ^ email.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! RegisterRequest) return false;
    return name == other.name &&
        password == other.password &&
        phone == other.phone &&
        email == other.email;
  }
}
