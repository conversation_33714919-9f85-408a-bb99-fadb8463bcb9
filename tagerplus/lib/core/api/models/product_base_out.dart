// Generated model class for ProductBaseOut
// This file is auto-generated. Do not edit manually.

/// Schema for product base output
class ProductBaseOut {
  final int id;

  final String name;

  final String title;

  final String barcode;

  final String slug;

  final String description;

  final String? image;

  final String unit;

  final double unitCount;

  const ProductBaseOut({
    required this.id,
    required this.name,
    required this.title,
    required this.barcode,
    required this.slug,
    required this.description,
    this.image,
    required this.unit,
    required this.unitCount,
  });

  factory ProductBaseOut.fromJson(Map<String, dynamic> json) {
    return ProductBaseOut(
      id: json['id'] as int? ?? 0,
      name: json['name']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      barcode: json['barcode']?.toString() ?? '',
      slug: json['slug']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      image: json['image'] as String?,
      unit: json['unit']?.toString() ?? '',
      unitCount: (json['unit_count'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'title': title,
      'barcode': barcode,
      'slug': slug,
      'description': description,
      'image': image,
      'unit': unit,
      'unit_count': unitCount,
    };
  }

  @override
  String toString() {
    return 'ProductBaseOut(id: $id, name: $name, title: $title, barcode: $barcode, slug: $slug, description: $description, image: $image, unit: $unit, unit_count: $unitCount)';
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        title.hashCode ^
        barcode.hashCode ^
        slug.hashCode ^
        description.hashCode ^
        image.hashCode ^
        unit.hashCode ^
        unitCount.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ProductBaseOut) return false;
    return id == other.id &&
        name == other.name &&
        title == other.title &&
        barcode == other.barcode &&
        slug == other.slug &&
        description == other.description &&
        image == other.image &&
        unit == other.unit &&
        unitCount == other.unitCount;
  }
}
