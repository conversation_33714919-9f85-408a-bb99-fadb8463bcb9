// Generated model class for DashboardOut
// This file is auto-generated. Do not edit manually.

import 'models.dart';

/// Complete dashboard response schema
class DashboardOut {
  final DashboardStatsOut stats;

  final List<RecentOrderOut> recentOrders;

  final List<DailySalesOut> dailySales;

  const DashboardOut({
    required this.stats,
    required this.recentOrders,
    required this.dailySales,
  });

  factory DashboardOut.fromJson(Map<String, dynamic> json) {
    return DashboardOut(
      stats: DashboardStatsOut.fromJson(
        json['stats'] as Map<String, dynamic>? ?? {},
      ),
      recentOrders:
          (json['recent_orders'] as List<dynamic>?)
              ?.map(
                (item) => item != null
                    ? RecentOrderOut.fromJson(item as Map<String, dynamic>)
                    : RecentOrderOut.fromJson({}),
              )
              .where((item) => item != null)
              .toList() ??
          [],
      dailySales:
          (json['daily_sales'] as List<dynamic>?)
              ?.map(
                (item) => item != null
                    ? DailySalesOut.fromJson(item as Map<String, dynamic>)
                    : DailySalesOut.fromJson({}),
              )
              .where((item) => item != null)
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'stats': stats.toJson(),
      'recent_orders': recentOrders.map((item) => item.toJson()).toList(),
      'daily_sales': dailySales.map((item) => item.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'DashboardOut(stats: $stats, recent_orders: $recentOrders, daily_sales: $dailySales)';
  }

  @override
  int get hashCode {
    return stats.hashCode ^ recentOrders.hashCode ^ dailySales.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! DashboardOut) return false;
    return stats == other.stats &&
        recentOrders == other.recentOrders &&
        dailySales == other.dailySales;
  }
}
