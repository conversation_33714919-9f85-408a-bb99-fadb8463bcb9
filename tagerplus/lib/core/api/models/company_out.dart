// Generated model class for CompanyOut
// This file is auto-generated. Do not edit manually.

/// Company information schema
class CompanyOut {
  final int id;

  final String name;

  final String title;

  const CompanyOut({required this.id, required this.name, required this.title});

  factory CompanyOut.fromJson(Map<String, dynamic> json) {
    return CompanyOut(
      id: json['id'] as int? ?? 0,
      name: json['name']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'name': name, 'title': title};
  }

  @override
  String toString() {
    return 'CompanyOut(id: $id, name: $name, title: $title)';
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ title.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CompanyOut) return false;
    return id == other.id && name == other.name && title == other.title;
  }
}
