// Generated model class for ProductWithPricing
// This file is auto-generated. Do not edit manually.

import 'models.dart';

class ProductWithPricing {
  final int id;

  final String name;

  final String title;

  final String barcode;

  final String slug;

  final String description;

  final String? imageUrl;

  final int? companyId;

  final int? categoryId;

  final CompanyOut? company;

  final CategoryOut? category;

  final String unit;

  final double unitCount;

  final double? basePrice;

  final double? otherPrice;

  const ProductWithPricing({
    required this.id,
    required this.name,
    required this.title,
    required this.barcode,
    required this.slug,
    required this.description,
    this.imageUrl,
    this.companyId,
    this.categoryId,
    this.company,
    this.category,
    required this.unit,
    required this.unitCount,
    this.basePrice,
    this.otherPrice,
  });

  factory ProductWithPricing.fromJson(Map<String, dynamic> json) {
    return ProductWithPricing(
      id: json['id'] as int? ?? 0,
      name: json['name']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      barcode: json['barcode']?.toString() ?? '',
      slug: json['slug']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      imageUrl: json['image_url'] as String?,
      companyId: json['company_id'] as int?,
      categoryId: json['category_id'] as int?,
      company: json['company'] != null
          ? CompanyOut.fromJson(json['company'] as Map<String, dynamic>)
          : null,
      category: json['category'] != null
          ? CategoryOut.fromJson(json['category'] as Map<String, dynamic>)
          : null,
      unit: json['unit']?.toString() ?? '',
      unitCount: (json['unit_count'] as num?)?.toDouble() ?? 0.0,
      basePrice: (json['base_price'] as num?)?.toDouble(),
      otherPrice: (json['other_price'] as num?)?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'title': title,
      'barcode': barcode,
      'slug': slug,
      'description': description,
      'image_url': imageUrl,
      'company_id': companyId,
      'category_id': categoryId,
      'company': company?.toJson(),
      'category': category?.toJson(),
      'unit': unit,
      'unit_count': unitCount,
      'base_price': basePrice,
      'other_price': otherPrice,
    };
  }

  @override
  String toString() {
    return 'ProductWithPricing(id: $id, name: $name, title: $title, barcode: $barcode, slug: $slug, description: $description, image_url: $imageUrl, company_id: $companyId, category_id: $categoryId, company: $company, category: $category, unit: $unit, unit_count: $unitCount, base_price: $basePrice, other_price: $otherPrice)';
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        title.hashCode ^
        barcode.hashCode ^
        slug.hashCode ^
        description.hashCode ^
        imageUrl.hashCode ^
        companyId.hashCode ^
        categoryId.hashCode ^
        company.hashCode ^
        category.hashCode ^
        unit.hashCode ^
        unitCount.hashCode ^
        basePrice.hashCode ^
        otherPrice.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ProductWithPricing) return false;
    return id == other.id &&
        name == other.name &&
        title == other.title &&
        barcode == other.barcode &&
        slug == other.slug &&
        description == other.description &&
        imageUrl == other.imageUrl &&
        companyId == other.companyId &&
        categoryId == other.categoryId &&
        company == other.company &&
        category == other.category &&
        unit == other.unit &&
        unitCount == other.unitCount &&
        basePrice == other.basePrice &&
        otherPrice == other.otherPrice;
  }
}
