// Generated model class for RegionOut
// This file is auto-generated. Do not edit manually.

/// Schema for region output
class RegionOut {
  final int id;

  final String name;

  final String type;

  final String slug;

  const RegionOut({
    required this.id,
    required this.name,
    required this.type,
    required this.slug,
  });

  factory RegionOut.fromJson(Map<String, dynamic> json) {
    return RegionOut(
      id: json['id'] as int? ?? 0,
      name: json['name']?.toString() ?? '',
      type: json['type']?.toString() ?? '',
      slug: json['slug']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'name': name, 'type': type, 'slug': slug};
  }

  @override
  String toString() {
    return 'RegionOut(id: $id, name: $name, type: $type, slug: $slug)';
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ type.hashCode ^ slug.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! RegionOut) return false;
    return id == other.id &&
        name == other.name &&
        type == other.type &&
        slug == other.slug;
  }
}
