// Generated model class for CategoryOut
// This file is auto-generated. Do not edit manually.

/// Category information schema
class CategoryOut {
  final int id;

  final String name;

  final String title;

  const CategoryOut({
    required this.id,
    required this.name,
    required this.title,
  });

  factory CategoryOut.fromJson(Map<String, dynamic> json) {
    return CategoryOut(
      id: json['id'] as int? ?? 0,
      name: json['name']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'name': name, 'title': title};
  }

  @override
  String toString() {
    return 'CategoryOut(id: $id, name: $name, title: $title)';
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ title.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CategoryOut) return false;
    return id == other.id && name == other.name && title == other.title;
  }
}
