// Generated model class for ItemUpdateIn
// This file is auto-generated. Do not edit manually.

/// Update item request schema
class ItemUpdateIn {
  final double? basePrice;

  final int? inventoryCount;

  final int? minimumOrderQuantity;

  final int? maximumOrderQuantity;

  const ItemUpdateIn({
    this.basePrice,
    this.inventoryCount,
    this.minimumOrderQuantity,
    this.maximumOrderQuantity,
  });

  factory ItemUpdateIn.fromJson(Map<String, dynamic> json) {
    return ItemUpdateIn(
      basePrice: (json['base_price'] as num?)?.toDouble(),
      inventoryCount: json['inventory_count'] as int?,
      minimumOrderQuantity: json['minimum_order_quantity'] as int?,
      maximumOrderQuantity: json['maximum_order_quantity'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'base_price': basePrice,
      'inventory_count': inventoryCount,
      'minimum_order_quantity': minimumOrderQuantity,
      'maximum_order_quantity': maximumOrderQuantity,
    };
  }

  @override
  String toString() {
    return 'ItemUpdateIn(base_price: $basePrice, inventory_count: $inventoryCount, minimum_order_quantity: $minimumOrderQuantity, maximum_order_quantity: $maximumOrderQuantity)';
  }

  @override
  int get hashCode {
    return basePrice.hashCode ^
        inventoryCount.hashCode ^
        minimumOrderQuantity.hashCode ^
        maximumOrderQuantity.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ItemUpdateIn) return false;
    return basePrice == other.basePrice &&
        inventoryCount == other.inventoryCount &&
        minimumOrderQuantity == other.minimumOrderQuantity &&
        maximumOrderQuantity == other.maximumOrderQuantity;
  }
}
