// Generated model class for PaginatedProductsForBulkOut
// This file is auto-generated. Do not edit manually.

import 'models.dart';

/// Paginated products response for bulk operations
class PaginatedProductsForBulkOut {
  final List<ProductForBulkOut> products;

  final List<CompanyOut> companies;

  final List<CategoryOut> categories;

  final int totalCount;

  final int page;

  final int pageSize;

  final int totalPages;

  final bool hasNext;

  final bool hasPrevious;

  const PaginatedProductsForBulkOut({
    required this.products,
    required this.companies,
    required this.categories,
    required this.totalCount,
    required this.page,
    required this.pageSize,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrevious,
  });

  factory PaginatedProductsForBulkOut.fromJson(Map<String, dynamic> json) {
    return PaginatedProductsForBulkOut(
      products:
          (json['products'] as List<dynamic>?)
              ?.map(
                (item) => item != null
                    ? ProductForBulkOut.fromJson(item as Map<String, dynamic>)
                    : ProductForBulkOut.fromJson({}),
              )
              .where((item) => item != null)
              .toList() ??
          [],
      companies:
          (json['companies'] as List<dynamic>?)
              ?.map(
                (item) => item != null
                    ? CompanyOut.fromJson(item as Map<String, dynamic>)
                    : CompanyOut.fromJson({}),
              )
              .where((item) => item != null)
              .toList() ??
          [],
      categories:
          (json['categories'] as List<dynamic>?)
              ?.map(
                (item) => item != null
                    ? CategoryOut.fromJson(item as Map<String, dynamic>)
                    : CategoryOut.fromJson({}),
              )
              .where((item) => item != null)
              .toList() ??
          [],
      totalCount: json['total_count'] as int? ?? 0,
      page: json['page'] as int? ?? 0,
      pageSize: json['page_size'] as int? ?? 0,
      totalPages: json['total_pages'] as int? ?? 0,
      hasNext: json['has_next'] as bool? ?? false,
      hasPrevious: json['has_previous'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'products': products.map((item) => item.toJson()).toList(),
      'companies': companies.map((item) => item.toJson()).toList(),
      'categories': categories.map((item) => item.toJson()).toList(),
      'total_count': totalCount,
      'page': page,
      'page_size': pageSize,
      'total_pages': totalPages,
      'has_next': hasNext,
      'has_previous': hasPrevious,
    };
  }

  @override
  String toString() {
    return 'PaginatedProductsForBulkOut(products: $products, companies: $companies, categories: $categories, total_count: $totalCount, page: $page, page_size: $pageSize, total_pages: $totalPages, has_next: $hasNext, has_previous: $hasPrevious)';
  }

  @override
  int get hashCode {
    return products.hashCode ^
        companies.hashCode ^
        categories.hashCode ^
        totalCount.hashCode ^
        page.hashCode ^
        pageSize.hashCode ^
        totalPages.hashCode ^
        hasNext.hashCode ^
        hasPrevious.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! PaginatedProductsForBulkOut) return false;
    return products == other.products &&
        companies == other.companies &&
        categories == other.categories &&
        totalCount == other.totalCount &&
        page == other.page &&
        pageSize == other.pageSize &&
        totalPages == other.totalPages &&
        hasNext == other.hasNext &&
        hasPrevious == other.hasPrevious;
  }
}
