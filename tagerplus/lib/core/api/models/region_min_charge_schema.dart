// Generated model class for RegionMinChargeSchema
// This file is auto-generated. Do not edit manually.

class RegionMinChargeSchema {
  final int wholesalerId;

  final int regionId;

  final double minCharge;

  final int minItems;

  const RegionMinChargeSchema({
    required this.wholesalerId,
    required this.regionId,
    required this.minCharge,
    required this.minItems,
  });

  factory RegionMinChargeSchema.fromJson(Map<String, dynamic> json) {
    return RegionMinChargeSchema(
      wholesalerId: json['wholesaler_id'] as int? ?? 0,
      regionId: json['region_id'] as int? ?? 0,
      minCharge: (json['min_charge'] as num?)?.toDouble() ?? 0.0,
      minItems: json['min_items'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'wholesaler_id': wholesalerId,
      'region_id': regionId,
      'min_charge': minCharge,
      'min_items': minItems,
    };
  }

  @override
  String toString() {
    return 'RegionMinChargeSchema(wholesaler_id: $wholesalerId, region_id: $regionId, min_charge: $minCharge, min_items: $minItems)';
  }

  @override
  int get hashCode {
    return wholesalerId.hashCode ^
        regionId.hashCode ^
        minCharge.hashCode ^
        minItems.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! RegionMinChargeSchema) return false;
    return wholesalerId == other.wholesalerId &&
        regionId == other.regionId &&
        minCharge == other.minCharge &&
        minItems == other.minItems;
  }
}
