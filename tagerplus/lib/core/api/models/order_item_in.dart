// Generated model class for OrderItemIn
// This file is auto-generated. Do not edit manually.

/// Schema for order items input
class OrderItemIn {
  final int itemId;

  final int quantity;

  const OrderItemIn({required this.itemId, required this.quantity});

  factory OrderItemIn.fromJson(Map<String, dynamic> json) {
    return OrderItemIn(
      itemId: json['item_id'] as int? ?? 0,
      quantity: json['quantity'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {'item_id': itemId, 'quantity': quantity};
  }

  @override
  String toString() {
    return 'OrderItemIn(item_id: $itemId, quantity: $quantity)';
  }

  @override
  int get hashCode {
    return itemId.hashCode ^ quantity.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! OrderItemIn) return false;
    return itemId == other.itemId && quantity == other.quantity;
  }
}
