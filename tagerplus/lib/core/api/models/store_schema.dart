// Generated model class for StoreSchema
// This file is auto-generated. Do not edit manually.

class StoreSchema {
  final int id;

  final int ownerId;

  final String name;

  final String description;

  final String address;

  final int? cityId;

  final int? stateId;

  final int? countryId;

  const StoreSchema({
    required this.id,
    required this.ownerId,
    required this.name,
    required this.description,
    required this.address,
    this.cityId,
    this.stateId,
    this.countryId,
  });

  factory StoreSchema.fromJson(Map<String, dynamic> json) {
    return StoreSchema(
      id: json['id'] as int? ?? 0,
      ownerId: json['owner_id'] as int? ?? 0,
      name: json['name']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      address: json['address']?.toString() ?? '',
      cityId: json['city_id'] as int?,
      stateId: json['state_id'] as int?,
      countryId: json['country_id'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'owner_id': ownerId,
      'name': name,
      'description': description,
      'address': address,
      'city_id': cityId,
      'state_id': stateId,
      'country_id': countryId,
    };
  }

  @override
  String toString() {
    return 'StoreSchema(id: $id, owner_id: $ownerId, name: $name, description: $description, address: $address, city_id: $cityId, state_id: $stateId, country_id: $countryId)';
  }

  @override
  int get hashCode {
    return id.hashCode ^
        ownerId.hashCode ^
        name.hashCode ^
        description.hashCode ^
        address.hashCode ^
        cityId.hashCode ^
        stateId.hashCode ^
        countryId.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! StoreSchema) return false;
    return id == other.id &&
        ownerId == other.ownerId &&
        name == other.name &&
        description == other.description &&
        address == other.address &&
        cityId == other.cityId &&
        stateId == other.stateId &&
        countryId == other.countryId;
  }
}
