// Generated model class for OrderDetailOut
// This file is auto-generated. Do not edit manually.

import 'models.dart';

/// Detailed order information schema
class OrderDetailOut {
  final int id;

  final StoreOut store;

  final String status;

  final double totalPrice;

  final double fees;

  final double productsTotalPrice;

  final int productsTotalQuantity;

  final String createdAt;

  final String updatedAt;

  final List<OrderItemOut> orderItems;

  const OrderDetailOut({
    required this.id,
    required this.store,
    required this.status,
    required this.totalPrice,
    required this.fees,
    required this.productsTotalPrice,
    required this.productsTotalQuantity,
    required this.createdAt,
    required this.updatedAt,
    required this.orderItems,
  });

  factory OrderDetailOut.fromJson(Map<String, dynamic> json) {
    return OrderDetailOut(
      id: json['id'] as int? ?? 0,
      store: StoreOut.fromJson(json['store'] as Map<String, dynamic>? ?? {}),
      status: json['status']?.toString() ?? '',
      totalPrice: (json['total_price'] as num?)?.toDouble() ?? 0.0,
      fees: (json['fees'] as num?)?.toDouble() ?? 0.0,
      productsTotalPrice:
          (json['products_total_price'] as num?)?.toDouble() ?? 0.0,
      productsTotalQuantity: json['products_total_quantity'] as int? ?? 0,
      createdAt: json['created_at']?.toString() ?? '',
      updatedAt: json['updated_at']?.toString() ?? '',
      orderItems:
          (json['order_items'] as List<dynamic>?)
              ?.map(
                (item) => item != null
                    ? OrderItemOut.fromJson(item as Map<String, dynamic>)
                    : OrderItemOut.fromJson({}),
              )
              .where((item) => item != null)
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'store': store.toJson(),
      'status': status,
      'total_price': totalPrice,
      'fees': fees,
      'products_total_price': productsTotalPrice,
      'products_total_quantity': productsTotalQuantity,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'order_items': orderItems.map((item) => item.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'OrderDetailOut(id: $id, store: $store, status: $status, total_price: $totalPrice, fees: $fees, products_total_price: $productsTotalPrice, products_total_quantity: $productsTotalQuantity, created_at: $createdAt, updated_at: $updatedAt, order_items: $orderItems)';
  }

  @override
  int get hashCode {
    return id.hashCode ^
        store.hashCode ^
        status.hashCode ^
        totalPrice.hashCode ^
        fees.hashCode ^
        productsTotalPrice.hashCode ^
        productsTotalQuantity.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode ^
        orderItems.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! OrderDetailOut) return false;
    return id == other.id &&
        store == other.store &&
        status == other.status &&
        totalPrice == other.totalPrice &&
        fees == other.fees &&
        productsTotalPrice == other.productsTotalPrice &&
        productsTotalQuantity == other.productsTotalQuantity &&
        createdAt == other.createdAt &&
        updatedAt == other.updatedAt &&
        orderItems == other.orderItems;
  }
}
