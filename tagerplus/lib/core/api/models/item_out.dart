// Generated model class for ItemOut
// This file is auto-generated. Do not edit manually.

import 'models.dart';

/// Item information schema
class ItemOut {
  final int id;

  final ItemProductOut product;

  final double basePrice;

  final int inventoryCount;

  final int minimumOrderQuantity;

  final int? maximumOrderQuantity;

  final String createdAt;

  final String updatedAt;

  const ItemOut({
    required this.id,
    required this.product,
    required this.basePrice,
    required this.inventoryCount,
    required this.minimumOrderQuantity,
    this.maximumOrderQuantity,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ItemOut.fromJson(Map<String, dynamic> json) {
    return ItemOut(
      id: json['id'] as int? ?? 0,
      product: ItemProductOut.fromJson(
        json['product'] as Map<String, dynamic>? ?? {},
      ),
      basePrice: (json['base_price'] as num?)?.toDouble() ?? 0.0,
      inventoryCount: json['inventory_count'] as int? ?? 0,
      minimumOrderQuantity: json['minimum_order_quantity'] as int? ?? 0,
      maximumOrderQuantity: json['maximum_order_quantity'] as int?,
      createdAt: json['created_at']?.toString() ?? '',
      updatedAt: json['updated_at']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product': product.toJson(),
      'base_price': basePrice,
      'inventory_count': inventoryCount,
      'minimum_order_quantity': minimumOrderQuantity,
      'maximum_order_quantity': maximumOrderQuantity,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  @override
  String toString() {
    return 'ItemOut(id: $id, product: $product, base_price: $basePrice, inventory_count: $inventoryCount, minimum_order_quantity: $minimumOrderQuantity, maximum_order_quantity: $maximumOrderQuantity, created_at: $createdAt, updated_at: $updatedAt)';
  }

  @override
  int get hashCode {
    return id.hashCode ^
        product.hashCode ^
        basePrice.hashCode ^
        inventoryCount.hashCode ^
        minimumOrderQuantity.hashCode ^
        maximumOrderQuantity.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ItemOut) return false;
    return id == other.id &&
        product == other.product &&
        basePrice == other.basePrice &&
        inventoryCount == other.inventoryCount &&
        minimumOrderQuantity == other.minimumOrderQuantity &&
        maximumOrderQuantity == other.maximumOrderQuantity &&
        createdAt == other.createdAt &&
        updatedAt == other.updatedAt;
  }
}
