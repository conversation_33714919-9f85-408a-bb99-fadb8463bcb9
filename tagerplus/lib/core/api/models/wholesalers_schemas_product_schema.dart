// Generated model class for wholesalers__schemas__ProductSchema
// This file is auto-generated. Do not edit manually.

class WholesalersSchemasProductschema {
  final int id;

  final String name;

  final String title;

  final String barcode;

  final String slug;

  final String description;

  final String? imageUrl;

  final int? companyId;

  final int? categoryId;

  final String unit;

  final double unitCount;

  const WholesalersSchemasProductschema({
    required this.id,
    required this.name,
    required this.title,
    required this.barcode,
    required this.slug,
    required this.description,
    this.imageUrl,
    this.companyId,
    this.categoryId,
    required this.unit,
    required this.unitCount,
  });

  factory WholesalersSchemasProductschema.fromJson(Map<String, dynamic> json) {
    return WholesalersSchemasProductschema(
      id: json['id'] as int? ?? 0,
      name: json['name']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      barcode: json['barcode']?.toString() ?? '',
      slug: json['slug']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      imageUrl: json['image_url'] as String?,
      companyId: json['company_id'] as int?,
      categoryId: json['category_id'] as int?,
      unit: json['unit']?.toString() ?? '',
      unitCount: (json['unit_count'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'title': title,
      'barcode': barcode,
      'slug': slug,
      'description': description,
      'image_url': imageUrl,
      'company_id': companyId,
      'category_id': categoryId,
      'unit': unit,
      'unit_count': unitCount,
    };
  }

  @override
  String toString() {
    return 'WholesalersSchemasProductschema(id: $id, name: $name, title: $title, barcode: $barcode, slug: $slug, description: $description, image_url: $imageUrl, company_id: $companyId, category_id: $categoryId, unit: $unit, unit_count: $unitCount)';
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        title.hashCode ^
        barcode.hashCode ^
        slug.hashCode ^
        description.hashCode ^
        imageUrl.hashCode ^
        companyId.hashCode ^
        categoryId.hashCode ^
        unit.hashCode ^
        unitCount.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! WholesalersSchemasProductschema) return false;
    return id == other.id &&
        name == other.name &&
        title == other.title &&
        barcode == other.barcode &&
        slug == other.slug &&
        description == other.description &&
        imageUrl == other.imageUrl &&
        companyId == other.companyId &&
        categoryId == other.categoryId &&
        unit == other.unit &&
        unitCount == other.unitCount;
  }
}
