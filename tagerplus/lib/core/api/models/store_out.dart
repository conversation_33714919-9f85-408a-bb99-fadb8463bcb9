// Generated model class for StoreOut
// This file is auto-generated. Do not edit manually.

/// Store information schema
class StoreOut {
  final int id;

  final String name;

  final String address;

  final String ownerUsername;

  const StoreOut({
    required this.id,
    required this.name,
    required this.address,
    required this.ownerUsername,
  });

  factory StoreOut.fromJson(Map<String, dynamic> json) {
    return StoreOut(
      id: json['id'] as int? ?? 0,
      name: json['name']?.toString() ?? '',
      address: json['address']?.toString() ?? '',
      ownerUsername: json['owner_username']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'owner_username': ownerUsername,
    };
  }

  @override
  String toString() {
    return 'StoreOut(id: $id, name: $name, address: $address, owner_username: $ownerUsername)';
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        address.hashCode ^
        ownerUsername.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! StoreOut) return false;
    return id == other.id &&
        name == other.name &&
        address == other.address &&
        ownerUsername == other.ownerUsername;
  }
}
