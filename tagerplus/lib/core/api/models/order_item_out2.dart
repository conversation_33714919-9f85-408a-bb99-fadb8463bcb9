// Generated model class for OrderItemOut2
// This file is auto-generated. Do not edit manually.

import 'models.dart';

class OrderItemOut2 {
  final int id;

  final int orderId;

  final int productItemId;

  final int quantity;

  final double pricePerUnit;

  final double totalPrice;

  final ProductbOut product;

  const OrderItemOut2({
    required this.id,
    required this.orderId,
    required this.productItemId,
    required this.quantity,
    required this.pricePerUnit,
    required this.totalPrice,
    required this.product,
  });

  factory OrderItemOut2.fromJson(Map<String, dynamic> json) {
    return OrderItemOut2(
      id: json['id'] as int? ?? 0,
      orderId: json['order_id'] as int? ?? 0,
      productItemId: json['product_item_id'] as int? ?? 0,
      quantity: json['quantity'] as int? ?? 0,
      pricePerUnit: (json['price_per_unit'] as num?)?.toDouble() ?? 0.0,
      totalPrice: (json['total_price'] as num?)?.toDouble() ?? 0.0,
      product: ProductbOut.fromJson(
        json['product'] as Map<String, dynamic>? ?? {},
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_id': orderId,
      'product_item_id': productItemId,
      'quantity': quantity,
      'price_per_unit': pricePerUnit,
      'total_price': totalPrice,
      'product': product.toJson(),
    };
  }

  @override
  String toString() {
    return 'OrderItemOut2(id: $id, order_id: $orderId, product_item_id: $productItemId, quantity: $quantity, price_per_unit: $pricePerUnit, total_price: $totalPrice, product: $product)';
  }

  @override
  int get hashCode {
    return id.hashCode ^
        orderId.hashCode ^
        productItemId.hashCode ^
        quantity.hashCode ^
        pricePerUnit.hashCode ^
        totalPrice.hashCode ^
        product.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! OrderItemOut2) return false;
    return id == other.id &&
        orderId == other.orderId &&
        productItemId == other.productItemId &&
        quantity == other.quantity &&
        pricePerUnit == other.pricePerUnit &&
        totalPrice == other.totalPrice &&
        product == other.product;
  }
}
