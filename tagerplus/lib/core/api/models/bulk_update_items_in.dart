// Generated model class for BulkUpdateItemsIn
// This file is auto-generated. Do not edit manually.

/// Bulk update items request schema
class BulkUpdateItemsIn {
  final List<int> itemIds;

  final double? basePrice;

  final int? inventoryCount;

  final int? minimumOrderQuantity;

  final int? maximumOrderQuantity;

  const BulkUpdateItemsIn({
    required this.itemIds,
    this.basePrice,
    this.inventoryCount,
    this.minimumOrderQuantity,
    this.maximumOrderQuantity,
  });

  factory BulkUpdateItemsIn.fromJson(Map<String, dynamic> json) {
    return BulkUpdateItemsIn(
      itemIds:
          (json['item_ids'] as List<dynamic>?)
              ?.map((item) => item as int? ?? 0)
              .toList() ??
          [],
      basePrice: (json['base_price'] as num?)?.toDouble(),
      inventoryCount: json['inventory_count'] as int?,
      minimumOrderQuantity: json['minimum_order_quantity'] as int?,
      maximumOrderQuantity: json['maximum_order_quantity'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'item_ids': itemIds.map((item) => item).toList(),
      'base_price': basePrice,
      'inventory_count': inventoryCount,
      'minimum_order_quantity': minimumOrderQuantity,
      'maximum_order_quantity': maximumOrderQuantity,
    };
  }

  @override
  String toString() {
    return 'BulkUpdateItemsIn(item_ids: $itemIds, base_price: $basePrice, inventory_count: $inventoryCount, minimum_order_quantity: $minimumOrderQuantity, maximum_order_quantity: $maximumOrderQuantity)';
  }

  @override
  int get hashCode {
    return itemIds.hashCode ^
        basePrice.hashCode ^
        inventoryCount.hashCode ^
        minimumOrderQuantity.hashCode ^
        maximumOrderQuantity.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! BulkUpdateItemsIn) return false;
    return itemIds == other.itemIds &&
        basePrice == other.basePrice &&
        inventoryCount == other.inventoryCount &&
        minimumOrderQuantity == other.minimumOrderQuantity &&
        maximumOrderQuantity == other.maximumOrderQuantity;
  }
}
