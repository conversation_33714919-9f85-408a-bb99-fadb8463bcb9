// Generated model class for BulkAddItemsIn
// This file is auto-generated. Do not edit manually.

/// Bulk add items request schema
class BulkAddItemsIn {
  final List<int> productIds;

  const BulkAddItemsIn({required this.productIds});

  factory BulkAddItemsIn.fromJson(Map<String, dynamic> json) {
    return BulkAddItemsIn(
      productIds:
          (json['product_ids'] as List<dynamic>?)
              ?.map((item) => item as int? ?? 0)
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {'product_ids': productIds.map((item) => item).toList()};
  }

  @override
  String toString() {
    return 'BulkAddItemsIn(product_ids: $productIds)';
  }

  @override
  int get hashCode {
    return productIds.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! BulkAddItemsIn) return false;
    return productIds == other.productIds;
  }
}
