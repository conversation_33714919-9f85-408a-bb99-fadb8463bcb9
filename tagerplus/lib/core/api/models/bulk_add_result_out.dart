// Generated model class for BulkAddResultOut
// This file is auto-generated. Do not edit manually.

import 'models.dart';

/// Bulk add operation result
class BulkAddResultOut {
  final List<ItemOut> createdItems;

  final List<Map<String, dynamic>> failedItems;

  final int successCount;

  final int failureCount;

  const BulkAddResultOut({
    required this.createdItems,
    required this.failedItems,
    required this.successCount,
    required this.failureCount,
  });

  factory BulkAddResultOut.fromJson(Map<String, dynamic> json) {
    return BulkAddResultOut(
      createdItems:
          (json['created_items'] as List<dynamic>?)
              ?.map(
                (item) => item != null
                    ? ItemOut.fromJson(item as Map<String, dynamic>)
                    : ItemOut.fromJson({}),
              )
              .where((item) => item != null)
              .toList() ??
          [],
      failedItems:
          (json['failed_items'] as List<dynamic>?)
              ?.map((item) => item as Map<String, dynamic>? ?? {})
              .toList() ??
          [],
      successCount: json['success_count'] as int? ?? 0,
      failureCount: json['failure_count'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'created_items': createdItems.map((item) => item.toJson()).toList(),
      'failed_items': failedItems.map((item) => item).toList(),
      'success_count': successCount,
      'failure_count': failureCount,
    };
  }

  @override
  String toString() {
    return 'BulkAddResultOut(created_items: $createdItems, failed_items: $failedItems, success_count: $successCount, failure_count: $failureCount)';
  }

  @override
  int get hashCode {
    return createdItems.hashCode ^
        failedItems.hashCode ^
        successCount.hashCode ^
        failureCount.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! BulkAddResultOut) return false;
    return createdItems == other.createdItems &&
        failedItems == other.failedItems &&
        successCount == other.successCount &&
        failureCount == other.failureCount;
  }
}
