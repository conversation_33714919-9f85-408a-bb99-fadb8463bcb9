// Generated model class for StoreUpdate
// This file is auto-generated. Do not edit manually.

/// Schema for updating store data
class StoreUpdate {
  final String? name;

  final String? description;

  final String? address;

  final int? cityId;

  final int? stateId;

  final int? countryId;

  const StoreUpdate({
    this.name,
    this.description,
    this.address,
    this.cityId,
    this.stateId,
    this.countryId,
  });

  factory StoreUpdate.fromJson(Map<String, dynamic> json) {
    return StoreUpdate(
      name: json['name'] as String?,
      description: json['description'] as String?,
      address: json['address'] as String?,
      cityId: json['city_id'] as int?,
      stateId: json['state_id'] as int?,
      countryId: json['country_id'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'address': address,
      'city_id': cityId,
      'state_id': stateId,
      'country_id': countryId,
    };
  }

  @override
  String toString() {
    return 'StoreUpdate(name: $name, description: $description, address: $address, city_id: $cityId, state_id: $stateId, country_id: $countryId)';
  }

  @override
  int get hashCode {
    return name.hashCode ^
        description.hashCode ^
        address.hashCode ^
        cityId.hashCode ^
        stateId.hashCode ^
        countryId.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! StoreUpdate) return false;
    return name == other.name &&
        description == other.description &&
        address == other.address &&
        cityId == other.cityId &&
        stateId == other.stateId &&
        countryId == other.countryId;
  }
}
