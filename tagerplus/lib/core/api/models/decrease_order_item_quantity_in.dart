// Generated model class for DecreaseOrderItemQuantityIn
// This file is auto-generated. Do not edit manually.

/// Decrease order item quantity request schema
class DecreaseOrderItemQuantityIn {
  final int newQuantity;

  final String? reason;

  const DecreaseOrderItemQuantityIn({required this.newQuantity, this.reason});

  factory DecreaseOrderItemQuantityIn.fromJson(Map<String, dynamic> json) {
    return DecreaseOrderItemQuantityIn(
      newQuantity: json['new_quantity'] as int? ?? 0,
      reason: json['reason'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {'new_quantity': newQuantity, 'reason': reason};
  }

  @override
  String toString() {
    return 'DecreaseOrderItemQuantityIn(new_quantity: $newQuantity, reason: $reason)';
  }

  @override
  int get hashCode {
    return newQuantity.hashCode ^ reason.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! DecreaseOrderItemQuantityIn) return false;
    return newQuantity == other.newQuantity && reason == other.reason;
  }
}
