// Generated model class for LoginRequest
// This file is auto-generated. Do not edit manually.

class LoginRequest {
  final String phone;

  final String password;

  const LoginRequest({required this.phone, required this.password});

  factory LoginRequest.fromJson(Map<String, dynamic> json) {
    return LoginRequest(
      phone: json['phone']?.toString() ?? '',
      password: json['password']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'phone': phone, 'password': password};
  }

  @override
  String toString() {
    return 'LoginRequest(phone: $phone, password: $password)';
  }

  @override
  int get hashCode {
    return phone.hashCode ^ password.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! LoginRequest) return false;
    return phone == other.phone && password == other.password;
  }
}
