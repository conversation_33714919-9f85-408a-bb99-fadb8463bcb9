// Generated model class for BulkUpdateResultOut
// This file is auto-generated. Do not edit manually.

import 'models.dart';

/// Bulk update operation result
class BulkUpdateResultOut {
  final List<ItemOut> updatedItems;

  final int successCount;

  final int failureCount;

  const BulkUpdateResultOut({
    required this.updatedItems,
    required this.successCount,
    required this.failureCount,
  });

  factory BulkUpdateResultOut.fromJson(Map<String, dynamic> json) {
    return BulkUpdateResultOut(
      updatedItems:
          (json['updated_items'] as List<dynamic>?)
              ?.map(
                (item) => item != null
                    ? ItemOut.fromJson(item as Map<String, dynamic>)
                    : ItemOut.fromJson({}),
              )
              .where((item) => item != null)
              .toList() ??
          [],
      successCount: json['success_count'] as int? ?? 0,
      failureCount: json['failure_count'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'updated_items': updatedItems.map((item) => item.toJson()).toList(),
      'success_count': successCount,
      'failure_count': failureCount,
    };
  }

  @override
  String toString() {
    return 'BulkUpdateResultOut(updated_items: $updatedItems, success_count: $successCount, failure_count: $failureCount)';
  }

  @override
  int get hashCode {
    return updatedItems.hashCode ^
        successCount.hashCode ^
        failureCount.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! BulkUpdateResultOut) return false;
    return updatedItems == other.updatedItems &&
        successCount == other.successCount &&
        failureCount == other.failureCount;
  }
}
