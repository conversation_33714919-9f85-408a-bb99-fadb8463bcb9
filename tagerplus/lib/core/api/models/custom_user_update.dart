// Generated model class for CustomUserUpdate
// This file is auto-generated. Do not edit manually.

/// Schema for updating user data
class CustomUserUpdate {
  final String? username;

  final String? email;

  final String? phone;

  final String? firstName;

  final String? lastName;

  final bool? isActive;

  const CustomUserUpdate({
    this.username,
    this.email,
    this.phone,
    this.firstName,
    this.lastName,
    this.isActive,
  });

  factory CustomUserUpdate.fromJson(Map<String, dynamic> json) {
    return CustomUserUpdate(
      username: json['username'] as String?,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      isActive: json['is_active'] as bool?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'email': email,
      'phone': phone,
      'first_name': firstName,
      'last_name': lastName,
      'is_active': isActive,
    };
  }

  @override
  String toString() {
    return 'CustomUserUpdate(username: $username, email: $email, phone: $phone, first_name: $firstName, last_name: $lastName, is_active: $isActive)';
  }

  @override
  int get hashCode {
    return username.hashCode ^
        email.hashCode ^
        phone.hashCode ^
        firstName.hashCode ^
        lastName.hashCode ^
        isActive.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CustomUserUpdate) return false;
    return username == other.username &&
        email == other.email &&
        phone == other.phone &&
        firstName == other.firstName &&
        lastName == other.lastName &&
        isActive == other.isActive;
  }
}
