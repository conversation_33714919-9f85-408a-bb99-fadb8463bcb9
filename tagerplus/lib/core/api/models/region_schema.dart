// Generated model class for RegionSchema
// This file is auto-generated. Do not edit manually.

class RegionSchema {
  final int? id;

  /// Region name, e.g. USA, California, San Francisco
  final String name;

  /// Type of region
  final String? type;

  /// Parent region. E.g. USA is parent of California, California is parent of San Francisco
  final int? parent;

  /// Optional region code (e.g. ISO country code, state abbreviation)
  final String? code;

  final String slug;

  final bool? isActive;

  final String createdAt;

  final String updatedAt;

  final String? deletedAt;

  const RegionSchema({
    this.id,
    required this.name,
    this.type,
    this.parent,
    this.code,
    required this.slug,
    this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  factory RegionSchema.fromJson(Map<String, dynamic> json) {
    return RegionSchema(
      id: json['id'] as int?,
      name: json['name']?.toString() ?? '',
      type: json['type'] as String?,
      parent: json['parent'] as int?,
      code: json['code'] as String?,
      slug: json['slug']?.toString() ?? '',
      isActive: json['is_active'] as bool?,
      createdAt: json['created_at']?.toString() ?? '',
      updatedAt: json['updated_at']?.toString() ?? '',
      deletedAt: json['deleted_at'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'parent': parent,
      'code': code,
      'slug': slug,
      'is_active': isActive,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'deleted_at': deletedAt,
    };
  }

  @override
  String toString() {
    return 'RegionSchema(id: $id, name: $name, type: $type, parent: $parent, code: $code, slug: $slug, is_active: $isActive, created_at: $createdAt, updated_at: $updatedAt, deleted_at: $deletedAt)';
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        type.hashCode ^
        parent.hashCode ^
        code.hashCode ^
        slug.hashCode ^
        isActive.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode ^
        deletedAt.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! RegionSchema) return false;
    return id == other.id &&
        name == other.name &&
        type == other.type &&
        parent == other.parent &&
        code == other.code &&
        slug == other.slug &&
        isActive == other.isActive &&
        createdAt == other.createdAt &&
        updatedAt == other.updatedAt &&
        deletedAt == other.deletedAt;
  }
}
