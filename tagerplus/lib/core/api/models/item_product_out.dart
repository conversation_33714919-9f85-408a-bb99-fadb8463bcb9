// Generated model class for ItemProductOut
// This file is auto-generated. Do not edit manually.

import 'models.dart';

/// Product information for items
class ItemProductOut {
  final int id;

  final String name;

  final String title;

  final String barcode;

  final CompanyOut? company;

  final CategoryOut? category;

  const ItemProductOut({
    required this.id,
    required this.name,
    required this.title,
    required this.barcode,
    this.company,
    this.category,
  });

  factory ItemProductOut.fromJson(Map<String, dynamic> json) {
    return ItemProductOut(
      id: json['id'] as int? ?? 0,
      name: json['name']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      barcode: json['barcode']?.toString() ?? '',
      company: json['company'] != null
          ? CompanyOut.fromJson(json['company'] as Map<String, dynamic>)
          : null,
      category: json['category'] != null
          ? CategoryOut.fromJson(json['category'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'title': title,
      'barcode': barcode,
      'company': company?.toJson(),
      'category': category?.toJson(),
    };
  }

  @override
  String toString() {
    return 'ItemProductOut(id: $id, name: $name, title: $title, barcode: $barcode, company: $company, category: $category)';
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        title.hashCode ^
        barcode.hashCode ^
        company.hashCode ^
        category.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ItemProductOut) return false;
    return id == other.id &&
        name == other.name &&
        title == other.title &&
        barcode == other.barcode &&
        company == other.company &&
        category == other.category;
  }
}
