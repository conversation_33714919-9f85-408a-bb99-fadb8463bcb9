// Generated model class for RecentOrderOut
// This file is auto-generated. Do not edit manually.

import 'models.dart';

/// Recent order schema for dashboard
class RecentOrderOut {
  final int id;

  final StoreOut store;

  final String status;

  final double totalPrice;

  final String createdAt;

  const RecentOrderOut({
    required this.id,
    required this.store,
    required this.status,
    required this.totalPrice,
    required this.createdAt,
  });

  factory RecentOrderOut.fromJson(Map<String, dynamic> json) {
    return RecentOrderOut(
      id: json['id'] as int? ?? 0,
      store: StoreOut.fromJson(json['store'] as Map<String, dynamic>? ?? {}),
      status: json['status']?.toString() ?? '',
      totalPrice: (json['total_price'] as num?)?.toDouble() ?? 0.0,
      createdAt: json['created_at']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'store': store.toJson(),
      'status': status,
      'total_price': totalPrice,
      'created_at': createdAt,
    };
  }

  @override
  String toString() {
    return 'RecentOrderOut(id: $id, store: $store, status: $status, total_price: $totalPrice, created_at: $createdAt)';
  }

  @override
  int get hashCode {
    return id.hashCode ^
        store.hashCode ^
        status.hashCode ^
        totalPrice.hashCode ^
        createdAt.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! RecentOrderOut) return false;
    return id == other.id &&
        store == other.store &&
        status == other.status &&
        totalPrice == other.totalPrice &&
        createdAt == other.createdAt;
  }
}
