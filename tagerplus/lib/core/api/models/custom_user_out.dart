// Generated model class for CustomUserOut
// This file is auto-generated. Do not edit manually.

/// Schema for user output
class CustomUserOut {
  final int id;

  final String username;

  final String? email;

  final String phone;

  final bool phoneVerified;

  final String? firstName;

  final String? lastName;

  final bool isActive;

  final bool isStaff;

  final bool isSuperuser;

  final String dateJoined;

  final String? lastLogin;

  final String createdAt;

  final String updatedAt;

  const CustomUserOut({
    required this.id,
    required this.username,
    this.email,
    required this.phone,
    required this.phoneVerified,
    this.firstName,
    this.lastName,
    required this.isActive,
    required this.isStaff,
    required this.isSuperuser,
    required this.dateJoined,
    this.lastLogin,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CustomUserOut.fromJson(Map<String, dynamic> json) {
    return CustomUserOut(
      id: json['id'] as int? ?? 0,
      username: json['username']?.toString() ?? '',
      email: json['email'] as String?,
      phone: json['phone']?.toString() ?? '',
      phoneVerified: json['phone_verified'] as bool? ?? false,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      isActive: json['is_active'] as bool? ?? false,
      isStaff: json['is_staff'] as bool? ?? false,
      isSuperuser: json['is_superuser'] as bool? ?? false,
      dateJoined: json['date_joined']?.toString() ?? '',
      lastLogin: json['last_login'] as String?,
      createdAt: json['created_at']?.toString() ?? '',
      updatedAt: json['updated_at']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'phone': phone,
      'phone_verified': phoneVerified,
      'first_name': firstName,
      'last_name': lastName,
      'is_active': isActive,
      'is_staff': isStaff,
      'is_superuser': isSuperuser,
      'date_joined': dateJoined,
      'last_login': lastLogin,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  @override
  String toString() {
    return 'CustomUserOut(id: $id, username: $username, email: $email, phone: $phone, phone_verified: $phoneVerified, first_name: $firstName, last_name: $lastName, is_active: $isActive, is_staff: $isStaff, is_superuser: $isSuperuser, date_joined: $dateJoined, last_login: $lastLogin, created_at: $createdAt, updated_at: $updatedAt)';
  }

  @override
  int get hashCode {
    return id.hashCode ^
        username.hashCode ^
        email.hashCode ^
        phone.hashCode ^
        phoneVerified.hashCode ^
        firstName.hashCode ^
        lastName.hashCode ^
        isActive.hashCode ^
        isStaff.hashCode ^
        isSuperuser.hashCode ^
        dateJoined.hashCode ^
        lastLogin.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CustomUserOut) return false;
    return id == other.id &&
        username == other.username &&
        email == other.email &&
        phone == other.phone &&
        phoneVerified == other.phoneVerified &&
        firstName == other.firstName &&
        lastName == other.lastName &&
        isActive == other.isActive &&
        isStaff == other.isStaff &&
        isSuperuser == other.isSuperuser &&
        dateJoined == other.dateJoined &&
        lastLogin == other.lastLogin &&
        createdAt == other.createdAt &&
        updatedAt == other.updatedAt;
  }
}
