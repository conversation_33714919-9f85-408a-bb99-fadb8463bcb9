// Generated model class for PaginatedResponse_ItemSchema_
// This file is auto-generated. Do not edit manually.

import 'models.dart';

class PaginatedresponseItemschema {
  final List<ItemSchema> data;

  final int totalCount;

  final int page;

  final int pageSize;

  final int totalPages;

  final bool hasNext;

  final bool hasPrevious;

  const PaginatedresponseItemschema({
    required this.data,
    required this.totalCount,
    required this.page,
    required this.pageSize,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrevious,
  });

  factory PaginatedresponseItemschema.fromJson(Map<String, dynamic> json) {
    return PaginatedresponseItemschema(
      data:
          (json['data'] as List<dynamic>?)
              ?.map(
                (item) => item != null
                    ? ItemSchema.fromJson(item as Map<String, dynamic>)
                    : ItemSchema.fromJson({}),
              )
              .where((item) => item != null)
              .toList() ??
          [],
      totalCount: json['total_count'] as int? ?? 0,
      page: json['page'] as int? ?? 0,
      pageSize: json['page_size'] as int? ?? 0,
      totalPages: json['total_pages'] as int? ?? 0,
      hasNext: json['has_next'] as bool? ?? false,
      hasPrevious: json['has_previous'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data.map((item) => item.toJson()).toList(),
      'total_count': totalCount,
      'page': page,
      'page_size': pageSize,
      'total_pages': totalPages,
      'has_next': hasNext,
      'has_previous': hasPrevious,
    };
  }

  @override
  String toString() {
    return 'PaginatedresponseItemschema(data: $data, total_count: $totalCount, page: $page, page_size: $pageSize, total_pages: $totalPages, has_next: $hasNext, has_previous: $hasPrevious)';
  }

  @override
  int get hashCode {
    return data.hashCode ^
        totalCount.hashCode ^
        page.hashCode ^
        pageSize.hashCode ^
        totalPages.hashCode ^
        hasNext.hashCode ^
        hasPrevious.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! PaginatedresponseItemschema) return false;
    return data == other.data &&
        totalCount == other.totalCount &&
        page == other.page &&
        pageSize == other.pageSize &&
        totalPages == other.totalPages &&
        hasNext == other.hasNext &&
        hasPrevious == other.hasPrevious;
  }
}
