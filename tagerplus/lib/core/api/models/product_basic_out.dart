// Generated model class for ProductBasicOut
// This file is auto-generated. Do not edit manually.

/// Basic product info for order items
class ProductBasicOut {
  final int id;

  final String name;

  final String title;

  final String unit;

  final double unitCount;

  final String? imageUrl;

  const ProductBasicOut({
    required this.id,
    required this.name,
    required this.title,
    required this.unit,
    required this.unitCount,
    this.imageUrl,
  });

  factory ProductBasicOut.fromJson(Map<String, dynamic> json) {
    return ProductBasicOut(
      id: json['id'] as int? ?? 0,
      name: json['name']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      unit: json['unit']?.toString() ?? '',
      unitCount: (json['unit_count'] as num?)?.toDouble() ?? 0.0,
      imageUrl: json['image_url'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'title': title,
      'unit': unit,
      'unit_count': unitCount,
      'image_url': imageUrl,
    };
  }

  @override
  String toString() {
    return 'ProductBasicOut(id: $id, name: $name, title: $title, unit: $unit, unit_count: $unitCount, image_url: $imageUrl)';
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        title.hashCode ^
        unit.hashCode ^
        unitCount.hashCode ^
        imageUrl.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ProductBasicOut) return false;
    return id == other.id &&
        name == other.name &&
        title == other.title &&
        unit == other.unit &&
        unitCount == other.unitCount &&
        imageUrl == other.imageUrl;
  }
}
