// Generated model class for UpdateOrderStatusIn
// This file is auto-generated. Do not edit manually.

/// Update order status request schema
class UpdateOrderStatusIn {
  final String status;

  const UpdateOrderStatusIn({required this.status});

  factory UpdateOrderStatusIn.fromJson(Map<String, dynamic> json) {
    return UpdateOrderStatusIn(status: json['status']?.toString() ?? '');
  }

  Map<String, dynamic> toJson() {
    return {'status': status};
  }

  @override
  String toString() {
    return 'UpdateOrderStatusIn(status: $status)';
  }

  @override
  int get hashCode {
    return status.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! UpdateOrderStatusIn) return false;
    return status == other.status;
  }
}
