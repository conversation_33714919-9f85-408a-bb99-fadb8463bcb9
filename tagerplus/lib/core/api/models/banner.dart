// Generated model class for Banner
// This file is auto-generated. Do not edit manually.

class Banner {
  final int? id;

  final String title;

  final String image;

  final String createdAt;

  final String updatedAt;

  final String? expireDate;

  final bool? isActive;

  final List<int> regions;

  const Banner({
    this.id,
    required this.title,
    required this.image,
    required this.createdAt,
    required this.updatedAt,
    this.expireDate,
    this.isActive,
    required this.regions,
  });

  factory Banner.fromJson(Map<String, dynamic> json) {
    return Banner(
      id: json['id'] as int?,
      title: json['title']?.toString() ?? '',
      image: json['image']?.toString() ?? '',
      createdAt: json['created_at']?.toString() ?? '',
      updatedAt: json['updated_at']?.toString() ?? '',
      expireDate: json['expire_date'] as String?,
      isActive: json['is_active'] as bool?,
      regions:
          (json['regions'] as List<dynamic>?)
              ?.map((item) => item as int? ?? 0)
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'image': image,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'expire_date': expireDate,
      'is_active': isActive,
      'regions': regions.map((item) => item).toList(),
    };
  }

  @override
  String toString() {
    return 'Banner(id: $id, title: $title, image: $image, created_at: $createdAt, updated_at: $updatedAt, expire_date: $expireDate, is_active: $isActive, regions: $regions)';
  }

  @override
  int get hashCode {
    return id.hashCode ^
        title.hashCode ^
        image.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode ^
        expireDate.hashCode ^
        isActive.hashCode ^
        regions.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! Banner) return false;
    return id == other.id &&
        title == other.title &&
        image == other.image &&
        createdAt == other.createdAt &&
        updatedAt == other.updatedAt &&
        expireDate == other.expireDate &&
        isActive == other.isActive &&
        regions == other.regions;
  }
}
