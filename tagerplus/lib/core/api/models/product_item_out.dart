// Generated model class for ProductItemOut
// This file is auto-generated. Do not edit manually.

/// Product information for order items
class ProductItemOut {
  final int id;

  final String name;

  final String title;

  final String barcode;

  final String? companyName;

  final String? categoryName;

  const ProductItemOut({
    required this.id,
    required this.name,
    required this.title,
    required this.barcode,
    this.companyName,
    this.categoryName,
  });

  factory ProductItemOut.fromJson(Map<String, dynamic> json) {
    return ProductItemOut(
      id: json['id'] as int? ?? 0,
      name: json['name']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      barcode: json['barcode']?.toString() ?? '',
      companyName: json['company_name'] as String?,
      categoryName: json['category_name'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'title': title,
      'barcode': barcode,
      'company_name': companyName,
      'category_name': categoryName,
    };
  }

  @override
  String toString() {
    return 'ProductItemOut(id: $id, name: $name, title: $title, barcode: $barcode, company_name: $companyName, category_name: $categoryName)';
  }

  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        title.hashCode ^
        barcode.hashCode ^
        companyName.hashCode ^
        categoryName.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ProductItemOut) return false;
    return id == other.id &&
        name == other.name &&
        title == other.title &&
        barcode == other.barcode &&
        companyName == other.companyName &&
        categoryName == other.categoryName;
  }
}
