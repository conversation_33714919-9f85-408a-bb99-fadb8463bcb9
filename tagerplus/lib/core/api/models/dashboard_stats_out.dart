// Generated model class for DashboardStatsOut
// This file is auto-generated. Do not edit manually.

/// Dashboard statistics schema
class DashboardStatsOut {
  final int totalOrders;

  final int pendingOrders;

  final int totalItems;

  final int lowStockItems;

  const DashboardStatsOut({
    required this.totalOrders,
    required this.pendingOrders,
    required this.totalItems,
    required this.lowStockItems,
  });

  factory DashboardStatsOut.fromJson(Map<String, dynamic> json) {
    return DashboardStatsOut(
      totalOrders: json['total_orders'] as int? ?? 0,
      pendingOrders: json['pending_orders'] as int? ?? 0,
      totalItems: json['total_items'] as int? ?? 0,
      lowStockItems: json['low_stock_items'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_orders': totalOrders,
      'pending_orders': pendingOrders,
      'total_items': totalItems,
      'low_stock_items': lowStockItems,
    };
  }

  @override
  String toString() {
    return 'DashboardStatsOut(total_orders: $totalOrders, pending_orders: $pendingOrders, total_items: $totalItems, low_stock_items: $lowStockItems)';
  }

  @override
  int get hashCode {
    return totalOrders.hashCode ^
        pendingOrders.hashCode ^
        totalItems.hashCode ^
        lowStockItems.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! DashboardStatsOut) return false;
    return totalOrders == other.totalOrders &&
        pendingOrders == other.pendingOrders &&
        totalItems == other.totalItems &&
        lowStockItems == other.lowStockItems;
  }
}
