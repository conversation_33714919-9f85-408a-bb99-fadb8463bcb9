// Generated model class for ItemStatsOut
// This file is auto-generated. Do not edit manually.

/// Item statistics schema
class ItemStatsOut {
  final int totalItems;

  final int lowStockItems;

  final int outOfStockItems;

  const ItemStatsOut({
    required this.totalItems,
    required this.lowStockItems,
    required this.outOfStockItems,
  });

  factory ItemStatsOut.fromJson(Map<String, dynamic> json) {
    return ItemStatsOut(
      totalItems: json['total_items'] as int? ?? 0,
      lowStockItems: json['low_stock_items'] as int? ?? 0,
      outOfStockItems: json['out_of_stock_items'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_items': totalItems,
      'low_stock_items': lowStockItems,
      'out_of_stock_items': outOfStockItems,
    };
  }

  @override
  String toString() {
    return 'ItemStatsOut(total_items: $totalItems, low_stock_items: $lowStockItems, out_of_stock_items: $outOfStockItems)';
  }

  @override
  int get hashCode {
    return totalItems.hashCode ^
        lowStockItems.hashCode ^
        outOfStockItems.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ItemStatsOut) return false;
    return totalItems == other.totalItems &&
        lowStockItems == other.lowStockItems &&
        outOfStockItems == other.outOfStockItems;
  }
}
