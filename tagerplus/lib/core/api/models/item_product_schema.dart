// Generated model class for ItemProductSchema
// This file is auto-generated. Do not edit manually.

class ItemProductSchema {
  final int id;

  final String title;

  final String? image;

  final int? companyId;

  final int? categoryId;

  final String unit;

  final double unitCount;

  const ItemProductSchema({
    required this.id,
    required this.title,
    this.image,
    this.companyId,
    this.categoryId,
    required this.unit,
    required this.unitCount,
  });

  factory ItemProductSchema.fromJson(Map<String, dynamic> json) {
    return ItemProductSchema(
      id: json['id'] as int? ?? 0,
      title: json['title']?.toString() ?? '',
      image: json['image'] as String?,
      companyId: json['company_id'] as int?,
      categoryId: json['category_id'] as int?,
      unit: json['unit']?.toString() ?? '',
      unitCount: (json['unit_count'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'image': image,
      'company_id': companyId,
      'category_id': categoryId,
      'unit': unit,
      'unit_count': unitCount,
    };
  }

  @override
  String toString() {
    return 'ItemProductSchema(id: $id, title: $title, image: $image, company_id: $companyId, category_id: $categoryId, unit: $unit, unit_count: $unitCount)';
  }

  @override
  int get hashCode {
    return id.hashCode ^
        title.hashCode ^
        image.hashCode ^
        companyId.hashCode ^
        categoryId.hashCode ^
        unit.hashCode ^
        unitCount.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ItemProductSchema) return false;
    return id == other.id &&
        title == other.title &&
        image == other.image &&
        companyId == other.companyId &&
        categoryId == other.categoryId &&
        unit == other.unit &&
        unitCount == other.unitCount;
  }
}
