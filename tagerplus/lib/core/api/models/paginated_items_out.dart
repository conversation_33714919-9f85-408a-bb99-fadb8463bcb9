// Generated model class for PaginatedItemsOut
// This file is auto-generated. Do not edit manually.

import 'models.dart';

/// Paginated items response schema
class PaginatedItemsOut {
  final List<ItemOut> items;

  final ItemStatsOut stats;

  final List<CompanyOut> companies;

  final List<CategoryOut> categories;

  final int totalCount;

  final int page;

  final int pageSize;

  final int totalPages;

  final bool hasNext;

  final bool hasPrevious;

  const PaginatedItemsOut({
    required this.items,
    required this.stats,
    required this.companies,
    required this.categories,
    required this.totalCount,
    required this.page,
    required this.pageSize,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrevious,
  });

  factory PaginatedItemsOut.fromJson(Map<String, dynamic> json) {
    return PaginatedItemsOut(
      items:
          (json['items'] as List<dynamic>?)
              ?.map(
                (item) => item != null
                    ? ItemOut.fromJson(item as Map<String, dynamic>)
                    : ItemOut.fromJson({}),
              )
              .where((item) => item != null)
              .toList() ??
          [],
      stats: ItemStatsOut.fromJson(
        json['stats'] as Map<String, dynamic>? ?? {},
      ),
      companies:
          (json['companies'] as List<dynamic>?)
              ?.map(
                (item) => item != null
                    ? CompanyOut.fromJson(item as Map<String, dynamic>)
                    : CompanyOut.fromJson({}),
              )
              .where((item) => item != null)
              .toList() ??
          [],
      categories:
          (json['categories'] as List<dynamic>?)
              ?.map(
                (item) => item != null
                    ? CategoryOut.fromJson(item as Map<String, dynamic>)
                    : CategoryOut.fromJson({}),
              )
              .where((item) => item != null)
              .toList() ??
          [],
      totalCount: json['total_count'] as int? ?? 0,
      page: json['page'] as int? ?? 0,
      pageSize: json['page_size'] as int? ?? 0,
      totalPages: json['total_pages'] as int? ?? 0,
      hasNext: json['has_next'] as bool? ?? false,
      hasPrevious: json['has_previous'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'items': items.map((item) => item.toJson()).toList(),
      'stats': stats.toJson(),
      'companies': companies.map((item) => item.toJson()).toList(),
      'categories': categories.map((item) => item.toJson()).toList(),
      'total_count': totalCount,
      'page': page,
      'page_size': pageSize,
      'total_pages': totalPages,
      'has_next': hasNext,
      'has_previous': hasPrevious,
    };
  }

  @override
  String toString() {
    return 'PaginatedItemsOut(items: $items, stats: $stats, companies: $companies, categories: $categories, total_count: $totalCount, page: $page, page_size: $pageSize, total_pages: $totalPages, has_next: $hasNext, has_previous: $hasPrevious)';
  }

  @override
  int get hashCode {
    return items.hashCode ^
        stats.hashCode ^
        companies.hashCode ^
        categories.hashCode ^
        totalCount.hashCode ^
        page.hashCode ^
        pageSize.hashCode ^
        totalPages.hashCode ^
        hasNext.hashCode ^
        hasPrevious.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! PaginatedItemsOut) return false;
    return items == other.items &&
        stats == other.stats &&
        companies == other.companies &&
        categories == other.categories &&
        totalCount == other.totalCount &&
        page == other.page &&
        pageSize == other.pageSize &&
        totalPages == other.totalPages &&
        hasNext == other.hasNext &&
        hasPrevious == other.hasPrevious;
  }
}
