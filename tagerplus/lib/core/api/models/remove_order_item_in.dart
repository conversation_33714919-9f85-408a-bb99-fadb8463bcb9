// Generated model class for RemoveOrderItemIn
// This file is auto-generated. Do not edit manually.

/// Remove order item request schema
class RemoveOrderItemIn {
  final String? reason;

  const RemoveOrderItemIn({this.reason});

  factory RemoveOrderItemIn.fromJson(Map<String, dynamic> json) {
    return RemoveOrderItemIn(reason: json['reason'] as String?);
  }

  Map<String, dynamic> toJson() {
    return {'reason': reason};
  }

  @override
  String toString() {
    return 'RemoveOrderItemIn(reason: $reason)';
  }

  @override
  int get hashCode {
    return reason.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! RemoveOrderItemIn) return false;
    return reason == other.reason;
  }
}
