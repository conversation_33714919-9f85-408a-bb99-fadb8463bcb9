// Generated model class for OrderItemOut
// This file is auto-generated. Do not edit manually.

import 'models.dart';

/// Order item schema
class OrderItemOut {
  final int id;

  final ProductItemOut product;

  final int quantity;

  final double pricePerUnit;

  final double totalPrice;

  const OrderItemOut({
    required this.id,
    required this.product,
    required this.quantity,
    required this.pricePerUnit,
    required this.totalPrice,
  });

  factory OrderItemOut.fromJson(Map<String, dynamic> json) {
    return OrderItemOut(
      id: json['id'] as int? ?? 0,
      product: ProductItemOut.fromJson(
        json['product'] as Map<String, dynamic>? ?? {},
      ),
      quantity: json['quantity'] as int? ?? 0,
      pricePerUnit: (json['price_per_unit'] as num?)?.toDouble() ?? 0.0,
      totalPrice: (json['total_price'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product': product.toJson(),
      'quantity': quantity,
      'price_per_unit': pricePerUnit,
      'total_price': totalPrice,
    };
  }

  @override
  String toString() {
    return 'OrderItemOut(id: $id, product: $product, quantity: $quantity, price_per_unit: $pricePerUnit, total_price: $totalPrice)';
  }

  @override
  int get hashCode {
    return id.hashCode ^
        product.hashCode ^
        quantity.hashCode ^
        pricePerUnit.hashCode ^
        totalPrice.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! OrderItemOut) return false;
    return id == other.id &&
        product == other.product &&
        quantity == other.quantity &&
        pricePerUnit == other.pricePerUnit &&
        totalPrice == other.totalPrice;
  }
}
