// Generated service class for products operations
// This file is auto-generated. Do not edit manually.

import 'package:dio/dio.dart';
import '../models/models.dart';
import 'package:openapi_dart_gen/openapi_dart_gen.dart';

/// Service class for products operations
class ProductsService {
  final Dio _dio;

  const ProductsService(this._dio);

  /// List Products
  ///
  /// List all products with pagination and filtering.
  /// Requires authentication.
  ///
  Future<Response<PaginatedProductResponse>> listProducts({
    int? page,
    int? pageSize,
    String? search,
    int? companyId,
    int? categoryId,
    String? unit,
    int? regionId,
  }) async {
    const path = '/api/products/';
    final queryParameters = <String, dynamic>{};
    if (page != null) {
      queryParameters['page'] = page;
    }
    if (pageSize != null) {
      queryParameters['page_size'] = pageSize;
    }
    if (search != null) {
      queryParameters['search'] = search;
    }
    if (companyId != null) {
      queryParameters['company_id'] = companyId;
    }
    if (categoryId != null) {
      queryParameters['category_id'] = categoryId;
    }
    if (unit != null) {
      queryParameters['unit'] = unit;
    }
    if (regionId != null) {
      queryParameters['region_id'] = regionId;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseData = PaginatedProductResponse.fromJson(
        response.data as Map<String, dynamic>,
      );
      return Response<PaginatedProductResponse>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Get Product
  ///
  /// Get a specific product by ID.
  /// Requires authentication.
  ///
  Future<Response<ProductSchema>> getProduct(int productId) async {
    String path = '/api/products/{product_id}';
    path = path.replaceAll('{product_id}', productId.toString());
    try {
      final response = await _dio.get(path);
      final responseData = ProductSchema.fromJson(
        response.data as Map<String, dynamic>,
      );
      return Response<ProductSchema>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }
}
