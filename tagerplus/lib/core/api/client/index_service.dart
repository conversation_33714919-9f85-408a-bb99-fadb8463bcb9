// Generated service class for index operations
// This file is auto-generated. Do not edit manually.

import 'package:dio/dio.dart';
import '../models/models.dart';
import 'package:openapi_dart_gen/openapi_dart_gen.dart';

/// Service class for index operations
class IndexService {
  final Dio _dio;

  const IndexService(this._dio);

  /// Get Banners
  Future<Response<List<Banner>>> getBanners({int? regionId}) async {
    const path = '/api/banners';
    final queryParameters = <String, dynamic>{};
    if (regionId != null) {
      queryParameters['region_id'] = regionId;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseDataList = (response.data as List<dynamic>)
          .map((item) => Banner.fromJson(item as Map<String, dynamic>))
          .toList();
      return Response<List<Banner>>(
        data: responseDataList,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }
}
