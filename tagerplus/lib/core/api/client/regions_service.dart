// Generated service class for regions operations
// This file is auto-generated. Do not edit manually.

import 'package:dio/dio.dart';
import '../models/models.dart';
import 'package:openapi_dart_gen/openapi_dart_gen.dart';

/// Service class for regions operations
class RegionsService {
  final Dio _dio;

  const RegionsService(this._dio);

  /// List Regions
  ///
  /// List all regions with pagination and filtering.
  /// Requires authentication.
  ///
  Future<Response<PaginatedresponseRegionschema>> listRegions({
    int? page,
    int? pageSize,
    String? search,
    String? type,
    int? parentId,
    bool? isActive,
  }) async {
    const path = '/api/regions/';
    final queryParameters = <String, dynamic>{};
    if (page != null) {
      queryParameters['page'] = page;
    }
    if (pageSize != null) {
      queryParameters['page_size'] = pageSize;
    }
    if (search != null) {
      queryParameters['search'] = search;
    }
    if (type != null) {
      queryParameters['type'] = type;
    }
    if (parentId != null) {
      queryParameters['parent_id'] = parentId;
    }
    if (isActive != null) {
      queryParameters['is_active'] = isActive;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseData = PaginatedresponseRegionschema.fromJson(
        response.data as Map<String, dynamic>,
      );
      return Response<PaginatedresponseRegionschema>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Get Countries
  ///
  /// List all countries.
  /// Requires authentication.
  ///
  Future<Response<List<RegionSchema>>> getCountries() async {
    const path = '/api/regions/countries';
    try {
      final response = await _dio.get(path);
      final responseDataList = (response.data as List<dynamic>)
          .map((item) => RegionSchema.fromJson(item as Map<String, dynamic>))
          .toList();
      return Response<List<RegionSchema>>(
        data: responseDataList,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Get States
  ///
  /// List all states/provinces, optionally filtered by country.
  /// Requires authentication.
  ///
  Future<Response<List<RegionSchema>>> getStates({int? countryId}) async {
    const path = '/api/regions/states';
    final queryParameters = <String, dynamic>{};
    if (countryId != null) {
      queryParameters['country_id'] = countryId;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseDataList = (response.data as List<dynamic>)
          .map((item) => RegionSchema.fromJson(item as Map<String, dynamic>))
          .toList();
      return Response<List<RegionSchema>>(
        data: responseDataList,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Get Cities
  ///
  /// List all cities, optionally filtered by state.
  /// Requires authentication.
  ///
  Future<Response<List<RegionSchema>>> getCities({int? stateId}) async {
    const path = '/api/regions/cities';
    final queryParameters = <String, dynamic>{};
    if (stateId != null) {
      queryParameters['state_id'] = stateId;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseDataList = (response.data as List<dynamic>)
          .map((item) => RegionSchema.fromJson(item as Map<String, dynamic>))
          .toList();
      return Response<List<RegionSchema>>(
        data: responseDataList,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Get Region
  ///
  /// Get a specific region by ID.
  /// Requires authentication.
  ///
  Future<Response<RegionSchema>> getRegion(int regionId) async {
    String path = '/api/regions/{region_id}';
    path = path.replaceAll('{region_id}', regionId.toString());
    try {
      final response = await _dio.get(path);
      final responseData = RegionSchema.fromJson(
        response.data as Map<String, dynamic>,
      );
      return Response<RegionSchema>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }
}
