// Generated service class for items operations
// This file is auto-generated. Do not edit manually.

import 'package:dio/dio.dart';
import '../models/models.dart';
import 'package:openapi_dart_gen/openapi_dart_gen.dart';

/// Service class for items operations
class ItemsService {
  final Dio _dio;

  const ItemsService(this._dio);

  /// List Items
  ///
  /// List items for a specific wholesaler including product and category data.
  /// Excludes items with zero inventory or zero price.
  ///
  Future<Response<PaginatedresponseItemschema>> listItems({
    int? wholesalerId,
    int? page,
    int? pageSize,
    String? search,
    int? categoryId,
    int? productId,
    int? regionId,
  }) async {
    const path = '/api/items/';
    final queryParameters = <String, dynamic>{};
    if (wholesalerId != null) {
      queryParameters['wholesaler_id'] = wholesalerId;
    }
    if (page != null) {
      queryParameters['page'] = page;
    }
    if (pageSize != null) {
      queryParameters['page_size'] = pageSize;
    }
    if (search != null) {
      queryParameters['search'] = search;
    }
    if (categoryId != null) {
      queryParameters['category_id'] = categoryId;
    }
    if (productId != null) {
      queryParameters['product_id'] = productId;
    }
    if (regionId != null) {
      queryParameters['region_id'] = regionId;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseData = PaginatedresponseItemschema.fromJson(
        response.data as Map<String, dynamic>,
      );
      return Response<PaginatedresponseItemschema>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Get Items By Ids
  ///
  /// Get a list of items by their IDs.
  /// IDs should be provided as a comma-separated string.
  /// e.g., /api/items/by_ids?ids=1,2,3
  ///
  Future<Response<List<ItemSchema>>> getItemsByIds({
    String? ids,
    int? regionId,
  }) async {
    const path = '/api/items/by_id';
    final queryParameters = <String, dynamic>{};
    if (ids != null) {
      queryParameters['ids'] = ids;
    }
    if (regionId != null) {
      queryParameters['region_id'] = regionId;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseDataList = (response.data as List<dynamic>)
          .map((item) => ItemSchema.fromJson(item as Map<String, dynamic>))
          .toList();
      return Response<List<ItemSchema>>(
        data: responseDataList,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }
}
