// Generated service class for stores operations
// This file is auto-generated. Do not edit manually.

import 'package:dio/dio.dart';
import '../models/models.dart';
import 'package:openapi_dart_gen/openapi_dart_gen.dart';

/// Service class for stores operations
class StoresService {
  final Dio _dio;

  const StoresService(this._dio);

  /// List Stores
  ///
  /// List all stores with pagination and filtering.
  /// Requires authentication.
  ///
  Future<Response<PaginatedresponseStoreschema>> listStores({
    int? page,
    int? pageSize,
    String? search,
    int? ownerId,
    int? cityId,
  }) async {
    const path = '/api/stores/';
    final queryParameters = <String, dynamic>{};
    if (page != null) {
      queryParameters['page'] = page;
    }
    if (pageSize != null) {
      queryParameters['page_size'] = pageSize;
    }
    if (search != null) {
      queryParameters['search'] = search;
    }
    if (ownerId != null) {
      queryParameters['owner_id'] = ownerId;
    }
    if (cityId != null) {
      queryParameters['city_id'] = cityId;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseData = PaginatedresponseStoreschema.fromJson(
        response.data as Map<String, dynamic>,
      );
      return Response<PaginatedresponseStoreschema>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Create Store
  ///
  /// Create a new store.
  /// Requires authentication.
  ///
  Future<Response<StoreSchema>> createStore(StoreIn requestData) async {
    const path = '/api/stores/';
    try {
      final response = await _dio.post(path, data: requestData.toJson());
      final responseData = StoreSchema.fromJson(
        response.data as Map<String, dynamic>,
      );
      return Response<StoreSchema>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// List My Stores
  ///
  /// List current user's stores with pagination.
  /// Requires authentication.
  ///
  Future<Response<PaginatedresponseStoreschema>> listMyStores({
    int? page,
    int? pageSize,
  }) async {
    const path = '/api/stores/my';
    final queryParameters = <String, dynamic>{};
    if (page != null) {
      queryParameters['page'] = page;
    }
    if (pageSize != null) {
      queryParameters['page_size'] = pageSize;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseData = PaginatedresponseStoreschema.fromJson(
        response.data as Map<String, dynamic>,
      );
      return Response<PaginatedresponseStoreschema>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Get Store
  ///
  /// Get a specific store by ID.
  /// Requires authentication.
  ///
  Future<Response<StoreSchema>> getStore(int storeId) async {
    String path = '/api/stores/{store_id}';
    path = path.replaceAll('{store_id}', storeId.toString());
    try {
      final response = await _dio.get(path);
      final responseData = StoreSchema.fromJson(
        response.data as Map<String, dynamic>,
      );
      return Response<StoreSchema>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Update Store
  ///
  /// Update a store.
  /// Requires authentication.
  ///
  Future<Response<StoreSchema>> updateStore(
    int storeId,
    StoreUpdate requestData,
  ) async {
    String path = '/api/stores/{store_id}';
    path = path.replaceAll('{store_id}', storeId.toString());
    try {
      final response = await _dio.put(path, data: requestData.toJson());
      final responseData = StoreSchema.fromJson(
        response.data as Map<String, dynamic>,
      );
      return Response<StoreSchema>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Delete Store
  ///
  /// Soft delete a store.
  /// Requires authentication.
  ///
  Future<Response<dynamic>> deleteStore(int storeId) async {
    String path = '/api/stores/{store_id}';
    path = path.replaceAll('{store_id}', storeId.toString());
    try {
      final response = await _dio.delete(path);
      return response;
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }
}
