// Generated service class for core operations
// This file is auto-generated. Do not edit manually.

import 'package:dio/dio.dart';
import 'package:openapi_dart_gen/openapi_dart_gen.dart';

/// Service class for core operations
class CoreService {
  final Dio _dio;

  const CoreService(this._dio);

  /// Api Root
  /// API root endpoint
  Future<Response<dynamic>> apiRoot() async {
    const path = '/api/';
    try {
      final response = await _dio.get(path);
      return response;
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }
}
