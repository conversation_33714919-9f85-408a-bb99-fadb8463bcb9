// Generated service class for gomla operations
// This file is auto-generated. Do not edit manually.

import 'package:dio/dio.dart';
import '../models/models.dart';
import 'package:openapi_dart_gen/openapi_dart_gen.dart';

/// Service class for gomla operations
class GomlaService {
  final Dio _dio;

  const GomlaService(this._dio);

  /// Get Dashboard
  ///
  /// Get wholesaler dashboard data including statistics, recent orders, and sales data.
  /// Requires wholesaler authentication.
  ///
  Future<Response<DashboardOut>> getDashboard() async {
    const path = '/api/gomla/dashboard';
    try {
      final response = await _dio.get(path);
      final responseData = DashboardOut.fromJson(
        response.data as Map<String, dynamic>,
      );
      return Response<DashboardOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// List Orders
  ///
  /// List all orders for the wholesaler with filtering and pagination.
  /// Requires wholesaler authentication.
  ///
  Future<Response<PaginatedOrdersOut>> listOrders({
    int? page,
    int? pageSize,
    String? status,
    String? search,
    String? dateFrom,
    String? dateTo,
  }) async {
    const path = '/api/gomla/orders';
    final queryParameters = <String, dynamic>{};
    if (page != null) {
      queryParameters['page'] = page;
    }
    if (pageSize != null) {
      queryParameters['page_size'] = pageSize;
    }
    if (status != null) {
      queryParameters['status'] = status;
    }
    if (search != null) {
      queryParameters['search'] = search;
    }
    if (dateFrom != null) {
      queryParameters['date_from'] = dateFrom;
    }
    if (dateTo != null) {
      queryParameters['date_to'] = dateTo;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseData = PaginatedOrdersOut.fromJson(
        response.data as Map<String, dynamic>,
      );
      return Response<PaginatedOrdersOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Get Order Detail
  ///
  /// Get detailed information about a specific order.
  /// Requires wholesaler authentication.
  ///
  Future<Response<OrderDetailOut>> getOrderDetail(int orderId) async {
    String path = '/api/gomla/orders/{order_id}';
    path = path.replaceAll('{order_id}', orderId.toString());
    try {
      final response = await _dio.get(path);
      final responseData = OrderDetailOut.fromJson(
        response.data as Map<String, dynamic>,
      );
      return Response<OrderDetailOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Update Order Status
  ///
  /// Update the status of an order.
  /// Requires wholesaler authentication.
  ///
  Future<Response<dynamic>> updateOrderStatus(
    int orderId,
    UpdateOrderStatusIn requestData,
  ) async {
    String path = '/api/gomla/orders/{order_id}/status';
    path = path.replaceAll('{order_id}', orderId.toString());
    try {
      final response = await _dio.put(path, data: requestData.toJson());
      return response;
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Remove Order Item
  ///
  /// Remove an item from an order and restore inventory.
  /// Requires wholesaler authentication.
  ///
  Future<Response<dynamic>> removeOrderItem(
    int orderId,
    int itemId,
    RemoveOrderItemIn requestData,
  ) async {
    String path = '/api/gomla/orders/{order_id}/items/{item_id}';
    path = path.replaceAll('{order_id}', orderId.toString());
    path = path.replaceAll('{item_id}', itemId.toString());
    try {
      final response = await _dio.delete(path, data: requestData.toJson());
      return response;
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Decrease Order Item Quantity
  ///
  /// Decrease the quantity of an item in an order and restore the difference to inventory.
  /// Requires wholesaler authentication.
  ///
  Future<Response<dynamic>> decreaseOrderItemQuantity(
    int orderId,
    int itemId,
    DecreaseOrderItemQuantityIn requestData,
  ) async {
    String path = '/api/gomla/orders/{order_id}/items/{item_id}/quantity';
    path = path.replaceAll('{order_id}', orderId.toString());
    path = path.replaceAll('{item_id}', itemId.toString());
    try {
      final response = await _dio.put(path, data: requestData.toJson());
      return response;
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// List Items
  ///
  /// List all items/inventory for the wholesaler with filtering and pagination.
  /// Requires wholesaler authentication.
  ///
  Future<Response<PaginatedItemsOut>> listItems({
    int? page,
    int? pageSize,
    String? search,
    int? company,
    int? category,
    String? stock,
    String? sort,
  }) async {
    const path = '/api/gomla/items';
    final queryParameters = <String, dynamic>{};
    if (page != null) {
      queryParameters['page'] = page;
    }
    if (pageSize != null) {
      queryParameters['page_size'] = pageSize;
    }
    if (search != null) {
      queryParameters['search'] = search;
    }
    if (company != null) {
      queryParameters['company'] = company;
    }
    if (category != null) {
      queryParameters['category'] = category;
    }
    if (stock != null) {
      queryParameters['stock'] = stock;
    }
    if (sort != null) {
      queryParameters['sort'] = sort;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseData = PaginatedItemsOut.fromJson(
        response.data as Map<String, dynamic>,
      );
      return Response<PaginatedItemsOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Get Item Detail
  ///
  /// Get detailed information about a specific item.
  /// Requires wholesaler authentication.
  ///
  Future<Response<ItemOut>> getItemDetail(int itemId) async {
    String path = '/api/gomla/items/{item_id}';
    path = path.replaceAll('{item_id}', itemId.toString());
    try {
      final response = await _dio.get(path);
      final responseData = ItemOut.fromJson(
        response.data as Map<String, dynamic>,
      );
      return Response<ItemOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Update Item
  ///
  /// Update an existing item.
  /// Requires wholesaler authentication.
  ///
  Future<Response<ItemOut>> updateItem(
    int itemId,
    ItemUpdateIn requestData,
  ) async {
    String path = '/api/gomla/items/{item_id}';
    path = path.replaceAll('{item_id}', itemId.toString());
    try {
      final response = await _dio.put(path, data: requestData.toJson());
      final responseData = ItemOut.fromJson(
        response.data as Map<String, dynamic>,
      );
      return Response<ItemOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Delete Item
  ///
  /// Delete an item (soft delete).
  /// Requires wholesaler authentication.
  ///
  Future<Response<dynamic>> deleteItem(int itemId) async {
    String path = '/api/gomla/items/{item_id}';
    path = path.replaceAll('{item_id}', itemId.toString());
    try {
      final response = await _dio.delete(path);
      return response;
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// List Available Products
  ///
  /// List products available for adding to inventory (not already in wholesaler's inventory).
  /// Requires wholesaler authentication.
  ///
  Future<Response<PaginatedProductsForBulkOut>> listAvailableProducts({
    int? page,
    int? pageSize,
    String? search,
    int? company,
    int? category,
  }) async {
    const path = '/api/gomla/products/available';
    final queryParameters = <String, dynamic>{};
    if (page != null) {
      queryParameters['page'] = page;
    }
    if (pageSize != null) {
      queryParameters['page_size'] = pageSize;
    }
    if (search != null) {
      queryParameters['search'] = search;
    }
    if (company != null) {
      queryParameters['company'] = company;
    }
    if (category != null) {
      queryParameters['category'] = category;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseData = PaginatedProductsForBulkOut.fromJson(
        response.data as Map<String, dynamic>,
      );
      return Response<PaginatedProductsForBulkOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Bulk Add Items
  ///
  /// Bulk add items with default values.
  /// Requires wholesaler authentication.
  ///
  Future<Response<BulkAddResultOut>> bulkAddItems(
    BulkAddItemsIn requestData,
  ) async {
    const path = '/api/gomla/items/bulk-add';
    try {
      final response = await _dio.post(path, data: requestData.toJson());
      final responseData = BulkAddResultOut.fromJson(
        response.data as Map<String, dynamic>,
      );
      return Response<BulkAddResultOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Bulk Update Items
  ///
  /// Bulk update multiple items.
  /// Requires wholesaler authentication.
  ///
  Future<Response<BulkUpdateResultOut>> bulkUpdateItems(
    BulkUpdateItemsIn requestData,
  ) async {
    const path = '/api/gomla/items/bulk-update';
    try {
      final response = await _dio.put(path, data: requestData.toJson());
      final responseData = BulkUpdateResultOut.fromJson(
        response.data as Map<String, dynamic>,
      );
      return Response<BulkUpdateResultOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }
}
