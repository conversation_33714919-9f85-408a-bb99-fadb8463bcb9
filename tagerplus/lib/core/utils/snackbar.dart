import 'package:flutter/material.dart';
import 'package:get/get.dart';

class MySnackbar {
  static MySnackbar instance = MySnackbar();

  final Set<String> _displayedMessages = {};
  final Map<String, DateTime> _messageTimestamps = {};
  static const _messageCooldown = Duration(seconds: 8);

  static void show({
    required String title,
    required String message,
    Color? backgroundColor,
    Color? textColor,
  }) {
    final now = DateTime.now();
    final messageKey = '$title:$message';
    // print(instance._displayedMessages.contains(messageKey));

    // Check if message is already displayed or was shown recently
    if (instance._displayedMessages.contains(messageKey) ||
        (instance._messageTimestamps[messageKey]?.isAfter(
              now.subtract(_messageCooldown),
            ) ??
            false)) {
      return;
    }

    // Clean up old timestamps
    instance._messageTimestamps.removeWhere(
      (_, timestamp) => now.difference(timestamp) > _messageCooldown,
    );

    instance._displayedMessages.add(messageKey);
    instance._messageTimestamps[messageKey] = now;
    Get.showSnackbar(
      GetSnackBar(
        snackStyle: SnackStyle.floating,
        titleText: Text(title),
        messageText: Text(message),
        backgroundColor:
            backgroundColor ?? Get.theme.colorScheme.surfaceContainerLow,
        snackPosition: SnackPosition.top,
        borderRadius: 10,
        margin: const EdgeInsets.all(10),
        duration: const Duration(seconds: 3),
        borderColor: Get.theme.dividerColor.withValues(alpha: 0.5),
        dismissDirection: DismissDirection.horizontal,
        isDismissible: true,
        onTap: (_) {
          instance._displayedMessages.remove(messageKey);
          instance._messageTimestamps.remove(messageKey);
        },
      ),
    );

    Future.delayed(const Duration(seconds: 5), () {
      instance._displayedMessages.remove(messageKey);
      instance._messageTimestamps.remove(messageKey);
    });
  }
}
