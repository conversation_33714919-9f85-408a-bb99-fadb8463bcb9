/// Utility class for formatting currency values in the app
class CurrencyFormatter {
  /// Format price with Egyptian Pounds currency
  static String formatPrice(double price) {
    if (price == 0.0) {
      return 'غير محدد';
    }
    return '${price.toStringAsFixed(2)} ج.م';
  }

  /// Format price without currency symbol (for display purposes)
  static String formatPriceValue(double price) {
    if (price == 0.0) {
      return '0';
    }
    // Remove unnecessary decimal places if it's a whole number
    if (price == price.toInt()) {
      return price.toInt().toString();
    }
    return price.toStringAsFixed(2);
  }

  /// Format price with currency symbol for display in product tiles
  static String formatPriceForTile(double price) {
    if (price == 0.0) {
      return '0 ج.م';
    }
    // Remove unnecessary decimal places if it's a whole number
    if (price == price.toInt()) {
      return '${price.toInt()} ج.م';
    }
    return '${price.toStringAsFixed(2)} ج.م';
  }

  /// Check if price is valid (greater than 0)
  static bool isValidPrice(double? price) {
    return price != null && price > 0;
  }
}
