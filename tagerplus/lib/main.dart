import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:get_secure_storage/get_secure_storage.dart';
import 'package:tagerplus/core/themes/app_themes.dart';
import 'package:tagerplus/pages/auth/login/login.dart';
import 'package:tagerplus/pages/home/<USER>';
import 'package:tagerplus/services/api.dart';
import 'package:tagerplus/services/auth.dart';
import 'package:tagerplus/services/cart.dart';
import 'package:tagerplus/services/storage.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetSecureStorage.init(
    password: '19af010eff39553f712e175d0bdcab40',
    container: 'tagerplus_storage',
  );
  Get.put<LocalStorageService>(LocalStorageService());
  Get.put<ApiService>(ApiService().init());
  Get.put<AuthService>(await AuthService().init());
  Get.put<CartService>(CartService());
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'TagerPlus',
      theme: AppTheme.defaultTheme,
      debugShowCheckedModeBanner: false,
      // translations: LocalizationService(),
      locale: const Locale('ar', 'EG'),
      fallbackLocale: const Locale('en', 'US'),
      supportedLocales: const [Locale('ar', 'EG'), Locale('en', 'US')],
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      home: AuthService.to.isLoggedIn ? const HomePage() : const LoginPage(),
    );
  }
}
