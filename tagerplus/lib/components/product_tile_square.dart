import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/utils/snackbar.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'package:tagerplus/components/network_image.dart';
import 'package:tagerplus/core/utils/currency_formatter.dart';

class ProductTileSquare extends StatelessWidget {
  final String title;
  final double? price;
  final String description;
  final String? imageUrl;
  final int? productId;
  final double? insteadOfPrice;

  const ProductTileSquare({
    super.key,
    required this.title,
    this.price,
    this.description = '',
    this.imageUrl,
    this.productId,
    this.insteadOfPrice,
    this.onTap,
  });

  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDefaults.padding / 2),
      child: Material(
        borderRadius: AppDefaults.borderRadius,
        color: AppColors.scaffoldBackground,
        child: InkWell(
          borderRadius: AppDefaults.borderRadius,
          onTap:
              onTap ??
              () {
                MySnackbar.show(title: 'تفاصيل المنتج', message: 'تفاصيل المنتج قريباً');
              },
          child: Container(
            width: Get.width * .45,
            padding: const EdgeInsets.all(AppDefaults.padding),
            decoration: BoxDecoration(
              border: Border.all(width: 0.1, color: AppColors.placeholder),
              borderRadius: AppDefaults.borderRadius,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(AppDefaults.padding / 2),
                  child: AspectRatio(
                    aspectRatio: 1 / 1,
                    child: NetworkImageWithLoader(
                      imageUrl ?? '',
                      fit: BoxFit.contain,
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  title,
                  style: Theme.of(
                    context,
                  ).textTheme.titleMedium?.copyWith(color: Colors.black),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (description.isNotEmpty)
                  Text(
                    description,
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                const SizedBox(height: 4),
                _buildPriceSection(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPriceSection(BuildContext context) {
    // Check if we have a valid price to display
    final hasValidPrice = CurrencyFormatter.isValidPrice(price);

    if (!hasValidPrice) {
      return Text(
        'السعر غير متوفر',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Colors.grey,
          fontStyle: FontStyle.italic,
        ),
      );
    }

    final bool hasDiscount =
        CurrencyFormatter.isValidPrice(insteadOfPrice) &&
        insteadOfPrice! > (price ?? 0);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          CurrencyFormatter.formatPriceForTile(price ?? 0),
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: hasDiscount ? AppColors.primary : Colors.black,
          ),
        ),
        const SizedBox(width: 6),
        if (hasDiscount)
          Text(
            CurrencyFormatter.formatPriceForTile(insteadOfPrice ?? 0),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey,
              decoration: TextDecoration.lineThrough,
              height: 1,
            ),
          ),
      ],
    );
  }
}
