import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'package:tagerplus/components/network_image.dart';
import 'package:tagerplus/core/utils/currency_formatter.dart';

class ProductTileWithQuantity extends StatelessWidget {
  final String title;
  final double? price;
  final String description;
  final String? imageUrl;
  final int? productId;
  final double? insteadOfPrice;
  final int quantity;
  final VoidCallback? onIncrement;
  final VoidCallback? onDecrement;
  final VoidCallback? onTap;

  const ProductTileWithQuantity({
    super.key,
    required this.title,
    this.price,
    this.description = '',
    this.imageUrl,
    this.productId,
    this.insteadOfPrice,
    this.quantity = 0,
    this.onIncrement,
    this.onDecrement,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDefaults.padding / 2),
      child: Material(
        borderRadius: AppDefaults.borderRadius,
        color: AppColors.scaffoldBackground,
        child: InkWell(
          borderRadius: AppDefaults.borderRadius,
          onTap: onTap,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppDefaults.padding),
            decoration: BoxDecoration(
              border: Border.all(width: 0.2, color: AppColors.placeholder),
              borderRadius: AppDefaults.borderRadius,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(AppDefaults.padding / 2),
                  child: NetworkImageWithLoader(
                    imageUrl ?? '',
                    fit: BoxFit.contain,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  title,
                  style: Theme.of(
                    context,
                  ).textTheme.titleMedium?.copyWith(color: Colors.black),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (description.isNotEmpty)
                  Text(
                    description,
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                const SizedBox(height: 4),
                _buildPriceSection(context),
                const SizedBox(height: 8),
                _buildQuantityControls(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPriceSection(BuildContext context) {
    // Check if we have a valid price to display
    final hasValidPrice = CurrencyFormatter.isValidPrice(price);

    if (!hasValidPrice) {
      return Text(
        'السعر غير متوفر',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Colors.grey,
          fontStyle: FontStyle.italic,
        ),
      );
    }

    final bool hasDiscount =
        CurrencyFormatter.isValidPrice(insteadOfPrice) &&
        insteadOfPrice! > (price ?? 0);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          CurrencyFormatter.formatPriceForTile(price ?? 0),
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: hasDiscount ? AppColors.primary : Colors.black,
          ),
        ),
        const SizedBox(width: 6),
        if (hasDiscount)
          Text(
            CurrencyFormatter.formatPriceForTile(insteadOfPrice ?? 0),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey,
              decoration: TextDecoration.lineThrough,
              height: 1,
            ),
          ),
      ],
    );
  }

  Widget _buildQuantityControls(BuildContext context) {
    return Container(
      height: 40,
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppDefaults.radius),
        border: Border.all(color: AppColors.primary.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Decrement button
          Expanded(
            child: InkWell(
              onTap: onDecrement,
              borderRadius: const BorderRadius.horizontal(
                left: Radius.circular(8),
              ),
              child: Container(
                margin: const EdgeInsets.all(AppDefaults.padding / 3),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(AppDefaults.radius / .5),
                  color: quantity > 0 ? AppColors.primary : Colors.transparent,
                ),
                child: Icon(
                  Icons.remove,
                  color: quantity > 0 ? Colors.white : AppColors.primary,
                  size: 20,
                ),
              ),
            ),
          ),

          // Quantity display
          Expanded(
            flex: 2,
            child: Container(
              alignment: Alignment.center,
              child: Text(
                '$quantity',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ),
          ),

          // Increment button
          Expanded(
            child: InkWell(
              onTap: onIncrement,
              borderRadius: const BorderRadius.horizontal(
                right: Radius.circular(8),
              ),
              child: Container(
                alignment: Alignment.center,
                margin: const EdgeInsets.all(AppDefaults.padding / 3),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(AppDefaults.radius / .5),
                  color: AppColors.primary,
                ),
                child: const Icon(Icons.add, color: Colors.white, size: 20),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
