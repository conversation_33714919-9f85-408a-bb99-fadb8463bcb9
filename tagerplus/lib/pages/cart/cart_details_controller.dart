import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/api/models/item_schema.dart';
import 'package:tagerplus/core/api/models/wholesaler_schema.dart';
import 'package:tagerplus/core/api/models/region_min_charge_schema.dart';
import 'package:tagerplus/services/api.dart';
import 'package:tagerplus/services/auth.dart';
import 'package:tagerplus/services/cart.dart';
import 'package:tagerplus/pages/wholesaler/wholesaler_details.dart';
import 'package:tagerplus/core/utils/snackbar.dart';
import 'package:tagerplus/pages/cart/checkout_page.dart';

class CartDetailsController extends GetxController {
  final int wholesalerId;
  final bool isHomePage;
  
  CartDetailsController({
    required this.wholesalerId,
    this.isHomePage = false,
  });

  // Observable properties
  final items = <ItemSchema>[].obs;
  final wholesaler = Rxn<WholesalerSchema>();
  final minChargeInfo = Rxn<RegionMinChargeSchema>();
  final isLoading = true.obs;
  final isLoadingMinCharge = false.obs;
  final hasError = false.obs;
  final minChargeError = false.obs;

  // Services
  final CartService cartService = Get.find<CartService>();
  final AuthService authService = Get.find<AuthService>();
  final ApiService apiService = Get.find<ApiService>();

  @override
  void onInit() {
    super.onInit();
    _initializeCart();
  }

  Future<void> _initializeCart() async {
    await Future.wait([
      fetchItems(),
      fetchWholesaler(),
    ]);
    await loadMinChargeInfo();
  }

  // Getters for cart state
  List<CartItem> get currentCart {
    return cartService.cart[wholesalerId] ?? <CartItem>[];
  }

  bool get hasCart => currentCart.isNotEmpty;

  double get totalPrice {
    return cartService.getCartTotalPrice(wholesalerId);
  }

  int get itemCount {
    return cartService.getCartItemCount(wholesalerId);
  }

  int get totalQuantity {
    return currentCart.fold(0, (sum, item) => sum + item.quantity.value);
  }

  bool get canProceedToCheckout {
    if (!hasCart || minChargeInfo.value == null) return false;
    final minCharge = minChargeInfo.value!;
    return totalPrice >= minCharge.minCharge && itemCount >= minCharge.minItems;
  }

  Future<void> fetchItems() async {
    try {
      isLoading.value = true;
      hasError.value = false;
      
      final cartItems = currentCart.map((e) => e.item.id).toList();
      if (cartItems.isEmpty) {
        items.value = [];
        return;
      }
      
      final response = await apiService.apiClient.items.getItemsByIds(
        ids: cartItems.join(','),
        regionId: authService.currentStore.value?.cityId,
      );
      items.value = response.data ?? [];
    } catch (e) {
      hasError.value = true;
      MySnackbar.show(
        title: 'خطأ',
        message: 'فشل في تحميل العناصر',
        backgroundColor: Colors.red.shade100,
        textColor: Colors.red.shade800,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchWholesaler() async {
    try {
      final response = await apiService.apiClient.wholesalers.getWholesaler(wholesalerId);
      wholesaler.value = response.data;
    } catch (e) {
      // Wholesaler fetch failure is not critical
    }
  }

  Future<void> loadMinChargeInfo() async {
    if (!hasCart || authService.currentStore.value?.cityId == null) {
      return;
    }

    try {
      isLoadingMinCharge.value = true;
      minChargeError.value = false;
      
      final regionId = authService.currentStore.value!.cityId;
      final response = await apiService.apiClient.wholesalers.listMinCharges(
        regionId: regionId,
        wholesalerId: wholesalerId,
      );
      
      if (response.data != null && response.data!.isNotEmpty) {
        minChargeInfo.value = response.data!.first;
      }
    } catch (e) {
      minChargeError.value = true;
    } finally {
      isLoadingMinCharge.value = false;
    }
  }

  void updateItemQuantity(int itemId, int newQuantity) {
    final cartItem = currentCart.firstWhereOrNull(
      (item) => item.item.id == itemId,
    );
    
    if (cartItem != null) {
      if (newQuantity <= 0) {
        removeItem(itemId);
      } else {
        cartItem.quantity.value = newQuantity;
      }
    }
  }

  void removeItem(int itemId) {
    cartService.removeItemFromCart(wholesalerId, itemId);
    
    // Refresh items and min charge if cart becomes empty
    if (!hasCart) {
      minChargeInfo.value = null;
      items.value = [];
    } else {
      fetchItems();
    }
  }

  void clearCart() {
    cartService.clearCart(wholesalerId);
    items.value = [];
    minChargeInfo.value = null;
  }

  void showMinChargeError() {
    if (minChargeInfo.value == null) return;
    
    final minCharge = minChargeInfo.value!;
    final missingAmount = minCharge.minCharge - totalPrice;
    final missingItems = minCharge.minItems - itemCount;
    
    String message = '';
    if (missingAmount > 0 && missingItems > 0) {
      message = 'تحتاج إلى إضافة ${missingAmount.toStringAsFixed(2)} جنيه و $missingItems منتجات أخرى';
    } else if (missingAmount > 0) {
      message = 'تحتاج إلى إضافة ${missingAmount.toStringAsFixed(2)} جنيه أخرى';
    } else if (missingItems > 0) {
      message = 'تحتاج إلى إضافة $missingItems منتجات أخرى';
    }
    
    if (message.isNotEmpty) {
      MySnackbar.show(
        title: 'الحد الأدنى للطلب',
        message: message,
        backgroundColor: Colors.orange.shade100,
        textColor: Colors.orange.shade800,
      );
    }
  }

  Future<void> refreshCart() async {
    await _initializeCart();
  }

  void navigateToWholesaler() {
    // Navigate to wholesaler products page
    if (wholesalerId > 0) {
      Get.to(() => WholesalerDetailsPage(id: wholesalerId));
    }
  }

  void navigateToCheckout() {
    if (canProceedToCheckout) {
      // Show confirmation dialog first, then navigate to checkout page
      Get.dialog(
        AlertDialog(
          title: const Text(
            'تأكيد المتابعة',
            style: TextStyle(fontFamily: 'Gilroy'),
          ),
          content: Text(
            'سيتم المتابعة لاختيار موعد التوصيل لإجمالي (${totalPrice.toStringAsFixed(2)} جنيه). هل تريد المتابعة؟',
            style: const TextStyle(fontFamily: 'Gilroy'),
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text(
                'إلغاء',
                style: TextStyle(fontFamily: 'Gilroy'),
              ),
            ),
            TextButton(
              onPressed: () {
                Get.back();
                Get.to(() => CheckoutPage(wholesalerId: wholesalerId));
              },
              child: const Text(
                'متابعة',
                style: TextStyle(fontFamily: 'Gilroy', fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
      );
    } else {
      showMinChargeError();
    }
  }

  void navigateBack() {
    Get.back();
  }
}
