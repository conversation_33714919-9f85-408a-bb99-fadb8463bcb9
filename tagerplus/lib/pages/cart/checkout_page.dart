import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'package:tagerplus/core/utils/snackbar.dart';
import 'package:tagerplus/services/api.dart';
import 'package:tagerplus/services/auth.dart';
import 'package:tagerplus/services/cart.dart';
import 'package:tagerplus/pages/cart/widgets/cart_totals_card.dart';
import 'package:tagerplus/core/api/models/order_in.dart';
import 'package:tagerplus/core/api/models/order_item_in.dart';
import 'package:tagerplus/pages/home/<USER>';

class CheckoutPage extends StatefulWidget {
  final int wholesalerId;
  const CheckoutPage({super.key, required this.wholesalerId});

  @override
  State<CheckoutPage> createState() => _CheckoutPageState();
}

class _CheckoutPageState extends State<CheckoutPage> {
  DateTime? _selectedDeliveryDate;
  bool _isSubmitting = false;

  // Services
  final CartService _cartService = Get.find<CartService>();
  final AuthService _authService = Get.find<AuthService>();
  final ApiService _apiService = Get.find<ApiService>();

  @override
  void initState() {
    super.initState();
    _selectedDeliveryDate = DateTime.now().add(const Duration(days: 1));
  }

  List<CartItem> get _currentCartItems =>
      _cartService.cart[widget.wholesalerId]?.toList() ?? <CartItem>[];

  bool get _hasCart => _currentCartItems.isNotEmpty;

  double get _totalPrice => _cartService.getCartTotalPrice(widget.wholesalerId);

  int get _itemCount => _cartService.getCartItemCount(widget.wholesalerId);

  

  Future<void> _submitOrder() async {
    if (!_hasCart) {
      MySnackbar.show(
        title: 'السلة فارغة',
        message: 'لا توجد منتجات لإكمال الطلب',
        backgroundColor: Colors.red.shade100,
        textColor: Colors.red.shade800,
      );
      return;
    }
    if (_authService.currentStore.value == null) {
      MySnackbar.show(
        title: 'لا يوجد متجر',
        message: 'يرجى اختيار/إنشاء متجر أولاً',
        backgroundColor: Colors.red.shade100,
        textColor: Colors.red.shade800,
      );
      return;
    }

    setState(() => _isSubmitting = true);
    // try {
    final items = _currentCartItems
        .map(
          (ci) => OrderItemIn(itemId: ci.item.id, quantity: ci.quantity.value),
        )
        .toList();

    final orderIn = OrderIn(
      wholesalerId: widget.wholesalerId,
      // StoreSchema doesn't expose an 'id', using ownerId as backend's expected identifier
      storeId: _authService.currentStore.value!.id,
      items: items,
      deliverAt: _selectedDeliveryDate?.toIso8601String(),
    );

    await _apiService.apiClient.orders.createOrder(orderIn);

    _cartService.clearCart(widget.wholesalerId);

    if (mounted) {
      // Navigate to home then show success message
      await Get.offAll(() => const HomePage());
      MySnackbar.show(
        title: 'تم إرسال الطلب',
        message: 'تم إنشاء طلبك بنجاح',
        backgroundColor: Colors.green.shade100,
        textColor: Colors.green.shade800,
      );
    }
    // } catch (e) {
    //   MySnackbar.show(
    //     title: 'خطأ',
    //     message: 'حدث خطأ أثناء إنشاء الطلب',
    //     backgroundColor: Colors.red.shade100,
    //     textColor: Colors.red.shade800,
    //   );
    // } finally {
    //   if (mounted) setState(() => _isSubmitting = false);
    // }
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: const Text(
            'إكمال الطلب',
            style: TextStyle(fontFamily: 'Gilroy', fontWeight: FontWeight.bold),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios),
            onPressed: () => Get.back(),
          ),
          centerTitle: true,
        ),
        body: !_hasCart
            ? _buildEmptyState(context)
            : SingleChildScrollView(
                padding: const EdgeInsets.all(AppDefaults.padding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDeliveryTimeCard(context),
                    const SizedBox(height: AppDefaults.padding),
                    _buildStoreInfoCard(context),
                    const SizedBox(height: AppDefaults.padding),
                    CartTotalsCard(
                      subtotal: _totalPrice,
                      total: _totalPrice,
                      itemCount: _itemCount,
                    ),
                    const SizedBox(height: AppDefaults.padding * 2),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isSubmitting ? null : _submitOrder,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            vertical: AppDefaults.padding + 4,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: AppDefaults.borderRadius,
                          ),
                        ),
                        child: _isSubmitting
                            ? const SizedBox(
                                width: 22,
                                height: 22,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                            : const Text(
                                'تأكيد الطلب',
                                style: TextStyle(
                                  fontFamily: 'Gilroy',
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildDeliveryTimeCard(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: AppDefaults.borderRadius,
        boxShadow: AppDefaults.boxShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.calendar_today_outlined,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'موعد التوصيل',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Gilroy',
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Builder(
            builder: (context) {
              final now = DateTime.now();
              final base = DateTime(now.year, now.month, now.day);
              final tomorrow = base.add(const Duration(days: 1));
              final dayAfter = base.add(const Duration(days: 2));

              Widget buildOption({
                required DateTime date,
                required String title,
                required String subtitle,
              }) {
                final selected = _selectedDeliveryDate != null && _isSameDay(_selectedDeliveryDate!, date);
                final scheme = Theme.of(context).colorScheme;
                return GestureDetector(
                  onTap: () {
                    setState(() => _selectedDeliveryDate = date);
                    HapticFeedback.selectionClick();
                  },
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 150),
                    padding: const EdgeInsets.all(AppDefaults.padding),
                    decoration: BoxDecoration(
                      color: selected
                          ? scheme.primary.withOpacity(0.06)
                          : scheme.surface,
                      borderRadius: AppDefaults.borderRadius,
                      border: Border.all(
                        color: selected ? scheme.primary : scheme.outline.withOpacity(0.3),
                        width: selected ? 1.4 : 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              selected ? Icons.check_circle : Icons.radio_button_unchecked,
                              size: 18,
                              color: selected ? scheme.primary : scheme.onSurfaceVariant,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              title,
                              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                    fontFamily: 'Gilroy',
                                    fontWeight: FontWeight.w700,
                                    color: selected ? scheme.primary : scheme.onSurface,
                                  ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 6),
                        Text(
                          subtitle,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                fontFamily: 'Gilroy',
                                color: scheme.onSurfaceVariant,
                              ),
                        ),
                      ],
                    ),
                  ),
                );
              }

              return Row(
                children: [
                  Expanded(
                    child: buildOption(
                      date: tomorrow,
                      title: 'غدًا',
                      subtitle: _formatDate(tomorrow),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: buildOption(
                      date: dayAfter,
                      title: 'بعد غد',
                      subtitle: _formatDate(dayAfter),
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppDefaults.padding * 2),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(AppDefaults.padding * 2),
              decoration: BoxDecoration(
                color: Theme.of(
                  context,
                ).colorScheme.primaryContainer.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.shopping_cart_outlined,
                size: 80,
                color: Theme.of(context).colorScheme.primary.withOpacity(0.6),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'سلة التسوق فارغة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
                fontFamily: 'Gilroy',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              'أضف منتجات لإكمال الطلب',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontFamily: 'Gilroy',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () => Get.back(),
              icon: const Icon(Icons.shopping_bag_outlined),
              label: const Text('العودة للتسوق'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStoreInfoCard(BuildContext context) {
    final store = _authService.currentStore.value;
    return Container(
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: AppDefaults.borderRadius,
        boxShadow: AppDefaults.boxShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.store_mall_directory_outlined,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'المتجر',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Gilroy',
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (store != null)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  store.name,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w700,
                    fontFamily: 'Gilroy',
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  store.address,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            )
          else
            Text(
              'لم يتم العثور على متجر حالي',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
        ],
      ),
    );
  }
}
