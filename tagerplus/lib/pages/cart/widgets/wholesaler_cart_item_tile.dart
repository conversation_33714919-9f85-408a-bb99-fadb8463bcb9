import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tagerplus/core/api/models/item_schema.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'package:tagerplus/core/utils/snackbar.dart';
import 'package:tagerplus/services/cart.dart';

class WholesalerCartItemTile extends StatelessWidget {
  final CartItem cartItem;
  final ItemSchema? itemSchema;
  final Function(int) onQuantityChanged;
  final VoidCallback onRemove;

  const WholesalerCartItemTile({
    super.key,
    required this.cartItem,
    this.itemSchema,
    required this.onQuantityChanged,
    required this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    final item = cartItem.item;
    final quantity = cartItem.quantity.value;
    final lineTotal = item.basePrice * quantity;

    return Container(
      padding: const EdgeInsets.all(AppDefaults.padding),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product Image
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Container(
                child: itemSchema?.product?.image != null
                    ? Image.network(
                        itemSchema!.product!.image!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Icon(
                          Icons.inventory_2_outlined,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                          size: 32,
                        ),
                      )
                    : Icon(
                        Icons.inventory_2_outlined,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                        size: 32,
                      ),
              ),
            ),
          ),

          const SizedBox(width: 12),

          // Product Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.product.title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Gilroy',
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                if (itemSchema != null) ...[
                  Text(
                    'الحد الأدنى: ${itemSchema!.minimumOrderQuantity} قطعة',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      fontFamily: 'Gilroy',
                    ),
                  ),
                  const SizedBox(height: 4),
                ],
                Row(
                  children: [
                    Text(
                      '${item.basePrice.toStringAsFixed(2)} جنيه',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Gilroy',
                      ),
                    ),
                    Text(
                      ' / قطعة',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                        fontFamily: 'Gilroy',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'الإجمالي: ${lineTotal.toStringAsFixed(2)} جنيه',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Gilroy',
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: 12),

          // Quantity Controls
          Column(
            children: [
              // Quantity Stepper
              Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(
                      context,
                    ).colorScheme.outline.withOpacity(0.3),
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Decrease Button
                    InkWell(
                      onTap: quantity > 0
                          ? () {
                              HapticFeedback.lightImpact();
                              onQuantityChanged(quantity - 1);
                            }
                          : null,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        bottomLeft: Radius.circular(8),
                      ),
                      child: Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: quantity > 0
                              ? Theme.of(context).colorScheme.surfaceVariant
                              : Theme.of(
                                  context,
                                ).colorScheme.surfaceVariant.withOpacity(0.3),
                        ),
                        child: Icon(
                          Icons.remove,
                          size: 16,
                          color: quantity > 0
                              ? Theme.of(context).colorScheme.onSurfaceVariant
                              : Theme.of(
                                  context,
                                ).colorScheme.onSurfaceVariant.withOpacity(0.5),
                        ),
                      ),
                    ),

                    // Quantity Display
                    Container(
                      width: 40,
                      height: 32,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                      ),
                      child: Center(
                        child: Text(
                          quantity.toString(),
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                fontFamily: 'Gilroy',
                              ),
                        ),
                      ),
                    ),

                    // Increase Button
                    InkWell(
                      onTap: () {
                        // Enforce maximum quantity
                        final int inventory =
                            itemSchema?.inventoryCount ?? 999999;
                        final int? maxOrder = itemSchema?.maximumOrderQuantity;
                        final int maxAllowed =
                            (maxOrder != null && maxOrder > 0)
                            ? maxOrder
                            : inventory;

                        if (quantity >= maxAllowed) {
                          HapticFeedback.mediumImpact();
                          MySnackbar.show(
                            title: 'حد الكمية',
                            message:
                                'لا يمكن إضافة أكثر من $maxAllowed قطعة لهذا المنتج',
                          );
                          return;
                        }

                        HapticFeedback.lightImpact();
                        onQuantityChanged(quantity + 1);
                      },
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(8),
                        bottomRight: Radius.circular(8),
                      ),
                      child: Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        child: Icon(
                          Icons.add,
                          size: 16,
                          color: Theme.of(context).colorScheme.onPrimary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
