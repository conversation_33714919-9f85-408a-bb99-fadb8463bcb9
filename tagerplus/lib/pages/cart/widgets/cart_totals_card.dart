import 'package:flutter/material.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';

class CartTotalsCard extends StatelessWidget {
  final double subtotal;
  final double total;
  final int itemCount;

  const CartTotalsCard({
    super.key,
    required this.subtotal,
    required this.total,
    required this.itemCount,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: AppDefaults.borderRadius,
        boxShadow: AppDefaults.boxShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.receipt_outlined,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'ملخص الطلب',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  fontFamily: '<PERSON>roy',
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Items Count
          _buildSummaryRow(
            context,
            label: 'عدد المنتجات',
            value: '$itemCount منتج',
            isSubtotal: true,
          ),

          const SizedBox(height: 8),

          // Subtotal
          _buildSummaryRow(
            context,
            label: 'المجموع الفرعي',
            value: '${subtotal.toStringAsFixed(2)} جنيه',
            isSubtotal: true,
          ),

          const SizedBox(height: 12),

          // Divider
          Divider(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
          ),

          const SizedBox(height: 12),

          // Total
          _buildSummaryRow(
            context,
            label: 'الإجمالي',
            value: '${total.toStringAsFixed(2)} جنيه',
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(
    BuildContext context, {
    required String label,
    required String value,
    bool isSubtotal = false,
    bool isTotal = false,
    Color? valueColor,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
            color: isTotal
                ? Theme.of(context).colorScheme.onSurface
                : Theme.of(context).colorScheme.onSurfaceVariant,
            fontFamily: 'Gilroy',
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: isTotal ? FontWeight.bold : FontWeight.w600,
            color:
                valueColor ??
                (isTotal
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurface),
            fontSize: isTotal ? 16 : null,
            fontFamily: 'Gilroy',
          ),
        ),
      ],
    );
  }
}
