import 'package:flutter/material.dart';
import 'package:tagerplus/core/api/models/region_min_charge_schema.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';

class CartMinChargeCard extends StatelessWidget {
  final RegionMinChargeSchema minChargeInfo;
  final double currentTotal;
  final int currentItems;

  const CartMinChargeCard({
    super.key,
    required this.minChargeInfo,
    required this.currentTotal,
    required this.currentItems,
  });

  @override
  Widget build(BuildContext context) {
    final priceProgress = (currentTotal / minChargeInfo.minCharge).clamp(0.0, 1.0);
    final itemsProgress = (currentItems / minChargeInfo.minItems).clamp(0.0, 1.0);
    final isPriceMetMin = currentTotal >= minChargeInfo.minCharge;
    final isItemsMetMin = currentItems >= minChargeInfo.minItems;
    final isAllRequirementsMet = isPriceMetMin && isItemsMetMin;

    return Container(
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: isAllRequirementsMet 
            ? Colors.green.shade50 
            : Colors.orange.shade50,
        borderRadius: AppDefaults.borderRadius,
        border: Border.all(
          color: isAllRequirementsMet 
              ? Colors.green.shade200 
              : Colors.orange.shade200,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                isAllRequirementsMet 
                    ? Icons.check_circle_outline 
                    : Icons.info_outline,
                color: isAllRequirementsMet 
                    ? Colors.green.shade600 
                    : Colors.orange.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'الحد الأدنى للطلب',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: isAllRequirementsMet 
                      ? Colors.green.shade700 
                      : Colors.orange.shade700,
                  fontFamily: 'Gilroy',
                ),
              ),
              const Spacer(),
              if (isAllRequirementsMet)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'مكتمل',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.green.shade700,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Gilroy',
                    ),
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Price Progress
          _buildProgressSection(
            context,
            title: 'قيمة الطلب',
            current: currentTotal,
            required: minChargeInfo.minCharge,
            progress: priceProgress,
            isMet: isPriceMetMin,
            unit: 'جنيه',
          ),
          
          const SizedBox(height: 12),
          
          // Items Progress
          _buildProgressSection(
            context,
            title: 'عدد المنتجات',
            current: currentItems.toDouble(),
            required: minChargeInfo.minItems.toDouble(),
            progress: itemsProgress,
            isMet: isItemsMetMin,
            unit: 'منتج',
            isInteger: true,
          ),
          
          if (!isAllRequirementsMet) ...[
            const SizedBox(height: 16),
            _buildActionHint(context),
          ],
        ],
      ),
    );
  }

  Widget _buildProgressSection(
    BuildContext context, {
    required String title,
    required double current,
    required double required,
    required double progress,
    required bool isMet,
    required String unit,
    bool isInteger = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                fontFamily: 'Gilroy',
              ),
            ),
            Text(
              isInteger 
                  ? '${current.toInt()}/${required.toInt()} $unit'
                  : '${current.toStringAsFixed(2)}/${required.toStringAsFixed(2)} $unit',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: isMet 
                    ? Colors.green.shade700 
                    : Colors.orange.shade700,
                fontWeight: FontWeight.bold,
                fontFamily: 'Gilroy',
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
          valueColor: AlwaysStoppedAnimation<Color>(
            isMet ? Colors.green.shade600 : Colors.orange.shade600,
          ),
          borderRadius: BorderRadius.circular(4),
          minHeight: 6,
        ),
      ],
    );
  }

  Widget _buildActionHint(BuildContext context) {
    final missingAmount = minChargeInfo.minCharge - currentTotal;
    final missingItems = minChargeInfo.minItems - currentItems;
    
    String hintText = '';
    if (missingAmount > 0 && missingItems > 0) {
      hintText = 'أضف ${missingAmount.toStringAsFixed(2)} جنيه و $missingItems منتجات أخرى';
    } else if (missingAmount > 0) {
      hintText = 'أضف ${missingAmount.toStringAsFixed(2)} جنيه أخرى';
    } else if (missingItems > 0) {
      hintText = 'أضف $missingItems منتجات أخرى';
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.add_shopping_cart,
            color: Colors.orange.shade700,
            size: 18,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              hintText,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.orange.shade700,
                fontWeight: FontWeight.w600,
                fontFamily: 'Gilroy',
              ),
            ),
          ),
        ],
      ),
    );
  }
}
