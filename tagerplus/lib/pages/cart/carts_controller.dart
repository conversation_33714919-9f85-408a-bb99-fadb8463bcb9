import 'package:get/get.dart';
import 'package:tagerplus/core/utils/snackbar.dart';
import 'package:tagerplus/core/api/models/models.dart';
import 'package:tagerplus/services/api.dart';
import 'package:tagerplus/services/cart.dart';

class CartsController extends GetxController {
  final cartService = Get.find<CartService>();

  final wholesalers = <WholesalerSchema>[].obs;
  final isLoading = true.obs;
  final hasError = false.obs;

  @override
  void onInit() {
    super.onInit();
    fetchWholesalers();
    cartService.cart.addListener(() {
      if (wholesalers.isEmpty) fetchWholesalers();
    });
  }

  Future<void> fetchWholesalers() async {
    try {
      isLoading.value = true;
      hasError.value = false;
      final ids = cartService.cart.keys.toList();
      if (ids.isEmpty) {
        wholesalers.value = [];
        isLoading.value = false;
        return;
      }
      final List<WholesalerSchema> fetchedWholesalers = [];
      for (var id in ids) {
        final response = await ApiService.to.apiClient.wholesalers
            .getWholesaler(id);
        if (response.data != null) {
          fetchedWholesalers.add(response.data!);
        }
      }
      wholesalers.value = fetchedWholesalers;
    } catch (e) {
      hasError.value = true;
      MySnackbar.show(title: 'Error', message: 'Failed to fetch wholesalers');
    } finally {
      isLoading.value = false;
    }
  }

  double getCartTotalPrice(int wholesalerId) {
    return cartService.getCartTotalPrice(wholesalerId);
  }

  int getCartItemCount(int wholesalerId) {
    return cartService.getCartItemCount(wholesalerId);
  }

  int get totalCartsCount => cartService.cart.length;

  double get totalPriceAcrossAllCarts => cartService.totalPriceAcrossAllCarts;

  int get totalItemsAcrossAllCarts => cartService.totalItemsAcrossAllCarts;
}
