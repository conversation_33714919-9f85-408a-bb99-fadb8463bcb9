import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'package:tagerplus/pages/cart/cart_details_controller.dart';
import 'package:tagerplus/pages/cart/widgets/cart_wholesaler_header.dart';
import 'package:tagerplus/pages/cart/widgets/wholesaler_cart_item_tile.dart';
import 'package:tagerplus/pages/cart/widgets/cart_min_charge_card.dart';
import 'package:tagerplus/pages/cart/widgets/cart_totals_card.dart';
import 'package:tagerplus/services/cart.dart';

class CartDetailsPage extends StatelessWidget {
  final int wholesalerId;
  final bool isHomePage;
  
  const CartDetailsPage({
    super.key,
    required this.wholesalerId,
    this.isHomePage = false,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(
      CartDetailsController(
        wholesalerId: wholesalerId,
        isHomePage: isHomePage,
      ),
    );

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        appBar: isHomePage ? null : _buildAppBar(context, controller),
        body: GetX<CartDetailsController>(
          builder: (controller) {
            if (controller.isLoading.value) {
              return _buildLoadingState(context);
            }

            if (controller.hasError.value) {
              return _buildErrorState(context, controller);
            }

            if (!controller.hasCart) {
              return _buildEmptyState(context);
            }

            return _buildCartContent(context, controller);
          },
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context, CartDetailsController controller) {
    return AppBar(
      title: Text(
        'سلة التسوق',
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
          fontFamily: 'Gilroy',
        ),
      ),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios),
        onPressed: controller.navigateBack,
      ),
      actions: [
        if (controller.hasCart)
          IconButton(
            icon: Icon(
              Icons.delete_outline,
              color: Colors.red.shade600,
            ),
            onPressed: () => _showClearCartDialog(context, controller),
          ),
      ],
      elevation: 0,
      backgroundColor: Theme.of(context).colorScheme.surface,
      foregroundColor: Theme.of(context).colorScheme.onSurface,
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل السلة...',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              fontFamily: 'Gilroy',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, CartDetailsController controller) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppDefaults.padding * 2),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: Colors.red.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ في تحميل السلة',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.red.shade700,
                fontFamily: 'Gilroy',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'يرجى المحاولة مرة أخرى',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontFamily: 'Gilroy',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: controller.refreshCart,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDefaults.padding * 2,
                  vertical: AppDefaults.padding,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: AppDefaults.borderRadius,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppDefaults.padding * 2),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(AppDefaults.padding * 2),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.shopping_cart_outlined,
                size: 80,
                color: Theme.of(context).colorScheme.primary.withOpacity(0.6),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'سلة التسوق فارغة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
                fontFamily: 'Gilroy',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              'أضف منتجات إلى سلة التسوق للمتابعة',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontFamily: 'Gilroy',
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                // Navigate back to shopping
                if (isHomePage) {
                  // EntryPointUIState.onBottomNavigationTap(0);
                } else {
                  Get.back();
                }
              },
              icon: const Icon(Icons.shopping_bag_outlined),
              label: const Text('العودة للتسوق'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDefaults.padding * 2,
                  vertical: AppDefaults.padding,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: AppDefaults.borderRadius,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCartContent(BuildContext context, CartDetailsController controller) {
    return SafeArea(
      child: RefreshIndicator(
        onRefresh: controller.refreshCart,
        color: Theme.of(context).colorScheme.primary,
        child: CustomScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          slivers: [
            SliverPadding(
              padding: const EdgeInsets.all(AppDefaults.padding),
              sliver: SliverList(
                delegate: SliverChildListDelegate([
                  // Wholesaler Header
                  AnimatedSwitcher(
                    duration: AppDefaults.duration,
                    child: controller.wholesaler.value != null
                        ? CartWholesalerHeader(
                            wholesaler: controller.wholesaler.value!,
                          )
                        : const SizedBox.shrink(),
                  ),
                  
                  const SizedBox(height: AppDefaults.padding),
                  
                  // Back to Wholesaler Button
                  if (controller.wholesaler.value != null)
                    _buildWholesalerButton(context, controller),
                  
                  const SizedBox(height: AppDefaults.padding),
                  
                  // Cart Items List
                  _buildCartItemsList(context, controller),
                  
                  const SizedBox(height: AppDefaults.padding),
                  
                  // Min Charge Card
                  _buildMinChargeSection(context, controller),
                  
                  const SizedBox(height: AppDefaults.padding),
                  
                  // Totals Card
                  CartTotalsCard(
                    subtotal: controller.totalPrice,
                    total: controller.totalPrice,
                    itemCount: controller.itemCount,
                  ),
                  
                  const SizedBox(height: AppDefaults.padding * 2),
                  
                  // Checkout Button
                  _buildCheckoutButton(context, controller),
                  
                  const SizedBox(height: AppDefaults.padding),
                ]),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWholesalerButton(BuildContext context, CartDetailsController controller) {
    return OutlinedButton.icon(
      onPressed: controller.navigateToWholesaler,
      icon: const Icon(Icons.store_outlined),
      label: const Text('العودة إلى متجر التاجر'),
      style: OutlinedButton.styleFrom(
        foregroundColor: Theme.of(context).colorScheme.primary,
        side: BorderSide(color: Theme.of(context).colorScheme.outline),
        padding: const EdgeInsets.symmetric(
          horizontal: AppDefaults.padding,
          vertical: AppDefaults.padding,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(50),
        ),
      ),
    );
  }

  Widget _buildCartItemsList(BuildContext context, CartDetailsController controller) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: AppDefaults.borderRadius,
        boxShadow: AppDefaults.boxShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDefaults.padding),
            child: Row(
              children: [
                Icon(
                  Icons.shopping_cart_outlined,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'المنتجات في السلة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Gilroy',
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${controller.itemCount} منتج',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Gilroy',
                    ),
                  ),
                ),
              ],
            ),
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: controller.currentCart.length,
            separatorBuilder: (context, index) => Divider(
              height: 1,
              color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            ),
            itemBuilder: (context, index) {
              final cartItem = controller.currentCart[index];
              final itemSchema = controller.items.firstWhereOrNull(
                (item) => item.id == cartItem.item.id,
              );
              
              return AnimatedContainer(
                duration: AppDefaults.duration,
                child: WholesalerCartItemTile(
                  cartItem: cartItem,
                  itemSchema: itemSchema,
                  onQuantityChanged: (newQuantity) {
                    HapticFeedback.lightImpact();
                    controller.updateItemQuantity(cartItem.item.id, newQuantity);
                  },
                  onRemove: () {
                    HapticFeedback.mediumImpact();
                    _showRemoveItemDialog(context, controller, cartItem);
                  },
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMinChargeSection(BuildContext context, CartDetailsController controller) {
    return AnimatedSwitcher(
      duration: AppDefaults.duration,
      child: controller.isLoadingMinCharge.value
          ? _buildMinChargeLoading(context)
          : controller.minChargeError.value
              ? _buildMinChargeError(context, controller)
              : controller.minChargeInfo.value != null
                  ? CartMinChargeCard(
                      minChargeInfo: controller.minChargeInfo.value!,
                      currentTotal: controller.totalPrice,
                      currentItems: controller.itemCount,
                    )
                  : const SizedBox.shrink(),
    );
  }

  Widget _buildMinChargeLoading(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: AppDefaults.borderRadius,
        boxShadow: AppDefaults.boxShadow,
      ),
      child: Row(
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            'جاري تحميل معلومات الحد الأدنى...',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              fontFamily: 'Gilroy',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMinChargeError(BuildContext context, CartDetailsController controller) {
    return Container(
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: AppDefaults.borderRadius,
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red.shade600,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'فشل في تحميل معلومات الحد الأدنى',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.red.shade700,
                fontFamily: 'Gilroy',
              ),
            ),
          ),
          TextButton(
            onPressed: controller.loadMinChargeInfo,
            child: Text(
              'إعادة المحاولة',
              style: TextStyle(
                color: Colors.red.shade700,
                fontWeight: FontWeight.bold,
                fontFamily: 'Gilroy',
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCheckoutButton(BuildContext context, CartDetailsController controller) {
    final canProceed = controller.canProceedToCheckout;
    
    return AnimatedContainer(
      duration: AppDefaults.duration,
      width: double.infinity,
      child: ElevatedButton(
        onPressed: canProceed ? controller.navigateToCheckout : () {
          HapticFeedback.mediumImpact();
          controller.showMinChargeError();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: canProceed
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.3),
          foregroundColor: canProceed
              ? Theme.of(context).colorScheme.onPrimary
              : Theme.of(context).colorScheme.onSurfaceVariant,
          padding: const EdgeInsets.symmetric(
            vertical: AppDefaults.padding + 4,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: AppDefaults.borderRadius,
          ),
          elevation: canProceed ? 2 : 0,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              canProceed ? Icons.shopping_cart_checkout : Icons.warning_outlined,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              canProceed ? 'متابعة الدفع' : 'لا يمكن المتابعة',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                fontFamily: 'Gilroy',
              ),
            ),
            if (canProceed) ...[
              const SizedBox(width: 8),
              Text(
                '(${controller.totalPrice.toStringAsFixed(2)} جنيه)',
                style: const TextStyle(
                  fontSize: 14,
                  fontFamily: 'Gilroy',
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showClearCartDialog(BuildContext context, CartDetailsController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'تأكيد الحذف',
          style: TextStyle(fontFamily: 'Gilroy'),
        ),
        content: const Text(
          'هل أنت متأكد من رغبتك في حذف جميع المنتجات من السلة؟',
          style: TextStyle(fontFamily: 'Gilroy'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'إلغاء',
              style: TextStyle(fontFamily: 'Gilroy'),
            ),
          ),
          TextButton(
            onPressed: () {
              controller.clearCart();
              Navigator.of(context).pop();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text(
              'حذف الكل',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontFamily: 'Gilroy',
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showRemoveItemDialog(BuildContext context, CartDetailsController controller, CartItem cartItem) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'حذف المنتج',
          style: TextStyle(fontFamily: 'Gilroy'),
        ),
        content: Text(
          'هل تريد حذف "${cartItem.item.product.title}" من السلة؟',
          style: const TextStyle(fontFamily: 'Gilroy'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'إلغاء',
              style: TextStyle(fontFamily: 'Gilroy'),
            ),
          ),
          TextButton(
            onPressed: () {
              controller.removeItem(cartItem.item.id);
              Navigator.of(context).pop();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text(
              'حذف',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontFamily: 'Gilroy',
              ),
            ),
          ),
        ],
      ),
    );
  }
}
