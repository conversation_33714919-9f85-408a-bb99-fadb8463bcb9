import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'package:tagerplus/core/utils/currency_formatter.dart';
import 'package:tagerplus/pages/cart/carts_controller.dart';
import 'package:tagerplus/pages/cart/cart_details.dart';
import 'package:tagerplus/pages/cart/widgets/cart_summary_card.dart';
import 'package:tagerplus/pages/wholesaler/wholesaler_details.dart';

class CartsPage extends StatelessWidget {
  const CartsPage({super.key});

  @override
  Widget build(BuildContext context) {
    Get.put(CartsController());
    return Scaffold(
      appBar: AppBar(
        title: const Text('عربات التسوق'),
      ),
      body: GetX<CartsController>(
        builder: (controller) {
          if (controller.isLoading.value) {
            return const Center(child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            ));
          }

          if (controller.hasError.value) {
            return const Center(child: Text('فشل تحميل عربات التسوق.'));
          }

          if (controller.wholesalers.isEmpty) {
            return _buildEmptyState(context);
          }

          return Column(
            children: [
              _buildSummaryHeader(context, controller),
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(
                      horizontal: AppDefaults.margin),
                  itemCount: controller.wholesalers.length,
                  itemBuilder: (context, index) {
                    final wholesaler = controller.wholesalers[index];
                    return CartSummaryCard(
                      wholesaler: wholesaler,
                      itemCount: controller.getCartItemCount(wholesaler.id),
                      totalPrice: controller.getCartTotalPrice(wholesaler.id),
                      onTap: () {
                        Get.to(() => CartDetailsPage(wholesalerId: wholesaler.id));
                      },
                      onWholesalerTap: () {
                        Get.to(() => WholesalerDetailsPage(id: wholesaler.id));
                      },
                      onClear: () {
                        _showClearCartDialog(context, controller, wholesaler.id);
                      },
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSummaryHeader(
      BuildContext context, CartsController controller) {
    return Container(
      padding: const EdgeInsets.all(AppDefaults.padding),
      margin: const EdgeInsets.symmetric(
        horizontal: AppDefaults.margin,
        vertical: AppDefaults.margin / 2,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppDefaults.radius),
        boxShadow: AppDefaults.boxShadow,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'إجمالي العربات',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
              const SizedBox(height: 4),
              Text(
                '${controller.wholesalers.length} عربة',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
              ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                'الإجمالي',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
              const SizedBox(height: 4),
              Text(
                CurrencyFormatter.formatPrice(controller.cartService.totalPriceAcrossAllCarts),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showClearCartDialog(
      BuildContext context, CartsController controller, int wholesalerId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من رغبتك في حذف عربة التسوق هذه؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              controller.cartService.clearCart(wholesalerId);
              controller.fetchWholesalers(); // Refresh the list
              Navigator.of(context).pop();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 80,
            color: Colors.grey[300],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد عربات بعد',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر عربات التسوق الخاصة بك هنا',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[500],
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
