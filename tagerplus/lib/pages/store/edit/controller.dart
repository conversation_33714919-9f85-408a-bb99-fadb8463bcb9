import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:tagerplus/core/utils/snackbar.dart';
import '../../../core/api/api.dart' as api;
import '../../../services/api.dart';
import '../../../services/auth.dart';

class EditStoreController extends GetxController {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  // Text controllers
  final TextEditingController nameController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController addressController = TextEditingController();

  // Private reactive variables
  final RxBool _isLoading = false.obs;
  final RxBool _isLoadingRegions = false.obs;
  final RxList<api.RegionSchema> _regions = <api.RegionSchema>[].obs;
  final Rx<api.RegionSchema?> _selectedRegion = Rx<api.RegionSchema?>(null);

  // Getters
  bool get isLoading => _isLoading.value;
  bool get isLoadingRegions => _isLoadingRegions.value;
  List<api.RegionSchema> get regions => _regions;
  api.RegionSchema? get selectedRegion => _selectedRegion.value;

  // Setters
  set isLoading(bool v) => _isLoading.value = v;
  set isLoadingRegions(bool v) => _isLoadingRegions.value = v;
  set selectedRegion(api.RegionSchema? v) => _selectedRegion.value = v;

  @override
  void onInit() {
    super.onInit();
    // Pre-fill from current store
    final store = AuthService.to.currentStore.value;
    if (store != null) {
      nameController.text = store.name;
      descriptionController.text = store.description;
      addressController.text = store.address;
    }
    loadRegions(
      initialIds: (
        cityId: store?.cityId,
        stateId: store?.stateId,
        countryId: store?.countryId,
      ),
    );
  }

  /// Load available regions and select the current one
  Future<void> loadRegions({
    ({int? cityId, int? stateId, int? countryId})? initialIds,
  }) async {
    if (isLoadingRegions) return;

    isLoadingRegions = true;
    try {
      final regionsList = await ApiService.to.apiClient.regions.getCities();
      _regions.value = regionsList.data!;

      // Try to select by ID preference city > state > country
      if (initialIds != null) {
        final byCity = initialIds.cityId != null
            ? _regions.firstWhereOrNull((r) => r.id == initialIds.cityId)
            : null;
        final byState = initialIds.stateId != null
            ? _regions.firstWhereOrNull((r) => r.id == initialIds.stateId)
            : null;
        final byCountry = initialIds.countryId != null
            ? _regions.firstWhereOrNull((r) => r.id == initialIds.countryId)
            : null;
        _selectedRegion.value = byCity ?? byState ?? byCountry;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading regions: $e');
      }
      MySnackbar.show(title: 'خطأ', message: 'مشكلة في الاتصال');
    } finally {
      isLoadingRegions = false;
    }
  }

  // Validation
  String? validateName(String? value) {
    if (value == null || value.trim().isEmpty)
      return 'اكتب اسم المتجر لو سمحت';
    if (value.trim().length < 2) return 'الاسم قصير';
    return null;
  }

  String? validateAddress(String? value) {
    if (value == null || value.trim().isEmpty)
      return 'اكتب عنوان المتجر لو سمحت';
    if (value.trim().length < 5) return 'العنوان قصير';
    return null;
  }

  String? validateRegion(api.RegionSchema? value) {
    if (value == null) return 'اختار المنطقة لو سمحت';
    return null;
  }

  /// Save updates
  Future<void> save() async {
    if (isLoading) return;
    final isValid = formKey.currentState?.validate() ?? false;
    if (!isValid) return;
    if (selectedRegion == null) {
      MySnackbar.show(title: 'خطأ', message: 'اختار المنطقة لو سمحت');
      return;
    }

    final store = AuthService.to.currentStore.value;
    if (store == null) {
      MySnackbar.show(title: 'خطأ', message: 'مفيش متجر');
      return;
    }

    // Map region into ids depending on its type
    int? cityId, stateId, countryId;
    switch ((selectedRegion!.type ?? '').toLowerCase()) {
      case 'city':
        cityId = selectedRegion!.id;
        stateId = selectedRegion!.parent;
        break;
      case 'state':
        stateId = selectedRegion!.id;
        countryId = selectedRegion!.parent;
        break;
      case 'country':
        countryId = selectedRegion!.id;
        break;
      default:
        cityId = selectedRegion!.id;
    }

    isLoading = true;
    try {
      final update = api.StoreUpdate(
        name: nameController.text.trim(),
        description: descriptionController.text.trim(),
        address: addressController.text.trim(),
        cityId: cityId,
        stateId: stateId,
        countryId: countryId,
      );

      final res = await ApiService.to.apiClient.stores.updateStore(
        store.id,
        update,
      );

      // Update AuthService store snapshot
      AuthService.to.setCurrentStore(res.data!);
      Get.back();
      MySnackbar.show(title: 'تم', message: 'تم تحديث بيانات المتجر');
    } on Exception catch (e) {
      if (kDebugMode) {
        print('Update store error: $e');
      }
      MySnackbar.show(title: 'خطأ', message: 'فشل تحديث بيانات المتجر');
    } finally {
      isLoading = false;
    }
  }

  @override
  void onClose() {
    nameController.dispose();
    descriptionController.dispose();
    addressController.dispose();
    super.onClose();
  }
}
