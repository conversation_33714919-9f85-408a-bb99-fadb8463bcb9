import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/pages/store/edit/controller.dart';
import '../../../core/api/api.dart';

class EditStorePage extends StatelessWidget {
  const EditStorePage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    InputDecoration buildDecoration({
      required String label,
      required IconData icon,
      String? hint,
    }) {
      return InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: AppColors.primary),
        filled: true,
        fillColor: Colors.grey.shade50,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(28),
          borderSide: BorderSide(color: Colors.grey.shade300, width: 1.5),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(28),
          borderSide: BorderSide(color: Colors.grey.shade300, width: 1.5),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(28),
          borderSide: const BorderSide(color: AppColors.primary, width: 2.5),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(28),
          borderSide: const BorderSide(color: Colors.red, width: 1.5),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(28),
          borderSide: const BorderSide(color: Colors.red, width: 2.5),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
      );
    }

    return GetBuilder<EditStoreController>(
      init: EditStoreController(),
      builder: (controller) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('تعديل بيانات المتجر'),
            centerTitle: true,
            elevation: 0,
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Form(
              key: controller.formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  TextFormField(
                    controller: controller.nameController,
                    textInputAction: TextInputAction.next,
                    validator: controller.validateName,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                      color: Colors.black87,
                    ),
                    decoration: buildDecoration(
                      label: 'اسم المتجر',
                      icon: Icons.store_rounded,
                    ),
                  ),
                  const SizedBox(height: 16),

                  TextFormField(
                    controller: controller.descriptionController,
                    textInputAction: TextInputAction.next,
                    maxLines: 3,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                      color: Colors.black87,
                    ),
                    decoration: buildDecoration(
                      label: 'وصف المتجر',
                      icon: Icons.description_rounded,
                      hint: 'اختياري',
                    ),
                  ),
                  const SizedBox(height: 16),

                  TextFormField(
                    controller: controller.addressController,
                    textInputAction: TextInputAction.next,
                    maxLines: 2,
                    validator: controller.validateAddress,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                      color: Colors.black87,
                    ),
                    decoration: buildDecoration(
                      label: 'عنوان المتجر',
                      icon: Icons.location_on_rounded,
                    ),
                  ),
                  const SizedBox(height: 16),

                  Obx(
                    () => DropdownButtonFormField<RegionSchema>(
                      value: controller.selectedRegion,
                      onChanged: (RegionSchema? v) => controller.selectedRegion = v,
                      validator: (value) => controller.validateRegion(value),
                      decoration: buildDecoration(
                        label: 'اختر المنطقة',
                        icon: Icons.map_rounded,
                      ),
                      items: controller.regions
                          .map(
                            (r) => DropdownMenuItem<RegionSchema>(
                              value: r,
                              child: Text(
                                r.name,
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: Colors.black87,
                                ),
                              ),
                            ),
                          )
                          .toList(),
                      hint: controller.isLoadingRegions
                          ? Row(
                              children: [
                                const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  'جاري التحميل...',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            )
                          : Text(
                              'اختر المنطقة',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: Colors.grey.shade600,
                              ),
                            ),
                      dropdownColor: Colors.white,
                      style: theme.textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: Colors.black87,
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  Obx(
                    () => SizedBox(
                      height: 56,
                      child: ElevatedButton(
                        onPressed: controller.isLoading ? null : controller.save,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(28),
                          ),
                        ),
                        child: controller.isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation(Colors.white),
                                ),
                              )
                            : Text(
                                'حفظ التعديلات',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 0.3,
                                ),
                              ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
