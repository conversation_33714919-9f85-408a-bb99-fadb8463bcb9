import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../../core/api/api.dart';
import '../../../services/api.dart';
import 'package:tagerplus/core/utils/snackbar.dart';

class StoreCreateController extends GetxController {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  // Text controllers
  final TextEditingController storeNameController = TextEditingController();
  final TextEditingController storeDescriptionController =
      TextEditingController();
  final TextEditingController storeAddressController = TextEditingController();

  // Private reactive variables
  final RxBool _isLoading = false.obs;
  final RxBool _isLoadingRegions = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxList<RegionSchema> _regions = <RegionSchema>[].obs;
  final Rx<RegionSchema?> _selectedRegion = Rx<RegionSchema?>(null);

  // Getters (public access to private variables)
  bool get isLoading => _isLoading.value;
  bool get isLoadingRegions => _isLoadingRegions.value;
  String get errorMessage => _errorMessage.value;
  List<RegionSchema> get regions => _regions;
  RegionSchema? get selectedRegion => _selectedRegion.value;

  // Setters (controlled modification)
  set isLoading(bool value) => _isLoading.value = value;
  set isLoadingRegions(bool value) => _isLoadingRegions.value = value;
  set selectedRegion(RegionSchema? value) => _selectedRegion.value = value;

  @override
  void onInit() {
    super.onInit();
    loadRegions();
  }

  /// Load available regions
  Future<void> loadRegions() async {
    if (isLoadingRegions) return;

    isLoadingRegions = true;
    try {
      final regionsList = await ApiService.to.apiClient.regions.getStates();
      _regions.value = regionsList.data!;
    } catch (e) {
      if (kDebugMode) {
        print('Error loading regions: $e');
      }
      MySnackbar.show(title: 'خطأ', message: 'مشكلة في الاتصال');
    } finally {
      isLoadingRegions = false;
    }
  }

  /// Validation methods
  String? validateStoreName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'اكتب اسم المتجر لو سمحت';
    }
    if (value.trim().length < 2) {
      return 'الاسم قصير';
    }
    return null;
  }

  String? validateStoreAddress(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'اكتب عنوان المتجر لو سمحت';
    }
    if (value.trim().length < 5) {
      return 'العنوان قصير';
    }
    return null;
  }

  String? validateRegion(RegionSchema? value) {
    if (value == null) {
      return 'اختار المنطقة لو سمحت';
    }
    return null;
  }

  /// Create store
  Future<void> createStore() async {
    if (isLoading) return;

    final isValid = formKey.currentState?.validate() ?? false;
    if (!isValid) return;

    if (selectedRegion == null) {
      MySnackbar.show(title: 'خطأ', message: 'اختار المنطقة لو سمحت');
      return;
    }

    isLoading = true;
    try {
      // Map region to appropriate field based on type
      int? cityId, stateId, countryId;
      switch (selectedRegion!.type!.toLowerCase()) {
        case 'city':
          cityId = selectedRegion!.id;
          stateId = selectedRegion!.parent;
          break;
        case 'state':
          stateId = selectedRegion!.id;
          countryId = selectedRegion!.parent;
          break;
        case 'country':
          countryId = selectedRegion!.id;
          break;
        default:
          // Default to city if type is unknown
          cityId = selectedRegion!.id;
      }

      final currentUser = await ApiService.to.apiClient.auth.getCurrentUser();

      final storeRequest = StoreIn(
        name: storeNameController.text.trim(),
        description: storeDescriptionController.text.trim().isEmpty
            ? ''
            : storeDescriptionController.text.trim(),
        address: storeAddressController.text.trim(),
        cityId: cityId,
        stateId: stateId,
        countryId: countryId,
        ownerId: currentUser.data?.id ?? 0,
      );

      await ApiService.to.apiClient.stores.createStore(storeRequest);

      MySnackbar.show(title: 'تم', message: 'تم إنشاء المتجر بنجاح');

      // Navigate to home page
      Get.offAll(
        () => const Scaffold(
          body: Center(child: Text('Home Page - Coming Soon')),
        ),
      );
    } on Exception catch (e) {
      String errorMessage = 'فشل إنشاء المتجر';

      if (e.toString().contains('validation')) {
        errorMessage = e.toString();
      } else if (e.toString().contains('Connection')) {
        errorMessage = 'مشكلة في الاتصال';
      } else if (e.toString().contains('Server')) {
        errorMessage = 'خطأ في السيرفر';
      }

      if (kDebugMode) {
        print('Store creation error: $e');
      }

      MySnackbar.show(title: 'خطأ', message: errorMessage);
    } catch (e) {
      if (kDebugMode) {
        print('Unexpected store creation error: $e');
      }
      MySnackbar.show(title: 'خطأ', message: 'فشل إنشاء المتجر');
    } finally {
      isLoading = false;
    }
  }

  /// Skip store creation for now
  void skipStoreCreation() {
    Get.offAll(
      () =>
          const Scaffold(body: Center(child: Text('Home Page - Coming Soon'))),
    );
  }

  /// Cleanup
  @override
  void onClose() {
    storeNameController.dispose();
    storeDescriptionController.dispose();
    storeAddressController.dispose();
    super.onClose();
  }
}
