import 'package:get/get.dart';
import 'package:tagerplus/core/utils/snackbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../../core/api/api.dart';
import '../../../services/api.dart';
import 'package:tagerplus/pages/store/create/create.dart';

class SignupController extends GetxController {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  final TextEditingController nameController = TextEditingController();
  final TextEditingController phoneNumberController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController =
      TextEditingController();

  final RxBool isPasswordHidden = true.obs;
  final RxBool isConfirmPasswordHidden = true.obs;
  final RxBool isLoading = false.obs;
  final RxBool agreeToTerms = false.obs;

  void togglePasswordVisibility() {
    isPasswordHidden.value = !isPasswordHidden.value;
  }

  void toggleConfirmPasswordVisibility() {
    isConfirmPasswordHidden.value = !isConfirmPasswordHidden.value;
  }

  void toggleTermsAgreement() {
    agreeToTerms.value = !agreeToTerms.value;
  }

  String? validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'اكتب الاسم لو سمحت';
    }
    if (value.trim().length < 2) {
      return 'الاسم قصير';
    }
    return null;
  }

  String? validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'اكتب رقم الموبايل لو سمحت';
    }
    if (value.trim().length < 8) {
      return 'اكتب رقم صحيح';
    }
    return null;
  }

  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'اكتب الباسورد لو سمحت';
    }
    if (value.length < 6) {
      return 'الباسورد لازم يكون 6 حروف على الأقل';
    }
    return null;
  }

  String? validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'أكد الباسورد لو سمحت';
    }
    if (value != passwordController.text) {
      return 'الباسوردين مش متطابقين';
    }
    return null;
  }

  Future<void> signup() async {
    if (isLoading.value) return;

    if (!agreeToTerms.value) {
      MySnackbar.show(title: 'خطأ', message: 'لازم توافق على الشروط والأحكام الأول');
      return;
    }

    final isValid = formKey.currentState?.validate() ?? false;
    if (!isValid) return;

    isLoading.value = true;
    try {
      final registerRequest = RegisterRequest(
        name: nameController.text.trim(),
        phone: phoneNumberController.text.trim(),
        password: passwordController.text,
      );

      final registerResponse = await ApiService.to.apiClient.auth.signup(
        registerRequest,
      );

      if (registerResponse.statusCode == 200 && registerResponse.data != null) {
        MySnackbar.show(title: 'تم', message: 'اتعمل الحساب بنجاح');

        // Navigate to store creation since new users don't have stores
        Get.offAll(() => const StoreCreatePage());
      } else {
        MySnackbar.show(title: 'خطأ', message: 'فشل إنشاء الحساب');
      }
    } on Exception catch (e) {
      String errorMessage = 'فشل إنشاء الحساب';

      if (e.toString().contains('phone')) {
        errorMessage = 'رقم الموبايل موجود قبل كده';
      } else if (e.toString().contains('password')) {
        errorMessage = 'الباسورد ضعيف';
      } else if (e.toString().contains('Connection')) {
        errorMessage = 'مشكلة في الاتصال';
      } else if (e.toString().contains('Server')) {
        errorMessage = 'خطأ في السيرفر';
      }

      if (kDebugMode) {
        print('Signup error: $e');
      }

      MySnackbar.show(title: 'خطأ', message: errorMessage);
    } catch (e) {
      if (kDebugMode) {
        print('Unexpected signup error: $e');
      }
      MySnackbar.show(title: 'خطأ', message: 'فشل إنشاء الحساب');
    } finally {
      isLoading.value = false;
    }
  }

  @override
  void onClose() {
    nameController.dispose();
    phoneNumberController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    super.onClose();
  }
}
