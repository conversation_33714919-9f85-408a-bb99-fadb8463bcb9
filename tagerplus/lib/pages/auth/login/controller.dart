import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:tagerplus/core/utils/snackbar.dart';
import 'package:tagerplus/core/api/models/login_request.dart';
import 'package:tagerplus/pages/home/<USER>';
import 'package:tagerplus/pages/store/create/create.dart';
import 'package:tagerplus/services/api.dart';
import 'package:tagerplus/services/auth.dart';

class LoginController extends GetxController {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  final TextEditingController phoneNumberController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  final RxBool isPasswordHidden = true.obs;
  final RxBool isLoading = false.obs;

  void togglePasswordVisibility() {
    isPasswordHidden.value = !isPasswordHidden.value;
  }

  String? validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'اكتب رقم الموبايل لو سمحت';
    }
    if (value.trim().length < 8) {
      return 'اكتب رقم صحيح';
    }
    return null;
  }

  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'اكتب الباسورد لو سمحت';
    }
    if (value.length < 6) {
      return 'الباسورد لازم يكون 6 حروف على الأقل';
    }
    return null;
  }

  Future<void> login() async {
    if (isLoading.value) return;
    final isValid = formKey.currentState?.validate() ?? false;
    if (!isValid) return;

    isLoading.value = true;
    try {
      final loginRequest = LoginRequest(
        phone: phoneNumberController.text.trim(),
        password: passwordController.text,
      );

      final loginResponse = await ApiService.to.apiClient.auth.signin(
        loginRequest,
      );

      if (loginResponse.statusCode == 200 && loginResponse.data != null) {
        MySnackbar.show(title: 'تم', message: 'تم تسجيل الدخول بنجاح');

        await AuthService.to.login(loginResponse.data!.token);

        // Check if user has stores using the old auth service
        if (AuthService.to.hasStores) {
          // Navigate to home page
          Get.offAll(() => const HomePage());
        } else {
          // Navigate to store creation
          Get.offAll(() => const StoreCreatePage());
        }
      } else {
        MySnackbar.show(title: 'خطأ', message: 'فشل تسجيل الدخول');
      }
    } on Exception catch (e) {
      String errorMessage = 'فشل تسجيل الدخول';

      if (e.toString().contains('Unauthorized')) {
        errorMessage = 'بيانات الدخول غير صحيحة';
      } else if (e.toString().contains('Connection')) {
        errorMessage = 'مشكلة في الاتصال';
      } else if (e.toString().contains('Server')) {
        errorMessage = 'خطأ في السيرفر';
      }

      if (kDebugMode) {
        print('Login error: $e');
      }

      MySnackbar.show(title: 'خطأ', message: errorMessage);
    } catch (e) {
      if (kDebugMode) {
        print('Unexpected login error: $e');
      }
      MySnackbar.show(title: 'خطأ', message: 'فشل تسجيل الدخول');
    } finally {
      isLoading.value = false;
    }
  }

  @override
  void onClose() {
    phoneNumberController.dispose();
    passwordController.dispose();
    super.onClose();
  }
}
