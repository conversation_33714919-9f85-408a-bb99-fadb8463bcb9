import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/components/network_image.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'package:tagerplus/pages/orders/list/controller.dart';
import 'package:tagerplus/pages/orders/details/details.dart';

class OrdersListPage extends StatelessWidget {
  const OrdersListPage({super.key});

  @override
  Widget build(BuildContext context) {
    Get.put(OrdersListController());

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        appBar: AppBar(
          title: Text(
            'الطلبات',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          centerTitle: true,
          elevation: 0,
          backgroundColor: Theme.of(context).colorScheme.surface,
          foregroundColor: Theme.of(context).colorScheme.onSurface,
        ),
        body: GetX<OrdersListController>(
          builder: (c) {
            if (c.isLoading && c.orders.isEmpty) {
              return const _OrdersLoading();
            }

            if (c.hasError && c.orders.isEmpty) {
              return _OrdersError(
                message: c.errorMessage,
                onRetry: c.refreshOrders,
              );
            }

            if (c.orders.isEmpty) {
              return const _OrdersEmpty();
            }

            return RefreshIndicator(
              onRefresh: c.refreshOrders,
              color: Theme.of(context).colorScheme.primary,
              child: ListView.separated(
                controller: c.scrollController,
                padding: const EdgeInsets.all(AppDefaults.padding),
                itemCount: c.orders.length + (c.canLoadMore ? 1 : 0),
                separatorBuilder: (_, __) => const SizedBox(height: 8),
                itemBuilder: (context, index) {
                  if (index >= c.orders.length) {
                    // Loading footer
                    return const Padding(
                      padding: EdgeInsets.symmetric(vertical: 16),
                      child: Center(
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    );
                  }

                  final order = c.orders[index];

                  return _OrderTile(
                    id: order.id,
                    status: order.status,
                    total: order.totalPrice,
                    itemsCount: order.productsTotalQuantity,
                    createdAt: order.createdAt,
                    wholesalerLogo: order.wholesaler.logo ?? '',
                    wholesalerTitle: order.wholesaler.title,
                    onTap: () =>
                        Get.to(() => OrderDetailsPage(orderId: order.id)),
                  );
                },
              ),
            );
          },
        ),
      ),
    );
  }
}

class _OrdersLoading extends StatelessWidget {
  const _OrdersLoading();

  @override
  Widget build(BuildContext context) {
    return const Center(child: CircularProgressIndicator());
  }
}

class _OrdersEmpty extends StatelessWidget {
  const _OrdersEmpty();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppDefaults.padding * 2),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: 80,
              color: Theme.of(context).colorScheme.primary.withOpacity(0.6),
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد طلبات حتى الآن',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'ابدأ التسوق وأضف منتجاتك لإنشاء طلب جديد',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class _OrdersError extends StatelessWidget {
  final String message;
  final VoidCallback onRetry;
  const _OrdersError({required this.message, required this.onRetry});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppDefaults.padding * 2),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 80, color: Colors.red.shade300),
            const SizedBox(height: 16),
            Text(
              'فشل تحميل الطلبات',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.red.shade700,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }
}

String _translateStatus(String status) {
  switch (status.toLowerCase()) {
    case 'pending':
      return 'قيد الانتظار';
    case 'approved':
      return 'تمت الموافقة';
    case 'processing':
      return 'قيد المعالجة';
    case 'delivered':
      return 'تم التسليم';
    case 'completed':
      return 'مكتمل';
    case 'rejected':
      return 'مرفوض';
    case 'canceled':
    case 'cancelled':
      return 'ملغي';
    default:
      return status;
  }
}

class _OrderTile extends StatelessWidget {
  final int id;
  final String status;
  final double total;
  final int itemsCount;
  final String createdAt;
  final VoidCallback onTap;
  final String wholesalerLogo;
  final String wholesalerTitle;

  const _OrderTile({
    required this.id,
    required this.status,
    required this.total,
    required this.itemsCount,
    required this.createdAt,
    required this.onTap,
    required this.wholesalerLogo,
    required this.wholesalerTitle,
  });

  Color _statusColor(BuildContext context) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'approved':
      case 'processing':
        return Theme.of(context).colorScheme.primary;
      case 'delivered':
      case 'completed':
        return Colors.green;
      case 'rejected':
      case 'canceled':
        return Colors.red;
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: AppDefaults.borderRadius,
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: AppDefaults.borderRadius,
          boxShadow: AppDefaults.boxShadow,
        ),
        padding: const EdgeInsets.all(AppDefaults.padding),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: SizedBox(
                width: 44,
                height: 44,
                child: NetworkImageWithLoader(
                  wholesalerLogo,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        wholesalerTitle,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _statusColor(context).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _translateStatus(status),
                          style: TextStyle(
                            color: _statusColor(context),
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '#$id • $itemsCount منتج • $total جنيه',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 2),
                  Text(createdAt, style: Theme.of(context).textTheme.bodySmall),
                ],
              ),
            ),
            const Icon(Icons.chevron_left),
          ],
        ),
      ),
    );
  }
}
