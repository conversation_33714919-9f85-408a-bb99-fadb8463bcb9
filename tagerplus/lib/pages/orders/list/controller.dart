import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/utils/snackbar.dart';
import 'package:tagerplus/core/api/models/models.dart';
import 'package:tagerplus/services/api.dart';
import 'package:tagerplus/services/auth.dart';

class OrdersListController extends GetxController {
  final RxBool _isLoading = false.obs;
  final RxBool _hasError = false.obs;
  final RxString _errorMessage = ''.obs;

  final RxList<OrderOut> _orders = <OrderOut>[].obs;
  final RxInt _page = 1.obs;
  final int _pageSize = 20;
  final RxBool _hasNext = true.obs;

  bool get isLoading => _isLoading.value;
  bool get hasError => _hasError.value;
  String get errorMessage => _errorMessage.value;

  List<OrderOut> get orders => _orders.toList();
  bool get canLoadMore => _hasNext.value && !_isLoading.value;

  final ScrollController scrollController = ScrollController();

  @override
  void onInit() {
    super.onInit();
    loadOrders(initial: true);
    scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    if (!canLoadMore) return;
    if (scrollController.position.pixels >=
        scrollController.position.maxScrollExtent - 200) {
      loadOrders();
    }
  }

  Future<void> refreshOrders() async {
    _orders.clear();
    _page.value = 1;
    _hasNext.value = true;
    await loadOrders(initial: true);
  }

  Future<void> loadOrders({bool initial = false}) async {
    if (_isLoading.value) return;

    _isLoading.value = true;
    if (initial) {
      _hasError.value = false;
      _errorMessage.value = '';
    }

    try {
      final storeId = AuthService.to.currentStore.value?.id;
      final resp = await ApiService.to.apiClient.orders.listOrders(
        page: _page.value,
        pageSize: _pageSize,
        storeId: storeId,
      );

      final data = resp.data;
      final ordersData = data?.orders ?? <OrderOut>[];
      final hasNextFlag = data?.hasNext ?? false;

      _orders.addAll(ordersData);
      _hasNext.value = hasNextFlag;
      if (hasNextFlag) _page.value = _page.value + 1;
    } catch (e, s) {
      if (kDebugMode) {
        print('Error loading orders: $e');
        print(s);
      }
      _hasError.value = true;
      _errorMessage.value = e.toString();
      MySnackbar.show(title: 'خطأ', message: 'حدث خطأ أثناء تحميل الطلبات');
    } finally {
      _isLoading.value = false;
    }
  }

  @override
  void onClose() {
    scrollController.dispose();
    super.onClose();
  }
}
