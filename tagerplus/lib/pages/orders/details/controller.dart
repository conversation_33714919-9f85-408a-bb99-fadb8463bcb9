import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/utils/snackbar.dart';
import 'package:tagerplus/core/api/models/models.dart';
import 'package:tagerplus/services/api.dart';

class OrderDetailsController extends GetxController {
  final int orderId;
  OrderDetailsController({required this.orderId});

  final RxBool _isLoading = false.obs;
  final RxBool _hasError = false.obs;
  final RxString _errorMessage = ''.obs;

  final Rx<OrderOut?> _order = Rx<OrderOut?>(null);

  bool get isLoading => _isLoading.value;
  bool get hasError => _hasError.value;
  String get errorMessage => _errorMessage.value;
  OrderOut? get order => _order.value;

  double get subtotal => order?.productsTotalPrice ?? 0.0;
  double get total => order?.totalPrice ?? 0.0;
  int get itemsCount => order?.productsTotalQuantity ?? 0;

  @override
  void onInit() {
    super.onInit();
    loadOrder();
  }

  Future<void> loadOrder() async {
    _isLoading.value = true;
    _hasError.value = false;
    _errorMessage.value = '';

    try {
      final resp = await ApiService.to.apiClient.orders.getOrder(orderId);
      _order.value = resp.data;
    } catch (e, s) {
      if (kDebugMode) {
        print('Error loading order details: $e');
        print(s);
      }
      _hasError.value = true;
      _errorMessage.value = e.toString();
      MySnackbar.show(title: 'خطأ', message: 'حصل خطأ أثناء تحميل تفاصيل الطلب');
    } finally {
      _isLoading.value = false;
    }
  }
}
