import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/utils/snackbar.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/services/auth.dart';
import 'package:tagerplus/pages/store/edit/edit_store_page.dart';
import 'package:tagerplus/pages/auth/login/login.dart';

class ProfileMenuOptions extends StatelessWidget {
  const ProfileMenuOptions({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    Widget buildTile({
      required IconData icon,
      required String title,
      String? subtitle,
      required VoidCallback onTap,
      Color? color,
    }) {
      return ListTile(
        leading: Container(
          width: 44,
          height: 44,
          decoration: BoxDecoration(
            color: (color ?? AppColors.primary).withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(icon, color: color ?? AppColors.primary),
        ),
        title: Text(
          title,
          style: theme.textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w700),
        ),
        subtitle: subtitle != null
            ? Text(subtitle, style: theme.textTheme.bodyMedium?.copyWith(color: Colors.grey[600]))
            : null,
        trailing: const Icon(Icons.chevron_left_rounded),
        onTap: onTap,
      );
    }

    return Column(
      children: [
        // Edit store (if has a store)
        Obx(() {
          final hasStore = AuthService.to.currentStore.value != null;
          if (!hasStore) return const SizedBox.shrink();
          return buildTile(
            icon: Icons.store_rounded,
            title: 'تعديل بيانات المتجر',
            subtitle: 'تحديث الاسم، العنوان، الوصف والمنطقة',
            onTap: () => Get.to(() => const EditStorePage()),
          );
        }),

        buildTile(
          icon: Icons.support_agent_rounded,
          title: 'الدعم والمساعدة',
          onTap: () {
            MySnackbar.show(title: 'قريبًا', message: 'سيتم إضافة الدعم لاحقًا');
          },
          color: Colors.blue,
        ),

        const Divider(height: 32),

        // Logout
        ListTile(
          leading: Container(
            width: 44,
            height: 44,
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.08),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(Icons.logout_rounded, color: Colors.red),
          ),
          title: Text(
            'تسجيل الخروج',
            style: theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w700,
              color: Colors.red,
            ),
          ),
          onTap: () async {
            await AuthService.to.logout();
            Get.offAll(() => const LoginPage());
          },
        ),
      ],
    );
  }
}
