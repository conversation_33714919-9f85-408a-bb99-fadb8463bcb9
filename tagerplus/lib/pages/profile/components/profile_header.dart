import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/services/auth.dart';

class ProfileHeader extends StatelessWidget {
  const ProfileHeader({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primary,
            AppColors.primary.withValues(alpha: 0.85),
          ],
        ),
      ),
      padding: const EdgeInsets.fromLTRB(20, 56, 20, 24),
      child: Safe<PERSON><PERSON>(
        bottom: false,
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.15),
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white24),
              ),
              child: const Icon(Icons.person_rounded, color: Colors.white, size: 34),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Obx(() {
                final user = AuthService.to.user.value;
                final store = AuthService.to.currentStore.value;
                final displayName = () {
                  if (user == null) return 'مرحبا بك';
                  final hasNames =
                      (user.firstName != null && user.firstName!.isNotEmpty) ||
                      (user.lastName != null && user.lastName!.isNotEmpty);
                  if (hasNames) {
                    return [user.firstName, user.lastName]
                        .where((e) => (e ?? '').isNotEmpty)
                        .join(' ')
                        .trim();
                  }
                  return user.username.isNotEmpty ? user.username : user.phone;
                }();
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      displayName,
                      style: theme.textTheme.titleLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w700,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      store?.name ?? 'لا يوجد متجر محدد',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withOpacity(0.9),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}
