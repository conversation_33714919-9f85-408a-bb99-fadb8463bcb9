import 'package:get/get.dart';
import 'package:tagerplus/core/api/models/models.dart';
import 'package:tagerplus/services/api.dart';
import 'package:flutter/foundation.dart';
import 'package:tagerplus/services/auth.dart';

class ProductDetailsController extends GetxController {
  static ProductDetailsController get to => Get.find();

  final int id;
  ProductDetailsController({required this.id});

  final RxBool _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  final product = Rxn<ProductSchema>();
  final items = RxList<ItemSchema>();
  final selectedItem = Rxn<ItemSchema>();

  @override
  void onInit() {
    super.onInit();
    fetchProduct();
  }

  Future<void> fetchProduct() async {
    try {
      _isLoading.value = true;
      final response = await ApiService.to.apiClient.products.getProduct(id);
      product.value = response.data;
      fetchItems();
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching product: $e');
      }
    } finally {
      _isLoading.value = false;
    }
  }

  void selectItem(ItemSchema item) {
    if (selectedItem.value == item) {
      selectedItem.value = null;
    } else {
      selectedItem.value = item;
    }
  }

  Future<void> fetchItems() async {
    try {
      _isLoading.value = true;
      final response = await ApiService.to.apiClient.items.listItems(
        productId: id,
        regionId: AuthService.to.currentStore.value?.cityId,
      );
      items.value = response.data?.data ?? [];
      items.sort((a, b) => a.basePrice.compareTo(b.basePrice));
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching items: $e');
      }
    } finally {
      _isLoading.value = false;
    }
  }
}
