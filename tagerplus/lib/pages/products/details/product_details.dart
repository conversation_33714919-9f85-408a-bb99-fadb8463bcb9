import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/utils/currency_formatter.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/api/models/models.dart';
import 'package:tagerplus/core/utils/snackbar.dart';
import 'package:tagerplus/pages/products/details/product_details_controller.dart';
import 'package:tagerplus/services/cart.dart';

extension ItemSchemaExtension on ItemSchema {
  ItemOut toItemOut() {
    return ItemOut(
      id: id,
      basePrice: basePrice,
      inventoryCount: inventoryCount,
      minimumOrderQuantity: minimumOrderQuantity,
      maximumOrderQuantity: maximumOrderQuantity,
      product: ItemProductOut(
        id: product?.id ?? 0,
        name: product?.title ?? '',
        title: product?.title ?? '',
        barcode: '',
      ),
      createdAt: '',
      updatedAt: '',
    );
  }
}

class ProductDetailsPage extends StatelessWidget {
  const ProductDetailsPage({super.key, required this.id});

  final int id;

  @override
  Widget build(BuildContext context) {
    final c = Get.put(ProductDetailsController(id: id));
    return Scaffold(
      resizeToAvoidBottomInset: false,
      bottomNavigationBar: Obx(() {
        if (c.isLoading || c.product.value == null) {
          return const SizedBox.shrink();
        }
        return const SafeArea(
          child: Padding(
            padding: EdgeInsets.all(AppDefaults.padding),
            child: _BuyNowRow(),
          ),
        );
      }),
      body: Obx(() {
        if (c.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final product = c.product.value;
        if (product == null) {
          return const Center(child: Text('المنتج غير موجود'));
        }

        return CustomScrollView(
          slivers: [
            SliverAppBar(
              expandedHeight: MediaQuery.of(context).size.height * 0.4,
              pinned: true,
              leading: Container(
                margin: const EdgeInsets.all(7),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: AppDefaults.borderRadius,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Center(
                  child: const BackButton(
                    style: ButtonStyle(
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),
                ),
              ),
              flexibleSpace: FlexibleSpaceBar(
                // title: Text(product.name, style: const TextStyle(fontSize: 16)),
                background: _ProductImagesSlider(
                  images: product.imageUrl != null ? [product.imageUrl!] : [],
                ),
              ),
            ),
            SliverToBoxAdapter(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product Info
                  Padding(
                    padding: const EdgeInsets.all(AppDefaults.padding),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          product.name,
                          style: Theme.of(context).textTheme.headlineSmall
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Text('الوزن: ${product.unitCount} ${product.unit}'),
                      ],
                    ),
                  ),

                  // Price Summary
                  _buildPriceSummary(context, c),

                  const SizedBox(height: 16),

                  // Product Description
                  if (product.description.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppDefaults.padding,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'تفاصيل المنتج',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                          const SizedBox(height: 8),
                          Text(product.description),
                        ],
                      ),
                    ),

                  const SizedBox(height: 16),

                  // Wholesaler Prices
                  _buildWholesalerPrices(context, c),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ],
        );
      }),
    );
  }

  Widget _buildPriceSummary(BuildContext context, ProductDetailsController c) {
    if (c.items.isEmpty) {
      return const Padding(
        padding: EdgeInsets.symmetric(horizontal: AppDefaults.padding),
        child: Text('لا توجد أسعار متاحة'),
      );
    }

    final minPrice = c.items.first.basePrice;
    final maxPrice = c.items.last.basePrice;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
      child: Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'أفضل سعر',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
              Text(
                CurrencyFormatter.formatPrice(minPrice),
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          if (maxPrice != minPrice) ...[
            const SizedBox(width: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'أعلى سعر',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
                Text(
                  CurrencyFormatter.formatPrice(maxPrice),
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.orange,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
          const Spacer(),
          Text(
            '${c.items.length} تاجر',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildWholesalerPrices(
    BuildContext context,
    ProductDetailsController c,
  ) {
    if (c.items.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(AppDefaults.padding),
        child: Text('لا توجد أسعار متاحة من التجار'),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(AppDefaults.padding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Divider(thickness: 0.1),
          Text(
            'الأسعار من التجار المختلفين',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 16),
          ...c.items.asMap().entries.map((entry) {
            final index = entry.key;
            final itemInfo = entry.value;
            final isLowestPrice = index == 0;
            return Obx(() {
              final isSelected = c.selectedItem.value == itemInfo;
              return GestureDetector(
                onTap: () => c.selectItem(itemInfo),
                child: _buildPriceCard(
                  context,
                  itemInfo,
                  isLowestPrice,
                  isSelected,
                ),
              );
            });
          }),
        ],
      ),
    );
  }

  Widget _buildPriceCard(
    BuildContext context,
    ItemSchema itemInfo,
    bool isLowestPrice,
    bool isSelected,
  ) {
    final wholesaler = itemInfo.wholesaler;
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: AppDefaults.borderRadius,
        color: isSelected ? AppColors.primary.withAlpha(15) : Colors.white,
        border: Border.all(
          color: isSelected ? AppColors.primary : Colors.grey[200]!,
          width: isSelected ? 1.5 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          // Wholesaler logo or avatar
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.grey[200],
            ),
            child: wholesaler?.logo != null
                ? ClipOval(
                    child: Image.network(wholesaler!.logo!, fit: BoxFit.cover),
                  )
                : Icon(Icons.store, color: Colors.grey[400]),
          ),
          const SizedBox(width: 12),

          // Wholesaler info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  wholesaler?.title ?? wholesaler?.username ?? 'تاجر',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (itemInfo.inventoryCount > 0)
                  Text(
                    'متوفر',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.green),
                  )
                else
                  Text(
                    'غير متوفر',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.red),
                  ),
                Text(
                  _getQuantityConstraintText(itemInfo),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ),

          // Price and badge
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              if (isLowestPrice)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'أفضل سعر',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              const SizedBox(height: 4),
              Text(
                CurrencyFormatter.formatPrice(itemInfo.basePrice),
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: isLowestPrice ? Colors.green : Colors.black,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getQuantityConstraintText(ItemSchema itemInfo) {
    final min = itemInfo.minimumOrderQuantity;
    final max = itemInfo.maximumOrderQuantity;
    if (max != null) {
      return 'الكمية: $min - $max';
    }
    return 'الحد الأدنى للكمية: $min';
  }
}

// Helper widgets

class _BuyNowRow extends StatelessWidget {
  const _BuyNowRow();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final controller = Get.find<ProductDetailsController>();
      final cartService = Get.find<CartService>();
      final selectedItem = controller.selectedItem.value;

      if (selectedItem == null) {
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Text(
            'اختر تاجر لإضافة المنتج إلى السلة',
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.grey, fontSize: 16),
          ),
        );
      }

      final wholesalerId = selectedItem.wholesaler!.id;
      final cartItems = cartService.cart[wholesalerId];
      final int quantity =
          cartItems
              ?.firstWhereOrNull(
                (element) => element.item.id == selectedItem.id,
              )
              ?.quantity
              .value ??
          0;

      if (quantity == 0) {
        // Show Add to Cart button
        return SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () {
              // check the maximum quantity per order
              if (selectedItem.maximumOrderQuantity != null &&
                  quantity >= selectedItem.maximumOrderQuantity!) {
                MySnackbar.show(
                  title: 'خطأ',
                  message: 'الحد الأقصى للكمية هو ${selectedItem.maximumOrderQuantity}',
                );
                return;
              }
              cartService.addToCart(wholesalerId, selectedItem.toItemOut());
            },
            icon: const Icon(Icons.add_shopping_cart),
            label: const Text('أضف إلى السلة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 2,
            ),
          ),
        );
      }

      // Show quantity controls
      return Row(
        children: [
          Expanded(
            child: Container(
              height: 56,
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  IconButton(
                    icon: const Icon(Icons.remove, color: AppColors.primary),
                    onPressed: () {
                      if (quantity <= selectedItem.minimumOrderQuantity!) {
                        MySnackbar.show(
                          title: 'خطأ',
                          message:
                              'الحد الأدنى للكمية هو ${selectedItem.minimumOrderQuantity}',
                        );
                        return;
                      }
                      cartService.removeFromCart(
                        wholesalerId,
                        selectedItem.toItemOut(),
                      );
                    },
                  ),
                  Text(
                    '$quantity',
                    style: const TextStyle(
                      color: AppColors.primary,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.add, color: AppColors.primary),
                    onPressed: () {
                      // check the maximum quantity per order
                      if (selectedItem.maximumOrderQuantity != null &&
                          quantity >= selectedItem.maximumOrderQuantity!) {
                        MySnackbar.show(
                          title: 'خطأ',
                          message:
                              'الحد الأقصى للكمية هو ${selectedItem.maximumOrderQuantity}',
                        );
                        return;
                      }
                      cartService.addToCart(
                        wholesalerId,
                        selectedItem.toItemOut(),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 8),
          Container(
            height: 56,
            width: 56,
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: const Icon(Icons.delete_outline, color: Colors.red),
              onPressed: () {
                cartService.removeItemFromCart(wholesalerId, selectedItem.id);
              },
            ),
          ),
        ],
      );
    });
  }
}

class _ProductImagesSlider extends StatelessWidget {
  final List<String> images;
  const _ProductImagesSlider({required this.images});

  @override
  Widget build(BuildContext context) {
    if (images.isEmpty) {
      return Container(
        height: 250,
        color: Colors.grey[200],
        child: const Center(
          child: Icon(Icons.image_not_supported, color: Colors.grey, size: 50),
        ),
      );
    }
    // For now, we only show the first image. A PageView can be used for a slider.
    return Image.network(
      images.first,
      height: 250,
      width: double.infinity,
      fit: BoxFit.cover,
    );
  }
}
