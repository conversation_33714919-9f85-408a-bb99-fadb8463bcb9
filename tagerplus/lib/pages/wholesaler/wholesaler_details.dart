import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/utils/snackbar.dart';
import 'package:tagerplus/pages/cart/cart_details.dart';
import 'package:tagerplus/pages/wholesaler/wholesaler_details_controller.dart';
import 'package:tagerplus/pages/wholesaler/components/wholesaler_header.dart';
import 'package:tagerplus/components/product_tile_with_quantity.dart';
import 'package:tagerplus/pages/wholesaler/components/sort_controls.dart';
import 'package:tagerplus/pages/wholesaler/components/wholesaler_cart_bottom_bar.dart';

class WholesalerDetailsPage extends GetView<WholesalerDetailsController> {
  const WholesalerDetailsPage({super.key, required this.id});

  final int id;

  @override
  Widget build(BuildContext context) {
    Get.put(WholesalerDetailsController(id: id));
    return Scaffold(
      body: Obx(() => _buildBody(context)),
      bottomNavigationBar: Obx(
        () => controller.cartItemCount > 0
            ? const WholesalerCartBottomBar()
            : const SizedBox.shrink(),
      ),
    );
  }

  Widget _buildBody(BuildContext context) {
    if (controller.isLoading.value) {
      return const Center(child: CircularProgressIndicator());
    }

    if (controller.error.value != null) {
      return _buildErrorState(context);
    }

    if (controller.wholesaler.value == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return RefreshIndicator(
      onRefresh: controller.refreshData,
      child: CustomScrollView(
        slivers: [
          // App Bar
          _buildAppBar(context),

          // Wholesaler Header
          SliverToBoxAdapter(
            child: WholesalerHeader(wholesaler: controller.wholesaler.value!),
          ),

          // Search Bar
          SliverToBoxAdapter(child: _buildSearchBar(context)),

          // Sort Controls
          SliverToBoxAdapter(
            child: Obx(
              () => SortControls(
                selectedSort: controller.selectedSort.value,
                onSortChanged: controller.onSortChanged,
              ),
            ),
          ),

          // Products Header
          SliverToBoxAdapter(child: _buildProductsHeader(context)),

          // Products Grid
          Obx(() => _buildProductsGrid(context)),

          // Bottom Spacing
          const SliverToBoxAdapter(child: SizedBox(height: 100)),
        ],
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return SliverAppBar(
      floating: true,
      title: Obx(
        () => Text(controller.wholesaler.value?.title ?? 'تفاصيل التاجر'),
      ),
      actions: [
        // Cart Button with badge
        Stack(
          children: [
            IconButton(
              onPressed: () {
                Get.to(
                  () => CartDetailsPage(
                    wholesalerId: Get.find<WholesalerDetailsController>()
                        .wholesaler
                        .value!
                        .id,
                  ),
                );
              },
              icon: const Icon(Icons.shopping_cart_outlined),
            ),
            Obx(() {
              final count = controller.cartItemCount;
              if (count == 0) return const SizedBox.shrink();
              return Positioned(
                right: 8,
                top: 8,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: const Color(0xFF00AD48),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: Text(
                    '$count',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              );
            }),
          ],
        ),
        const SizedBox(width: 8),
      ],
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        onChanged: controller.onSearchChanged,
        decoration: InputDecoration(
          hintText:
              'ابحث في منتجات ${controller.wholesaler.value?.title ?? "التاجر"}',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: Obx(
            () => controller.searchQuery.value.isNotEmpty
                ? IconButton(
                    onPressed: () => controller.onSearchChanged(''),
                    icon: const Icon(Icons.clear),
                  )
                : const SizedBox.shrink(),
          ),
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        ),
      ),
    );
  }

  Widget _buildProductsHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Obx(
        () => Text(
          'المنتجات (${controller.filteredItems.length})',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Widget _buildProductsGrid(BuildContext context) {
    final filteredItems = controller.filteredItems;

    if (filteredItems.isEmpty) {
      return SliverFillRemaining(child: _buildEmptyState(context));
    }

    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 0.55,
            crossAxisSpacing: 4,
            mainAxisSpacing: 16,
          ),
          itemCount: filteredItems.length,
          itemBuilder: (context, index) {
            final item = filteredItems[index];
            return ProductTileWithQuantity(
              title: item.product?.title ?? 'منتج غير محدد',
              price: item.basePrice,
              description:
                  '${item.product?.unitCount ?? 0} ${item.product?.unit ?? ''}',
              imageUrl: item.product?.image,
              productId: item.id,
              quantity: controller.getCartQuantity(item),
              onIncrement: () => controller.addToCart(item),
              onDecrement: () => controller.removeFromCart(item),
              onTap: () => controller.addToCart(item),
            );
          },
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ في تحميل منتجات التاجر',
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              controller.error.value!,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: controller.refreshData,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.search_off, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          Text(
            controller.searchQuery.value.isNotEmpty
                ? 'لا توجد منتجات تطابق البحث'
                : 'لا توجد منتجات متاحة',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          if (controller.searchQuery.value.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              'جرب البحث بكلمات مختلفة',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => controller.onSearchChanged(''),
              child: const Text('مسح البحث'),
            ),
          ],
        ],
      ),
    );
  }

  void _navigateToProductDetails(item) {
    // TODO: Implement navigation to product details
    MySnackbar.show(
      title: 'تفاصيل المنتج',
      message: 'سيتم تنفيذ هذه الميزة قريباً',
    );
  }
}
