import 'package:flutter/material.dart';
import 'package:tagerplus/components/network_image.dart';
import 'package:tagerplus/core/api/models/item_schema.dart';

class ProductCard extends StatelessWidget {
  final ItemSchema item;
  final VoidCallback? onTap;
  final VoidCallback? onAddToCart;
  final bool isInCart;
  final int cartQuantity;

  const ProductCard({
    super.key,
    required this.item,
    this.onTap,
    this.onAddToCart,
    this.isInCart = false,
    this.cartQuantity = 0,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Image
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(12),
                  ),
                  child: item.product?.image != null
                      ? NetworkImageWithLoader(
                          item.product!.image!,
                          fit: BoxFit.cover,
                        )
                      : Container(
                          color: Colors.grey[200],
                          child: const Icon(
                            Icons.image,
                            size: 40,
                            color: Colors.grey,
                          ),
                        ),
                ),
              ),
            ),

            // Product Details
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Product Name
                    Text(
                      item.product?.title ?? 'منتج غير محدد',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),

                    // Unit and Count
                    Text(
                      '${item.product?.unitCount ?? 0} ${item.product?.unit ?? ''}',
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                    ),
                    const Spacer(),

                    // Price and Stock
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${item.basePrice.toStringAsFixed(2)} ج.م',
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: const Color(0xFF00AD48),
                                  ),
                            ),
                            Text(
                              'متوفر: ${item.inventoryCount}',
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: item.inventoryCount > 0
                                        ? Colors.green
                                        : Colors.red,
                                  ),
                            ),
                          ],
                        ),

                        // Add to Cart Button
                        if (item.inventoryCount > 0)
                          SizedBox(
                            width: 36,
                            height: 36,
                            child: IconButton(
                              onPressed: onAddToCart,
                              style: IconButton.styleFrom(
                                backgroundColor: isInCart
                                    ? const Color(0xFF00AD48)
                                    : const Color(0xFF00AD48).withOpacity(0.1),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              icon: Icon(
                                isInCart ? Icons.check : Icons.add,
                                color: isInCart
                                    ? Colors.white
                                    : const Color(0xFF00AD48),
                                size: 18,
                              ),
                            ),
                          )
                        else
                          Container(
                            width: 36,
                            height: 36,
                            decoration: BoxDecoration(
                              color: Colors.grey[300],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Icon(
                              Icons.block,
                              color: Colors.grey,
                              size: 18,
                            ),
                          ),
                      ],
                    ),

                    // Cart Quantity Badge
                    if (isInCart && cartQuantity > 0)
                      Container(
                        margin: const EdgeInsets.only(top: 4),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF00AD48),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'في السلة: $cartQuantity',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
