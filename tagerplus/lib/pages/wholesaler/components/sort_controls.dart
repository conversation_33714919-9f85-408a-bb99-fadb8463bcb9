import 'package:flutter/material.dart';
import 'package:tagerplus/pages/wholesaler/wholesaler_details_controller.dart';

class SortControls extends StatelessWidget {
  final SortOption selectedSort;
  final Function(SortOption) onSortChanged;

  const SortControls({
    super.key,
    required this.selectedSort,
    required this.onSortChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Text(
            'ترتيب حسب:',
            style: Theme.of(
              context,
            ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildSortChip(context, 'السعر (الأقل)', SortOption.priceAsc),
                  _buildSortChip(
                    context,
                    'السعر (الأعلى)',
                    SortOption.priceDesc,
                  ),
                  _buildSortChip(context, 'الاسم (أ-ي)', SortOption.nameAsc),
                  _buildSortChip(context, 'الاسم (ي-أ)', SortOption.nameDesc),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSortChip(
    BuildContext context,
    String label,
    SortOption sortOption,
  ) {
    final isSelected = selectedSort == sortOption;
    return Padding(
      padding: const EdgeInsets.only(left: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          if (selected) {
            onSortChanged(sortOption);
          }
        },
        selectedColor: const Color(0xFF00AD48).withOpacity(0.2),
        checkmarkColor: const Color(0xFF00AD48),
        labelStyle: TextStyle(
          color: isSelected ? const Color(0xFF00AD48) : Colors.grey[700],
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
        side: BorderSide(
          color: isSelected ? const Color(0xFF00AD48) : Colors.grey[300]!,
        ),
      ),
    );
  }
}
