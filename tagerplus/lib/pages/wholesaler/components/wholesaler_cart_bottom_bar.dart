import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/utils/snackbar.dart';
import 'package:tagerplus/pages/cart/cart_details.dart';
import 'package:tagerplus/pages/wholesaler/wholesaler_details_controller.dart';

class WholesalerCartBottomBar extends GetView<WholesalerDetailsController> {
  const WholesalerCartBottomBar({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Container(
        color: Colors.transparent,
        padding: const EdgeInsets.fromLTRB(16, 8, 16, 12),
        child: Directionality(
          textDirection: TextDirection.rtl,
          child: Obx(() {
            final count = controller.cartItemCount;
            final price = controller.cartTotalPrice;
            final minCharge = controller.mincharge.value?.minCharge ?? 0.0;
            final String itemsLabel = count == 1 ? 'عنصر' : 'عناصر';

            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(14),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.06),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // Totals (right side in RTL)
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: const Color(
                                  0xFF00AD48,
                                ).withOpacity(0.12),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                '$count $itemsLabel',
                                style: const TextStyle(
                                  color: Color(0xFF00AD48),
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                            ),
                            const SizedBox(width: 10),
                            Text(
                              '${price.toStringAsFixed(2)} جنيه',
                              style: const TextStyle(
                                fontWeight: FontWeight.w700,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 6),
                        if (minCharge > 0) ...[
                          // Gentle progress towards minimum charge
                          ClipRRect(
                            borderRadius: BorderRadius.circular(6),
                            child: LinearProgressIndicator(
                              minHeight: 6,
                              value: (price / minCharge).clamp(0.0, 1.0),
                              backgroundColor: const Color(
                                0xFF00AD48,
                              ).withOpacity(0.12),
                              valueColor: const AlwaysStoppedAnimation<Color>(
                                Color(0xFF00AD48),
                              ),
                            ),
                          ),
                          const SizedBox(height: 6),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.info_outline,
                                size: 14,
                                color: Colors.black45,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                'الحد الأدنى: ${minCharge.toStringAsFixed(2)} جنيه',
                                style: const TextStyle(
                                  color: Colors.black54,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ] else ...[
                          TextButton(
                            onPressed: () {
                              MySnackbar.show(title: 'السلة', message: 'سيتم توجيهك إلى صفحة السلة قريباً');
                            },
                            child: const Text(
                              'سيتم احتساب رسوم الشحن عند الدفع',
                              style: TextStyle(
                                color: Colors.black54,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  const SizedBox(width: 12),

                  // CTA button (left side in RTL)
                  ConstrainedBox(
                    constraints: const BoxConstraints(minWidth: 140),
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Get.to(
                          () => CartDetailsPage(
                            wholesalerId:
                                Get.find<WholesalerDetailsController>().id,
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF00AD48),
                        foregroundColor: Colors.white,
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 14,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        textStyle: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      icon: const Icon(Icons.shopping_cart_outlined, size: 20),
                      label: const Text('متابعة إلى السلة'),
                    ),
                  ),
                ],
              ),
            );
          }),
        ),
      ),
    );
  }
}
