import 'package:flutter/material.dart';
import 'package:tagerplus/components/network_image.dart';
import 'package:tagerplus/core/api/models/wholesaler_schema.dart';

class WholesalerHeader extends StatelessWidget {
  final WholesalerSchema wholesaler;

  const WholesalerHeader({super.key, required this.wholesaler});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Stack(
          clipBehavior: Clip.none,
          alignment: Alignment.bottomCenter,
          children: [
            // Background Image
            if (wholesaler.backgroundImage != null)
              Container(
                height: 180,
                width: double.infinity,
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.vertical(
                    bottom: Radius.circular(20),
                  ),
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.vertical(
                    bottom: Radius.circular(20),
                  ),
                  child: NetworkImageWithLoader(
                    wholesaler.backgroundImage!,
                    fit: BoxFit.cover,
                  ),
                ),
              )
            else
              Container(
                height: 120,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: const BorderRadius.vertical(
                    bottom: Radius.circular(20),
                  ),
                ),
              ),

            // Logo
            Positioned(
              bottom: -50,
              child: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Theme.of(context).scaffoldBackgroundColor,
                    width: 4,
                  ),
                ),
                child: CircleAvatar(
                  radius: 50,
                  backgroundColor: Colors.grey[300],
                  backgroundImage: wholesaler.logo != null
                      ? NetworkImage(wholesaler.logo!)
                      : null,
                  child: wholesaler.logo == null
                      ? const Icon(Icons.store, size: 40, color: Colors.grey)
                      : null,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 60),

        // Wholesaler Info
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Column(
            children: [
              Text(
                wholesaler.title,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              // const SizedBox(height: 4),
              // Text(
              //   '@${wholesaler.username}',
              //   style: Theme.of(context).textTheme.titleMedium?.copyWith(
              //         color: Colors.grey[600],
              //       ),
              // ),
              const SizedBox(height: 12),
              // You can add more details like rating, location, etc. here
            ],
          ),
        ),
        const Divider(height: 32, thickness: 1, indent: 16, endIndent: 16),
      ],
    );
  }
}
