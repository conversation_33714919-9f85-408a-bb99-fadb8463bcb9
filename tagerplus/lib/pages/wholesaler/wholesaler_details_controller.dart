import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/api/models/item_schema.dart';
import 'package:tagerplus/core/api/models/region_min_charge_schema.dart';
import 'package:tagerplus/core/api/models/wholesaler_schema.dart';
import 'package:tagerplus/core/api/models/item_out.dart';
import 'package:tagerplus/core/api/models/item_product_out.dart';
import 'package:tagerplus/services/api.dart';
import 'package:tagerplus/services/auth.dart';
import 'package:tagerplus/services/cart.dart';
import 'package:tagerplus/core/utils/snackbar.dart';

enum SortOption { priceAsc, priceDesc, nameAsc, nameDesc, none }

class WholesalerDetailsController extends GetxController {
  static WholesalerDetailsController get to => Get.find();

  final int id;
  WholesalerDetailsController({required this.id});

  final wholesaler = Rxn<WholesalerSchema>();
  final items = RxList<ItemSchema>();
  final mincharge = Rxn<RegionMinChargeSchema>();
  final isLoading = true.obs;
  final error = RxnString();

  final searchQuery = ''.obs;
  final selectedCategory = RxnString();
  final selectedSort = SortOption.nameAsc.obs;

  // Cart service
  final CartService _cartService = CartService.to;

  @override
  void onInit() {
    super.onInit();
    _loadWholesalerData();
  }

  Future<void> _loadWholesalerData() async {
    try {
      isLoading(true);
      error.value = null;

      final results = await Future.wait([
        ApiService.to.apiClient.wholesalers.getWholesaler(id),
        ApiService.to.apiClient.items.listItems(wholesalerId: id),
        ApiService.to.apiClient.wholesalers.listMinCharges(
          wholesalerId: id,
          regionId: AuthService.to.currentStore.value?.cityId,
        ),
      ]);

      wholesaler.value = (results[0] as dynamic).data;
      items.value = (results[1] as dynamic).data?.data ?? [];
      mincharge.value = (results[2] as dynamic).data?.first;
    } catch (e) {
      if (kDebugMode) {
        print('Wholesaler details error: $e');
      }
      error.value = e.toString();
      MySnackbar.show(title: "خطأ", message: e.toString());
    } finally {
      isLoading(false);
    }
  }

  Future<void> refreshData() async {
    await _loadWholesalerData();
  }

  void onSearchChanged(String query) {
    searchQuery.value = query.toLowerCase();
  }

  void onCategorySelected(String? category) {
    selectedCategory.value = category;
  }

  void onSortChanged(SortOption sortOption) {
    selectedSort.value = sortOption;
  }

  List<ItemSchema> get filteredItems {
    List<ItemSchema> productList = items;

    // TODO: Implement category filtering when category data is available
    // if (selectedCategory.value != null) {
    //   productList = productList
    //       .where((p) => p.product?.categoryId.toString() == selectedCategory.value)
    //       .toList();
    // }

    if (searchQuery.value.isNotEmpty) {
      productList = productList.where((product) {
        final productName = product.product?.title.toLowerCase() ?? '';
        final query = searchQuery.value;
        return productName.contains(query);
      }).toList();
    }

    switch (selectedSort.value) {
      case SortOption.priceAsc:
        productList.sort((a, b) => a.basePrice.compareTo(b.basePrice));
        break;
      case SortOption.priceDesc:
        productList.sort((a, b) => b.basePrice.compareTo(a.basePrice));
        break;
      case SortOption.nameAsc:
        productList.sort(
          (a, b) => (a.product?.title ?? '').compareTo(b.product?.title ?? ''),
        );
        break;
      case SortOption.nameDesc:
        productList.sort(
          (a, b) => (b.product?.title ?? '').compareTo(a.product?.title ?? ''),
        );
        break;
      case SortOption.none:
        break;
    }

    return productList;
  }

  // Cart functionality
  bool isInCart(ItemSchema item) {
    if (!_cartService.cart.containsKey(id)) return false;
    return _cartService.cart[id]!.any(
      (cartItem) => cartItem.item.id == item.id,
    );
  }

  int getCartQuantity(ItemSchema item) {
    if (!_cartService.cart.containsKey(id)) return 0;
    final cartItem = _cartService.cart[id]!.firstWhereOrNull(
      (cartItem) => cartItem.item.id == item.id,
    );
    return cartItem?.quantity.value ?? 0;
  }

  void addToCart(ItemSchema item) {
    try {
      final itemOut = _convertToItemOut(item);
      _cartService.addToCart(id, itemOut);
      MySnackbar.show(
        title: "تم بنجاح",
        message: "تم إضافة ${item.product?.title ?? 'المنتج'} إلى السلة",
      );
    } catch (e) {
      MySnackbar.show(title: "خطأ", message: "فشل في إضافة المنتج إلى السلة");
    }
  }

  void removeFromCart(ItemSchema item) {
    try {
      final itemOut = _convertToItemOut(item);
      _cartService.removeFromCart(id, itemOut);
    } catch (e) {
      MySnackbar.show(title: "خطأ", message: "فشل في إزالة المنتج من السلة");
    }
  }

  int get cartItemCount {
    if (!_cartService.cart.containsKey(id)) return 0;
    return _cartService.cart[id]!.fold<int>(
      0,
      (sum, cartItem) => sum + cartItem.quantity.value,
    );
  }

  double get cartTotalPrice {
    if (!_cartService.cart.containsKey(id)) return 0.0;
    return _cartService.cart[id]!.fold<double>(
      0.0,
      (sum, cartItem) =>
          sum + (cartItem.item.basePrice * cartItem.quantity.value),
    );
  }

  // Helper method to convert ItemSchema to ItemOut for cart compatibility
  ItemOut _convertToItemOut(ItemSchema item) {
    return ItemOut(
      id: item.id,
      product: ItemProductOut(
        id: item.product?.id ?? 0,
        name: item.product?.title ?? '',
        title: item.product?.title ?? '',
        barcode: '', // Not available in ItemProductSchema
      ),
      basePrice: item.basePrice,
      inventoryCount: item.inventoryCount,
      minimumOrderQuantity: item.minimumOrderQuantity,
      maximumOrderQuantity: item.maximumOrderQuantity,
      createdAt: DateTime.now().toIso8601String(),
      updatedAt: DateTime.now().toIso8601String(),
    );
  }
}
