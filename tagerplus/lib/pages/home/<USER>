import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/utils/snackbar.dart';
import 'package:tagerplus/core/api/models/models.dart';
import 'package:tagerplus/services/api.dart';
import 'package:tagerplus/services/auth.dart';

class HomeController extends GetxController {
  // Navigation state
  final RxInt currentNavIndex = 0.obs;

  // Wholesalers data
  final RxList<WholesalerSchema> wholesalers = <WholesalerSchema>[].obs;
  final RxMap<int, RegionMinChargeSchema> minCharges =
      <int, RegionMinChargeSchema>{}.obs;
  final RxList<ProductSchema> products = <ProductSchema>[].obs;

  // Loading and error states
  final RxBool _isLoading = false.obs;
  final RxBool _hasError = false.obs;
  final RxString _errorMessage = ''.obs;

  // Getters
  bool get isLoading => _isLoading.value;
  bool get hasError => _hasError.value;
  String get errorMessage => _errorMessage.value;
  int get currentIndex => currentNavIndex.value;
  set currentIndex(int index) => currentNavIndex.value = index;

  final banners = <String>[].obs;

  @override
  void onInit() {
    super.onInit();
    fetchWholesalers();
    fetchProducts();
    fetchBanners();
  }

  // Search related state
  final RxString _searchQuery = ''.obs;
  final RxList<ProductSchema> _searchResults = <ProductSchema>[].obs;
  Timer? _debounce;

  // Getters for search
  String get searchQuery => _searchQuery.value;
  List<ProductSchema> get searchResults => _searchResults.toList();
  bool get isSearching => _searchQuery.isNotEmpty;

  Future<void> fetchBanners() async {
    final resp = await ApiService.to.apiClient.index.getBanners(
      regionId: AuthService.to.currentStore.value?.cityId,
    );
    banners.value = (resp.data ?? []).map((e) => e.image).toList();
  }

  // Handle search with debouncing
  void onSearchChanged(String query) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();

    _debounce = Timer(const Duration(milliseconds: 500), () {
      final trimmedQuery = query.trim();
      _searchQuery.value = trimmedQuery;
      if (trimmedQuery.isEmpty) {
        _searchResults.clear();
      } else {
        _performSearch(trimmedQuery);
      }
    });
  }

  // Perform the actual search
  Future<void> _performSearch(String query) async {
    if (query.isEmpty) {
      _searchResults.clear();
      return;
    }

    final queryLower = query.toLowerCase();
    final resp = await ApiService.to.apiClient.products.listProducts(
      regionId: AuthService.to.currentStore.value?.cityId,
      search: queryLower,
    );
    _searchResults.value = resp.data?.products ?? [];
  }

  @override
  void onClose() {
    _debounce?.cancel();
    super.onClose();
  }

  // Data fetching methods
  Future<void> fetchWholesalers() async {
    if (_isLoading.value) return;

    _isLoading.value = true;
    _hasError.value = false;
    _errorMessage.value = '';

    try {
      // Get user's region from their store
      final userStore = AuthService.to.stores.isNotEmpty
          ? AuthService.to.stores.first
          : null;

      final regionId = userStore?.cityId;

      if (kDebugMode) {
        print('Fetching wholesalers for region: $regionId');
      }

      final response = await ApiService.to.apiClient.wholesalers
          .listWholesalers(regionId: regionId);

      if (response.data?.data != null) {
        wholesalers.value = response.data!.data;

        // Fetch minimum charges for each wholesaler
        await _fetchMinCharges();

        if (kDebugMode) {
          print('Successfully loaded ${wholesalers.length} wholesalers');
        }
      } else {
        wholesalers.clear();
      }
    } catch (e, s) {
      _hasError.value = true;
      _errorMessage.value = e.toString();

      if (kDebugMode) {
        print('Error fetching wholesalers: $e');
        print('Stack trace: ${s}');
      }

      MySnackbar.show(title: 'خطأ', message: 'حصل خطأ أثناء تحميل التجّار');
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> _fetchMinCharges() async {
    minCharges.clear();

    for (final wholesaler in wholesalers) {
      try {
        // Note: The API structure might need adjustment based on actual implementation
        // This assumes there's a regionId property on wholesaler or we use the user's region
        final userStore = AuthService.to.stores.isNotEmpty
            ? AuthService.to.stores.first
            : null;
        final regionId = userStore?.cityId;

        if (regionId != null) {
          final response = await ApiService.to.apiClient.wholesalers
              .listMinCharges(regionId: regionId, wholesalerId: wholesaler.id);

          if (response.data != null && response.data!.isNotEmpty) {
            minCharges[wholesaler.id] = response.data!.first;
          }
        }
      } catch (e) {
        if (kDebugMode) {
          print(
            'Error fetching min charge for wholesaler ${wholesaler.id}: $e',
          );
        }
        // Continue with other wholesalers even if one fails
      }
    }
  }

  Future<void> fetchProducts() async {
    final resp = await ApiService.to.apiClient.products.listProducts(
      regionId: AuthService.to.currentStore.value?.cityId,
    );
    products.value = resp.data?.products ?? [];
  }

  // Utility methods
  RegionMinChargeSchema? getMinChargeForWholesaler(int wholesalerId) {
    return minCharges[wholesalerId];
  }
}
