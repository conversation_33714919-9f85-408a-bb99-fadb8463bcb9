{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Pro_Grocery-main",
            "cwd": "Pro_Grocery-main",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "Pro_Grocery-main (profile mode)",
            "cwd": "Pro_Grocery-main",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "Pro_Grocery-main (release mode)",
            "cwd": "Pro_Grocery-main",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "tagerplus",
            "cwd": "tagerplus",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "tagerplus (profile mode)",
            "cwd": "tagerplus",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "tagerplus (release mode)",
            "cwd": "tagerplus",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "wholesaler",
            "cwd": "wholesaler",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "wholesaler (profile mode)",
            "cwd": "wholesaler",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "wholesaler (release mode)",
            "cwd": "wholesaler",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        }
    ]
}