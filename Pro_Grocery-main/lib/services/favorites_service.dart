// Uses shared_preferences for local storage
import 'package:shared_preferences/shared_preferences.dart';

class FavoritesService {
  static const _favoritesKey = 'favorite_product_ids';
  static FavoritesService? _instance;
  static FavoritesService get instance => _instance ??= FavoritesService._();

  FavoritesService._();

  Future<SharedPreferences> get _prefs async =>
      await SharedPreferences.getInstance();

  Future<List<int>> getFavorites() async {
    final prefs = await _prefs;
    return prefs.getStringList(_favoritesKey)?.map(int.parse).toList() ?? [];
  }

  Future<bool> isFavorite(int productId) async {
    final favorites = await getFavorites();
    return favorites.contains(productId);
  }

  Future<void> addFavorite(int productId) async {
    final prefs = await _prefs;
    final favorites = await getFavorites();
    if (!favorites.contains(productId)) {
      favorites.add(productId);
      await prefs.setStringList(
          _favoritesKey, favorites.map((e) => e.toString()).toList());
    }
  }

  Future<void> removeFavorite(int productId) async {
    final prefs = await _prefs;
    final favorites = await getFavorites();
    favorites.remove(productId);
    await prefs.setStringList(
        _favoritesKey, favorites.map((e) => e.toString()).toList());
  }

  Future<void> toggleFavorite(int productId) async {
    if (await isFavorite(productId)) {
      await removeFavorite(productId);
    } else {
      await addFavorite(productId);
    }
  }
}
