import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import '../api/regions.dart';

class RegionService extends ChangeNotifier {
  static const String _selectedRegionKey = 'selected_region';
  static const String _regionsListKey = 'regions_list';
  static const String _lastUpdateKey = 'regions_last_update';

  // Cache duration - 24 hours
  static const Duration _cacheExpiry = Duration(hours: 24);

  RegionModel? _selectedRegion;
  List<RegionModel> _allRegions = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  RegionModel? get selectedRegion => _selectedRegion;
  List<RegionModel> get allRegions => _allRegions;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasSelectedRegion => _selectedRegion != null;

  // Default region ID to use if none selected (you can change this)
  int get defaultRegionId => _selectedRegion?.id ?? 1;

  /// Initialize the service by loading saved region
  Future<void> initialize() async {
    await _loadSavedRegion();
    await _loadCachedRegions();
  }

  /// Load saved region from SharedPreferences
  Future<void> _loadSavedRegion() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final regionJson = prefs.getString(_selectedRegionKey);

      if (regionJson != null) {
        final regionMap = json.decode(regionJson) as Map<String, dynamic>;
        _selectedRegion = RegionModel.fromJson(regionMap);
        notifyListeners();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading saved region: $e');
      }
    }
  }

  /// Load cached regions from SharedPreferences
  Future<void> _loadCachedRegions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final regionsJson = prefs.getString(_regionsListKey);
      final lastUpdateStr = prefs.getString(_lastUpdateKey);

      if (regionsJson != null && lastUpdateStr != null) {
        final lastUpdate = DateTime.parse(lastUpdateStr);
        final now = DateTime.now();

        // Check if cache is still valid
        if (now.difference(lastUpdate) < _cacheExpiry) {
          final regionsList = json.decode(regionsJson) as List;
          _allRegions = regionsList
              .map((regionMap) => RegionModel.fromJson(regionMap))
              .toList();
          notifyListeners();
          return;
        }
      }

      // Cache is expired or doesn't exist, fetch fresh data
      await fetchRegions();
    } catch (e) {
      if (kDebugMode) {
        print('Error loading cached regions: $e');
      }
      // If cache loading fails, try to fetch fresh data
      await fetchRegions();
    }
  }

  /// Fetch regions from API and cache them
  Future<void> fetchRegions({bool forceRefresh = false}) async {
    if (_isLoading) return;

    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // If we have cached data and not forcing refresh, return early
      if (!forceRefresh && _allRegions.isNotEmpty) {
        _isLoading = false;
        notifyListeners();
        return;
      }

      final regions = await RegionsApiService.getAllRegions();
      _allRegions = regions;

      // Cache the regions
      await _cacheRegions();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();

      if (kDebugMode) {
        print('Error fetching regions: $e');
      }
    }
  }

  /// Cache regions in SharedPreferences
  Future<void> _cacheRegions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final regionsJson =
          json.encode(_allRegions.map((region) => region.toJson()).toList());

      await prefs.setString(_regionsListKey, regionsJson);
      await prefs.setString(_lastUpdateKey, DateTime.now().toIso8601String());
    } catch (e) {
      if (kDebugMode) {
        print('Error caching regions: $e');
      }
    }
  }

  /// Select a region and save it
  Future<void> selectRegion(RegionModel region) async {
    try {
      _selectedRegion = region;

      final prefs = await SharedPreferences.getInstance();
      final regionJson = json.encode(region.toJson());
      await prefs.setString(_selectedRegionKey, regionJson);

      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('Error saving selected region: $e');
      }
      rethrow;
    }
  }

  /// Clear selected region
  Future<void> clearSelectedRegion() async {
    try {
      _selectedRegion = null;

      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_selectedRegionKey);

      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing selected region: $e');
      }
    }
  }

  /// Get regions by type
  List<RegionModel> getRegionsByType(String type) {
    return _allRegions.where((region) => region.type == type).toList();
  }

  /// Get countries
  List<RegionModel> get countries => getRegionsByType('COUNTRY');

  /// Get states
  List<RegionModel> get states => getRegionsByType('STATE');

  /// Get districts
  List<RegionModel> get districts => getRegionsByType('DISTRICT');

  /// Search regions by name
  List<RegionModel> searchRegions(String query) {
    if (query.isEmpty) return _allRegions;

    final lowercaseQuery = query.toLowerCase();
    return _allRegions.where((region) {
      return region.name.toLowerCase().contains(lowercaseQuery) ||
          region.hierarchicalName.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  /// Clear cache and refresh
  Future<void> refreshRegions() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_regionsListKey);
    await prefs.remove(_lastUpdateKey);

    _allRegions.clear();
    await fetchRegions(forceRefresh: true);
  }

  /// Check if region selection is required
  /// Returns true if no region is selected and we need user to select one
  bool get isRegionSelectionRequired => _selectedRegion == null;

  /// Get display name for current region
  String get currentRegionDisplayName {
    return _selectedRegion?.displayName ?? 'اختر المنطقة';
  }

  /// Clear all data (for logout/reset)
  Future<void> clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_selectedRegionKey);
      await prefs.remove(_regionsListKey);
      await prefs.remove(_lastUpdateKey);

      _selectedRegion = null;
      _allRegions.clear();
      _error = null;
      _isLoading = false;

      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing region data: $e');
      }
    }
  }
}
