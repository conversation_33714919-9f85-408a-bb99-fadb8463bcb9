import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';

import 'core/themes/app_themes.dart';
import 'services/app_services.dart';
import 'services/cart_service.dart';
import 'services/region_service.dart';
import 'services/store_service.dart';
import 'views/splash/splash_screen.dart';
// import 'package:sentry_flutter/sentry_flutter.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize global services
  await AppServices().initialize();

  // await SentryFlutter.init(
  //   (options) {
  //     options.dsn =
  //         'https://<EMAIL>/4509757745987664';
  //     // Adds request headers and IP for users, for more info visit:
  //     // https://docs.sentry.io/platforms/dart/guides/flutter/data-management/data-collected/
  //     options.sendDefaultPii = true;
  //     // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing.
  //     // We recommend adjusting this value in production.
  //     options.tracesSampleRate = 1.0;
  //     // The sampling rate for profiling is relative to tracesSampleRate
  //     // Setting to 1.0 will profile 100% of sampled transactions:
  //     options.profilesSampleRate = 1.0;
  //     // Disable Session Replay to prevent screenshot errors
  //     options.replay.sessionSampleRate = 0.0;
  //     options.replay.onErrorSampleRate = 0.0;
  //   },
  //   appRunner: () => runApp(SentryWidget(child: const MyApp())),
  // );
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<CartService>.value(
          value: AppServices().cartService,
        ),
        ChangeNotifierProvider<RegionService>.value(
          value: AppServices().regionService,
        ),
        ChangeNotifierProvider<StoreService>.value(
          value: AppServices().storeService,
        ),
        ChangeNotifierProvider.value(
          value: AppServices().multiCartService,
        ),
      ],
      child: MaterialApp(
        title: 'Tager Plus',
        theme: AppTheme.defaultTheme,
        home: const SplashScreen(),
        locale: const Locale('ar', 'EG'),
        debugShowCheckedModeBanner: false,
        supportedLocales: const [
          Locale('ar', 'EG'),
        ],
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        localeResolutionCallback: (locale, supportedLocales) {
          return const Locale('ar', 'EG');
        },
      ),
    );
  }
}
