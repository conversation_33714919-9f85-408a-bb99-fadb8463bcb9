import 'package:flutter/material.dart';

import '../../core/components/app_back_button.dart';
import '../../core/constants/constants.dart';
import '../../services/user_service.dart';
import '../../api/auth/user.dart';
import '../../services/auth.dart';
import '../../core/utils/validators.dart';
import '../../utils/navigation.dart' as nav;
import '../../views/onboarding/onboarding_page.dart';

class ProfileEditPage extends StatefulWidget {
  const ProfileEditPage({super.key});

  @override
  State<ProfileEditPage> createState() => _ProfileEditPageState();
}

class _ProfileEditPageState extends State<ProfileEditPage> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();

  UserData? userData;
  bool isLoading = true;
  bool isUpdating = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    final user = await UserService.getCurrentUser();
    if (mounted && user != null) {
      setState(() {
        userData = user;
        _firstNameController.text = user.firstName ?? '';
        _lastNameController.text = user.lastName ?? '';
        _emailController.text = user.email ?? '';
        _phoneController.text = user.phone;
        isLoading = false;
      });
    } else if (mounted) {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _updateProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      isUpdating = true;
    });

    try {
      final request = UserUpdateRequest(
        firstName: _firstNameController.text.trim().isEmpty
            ? null
            : _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim().isEmpty
            ? null
            : _lastNameController.text.trim(),
        email: _emailController.text.trim().isEmpty
            ? null
            : _emailController.text.trim(),
        phone: _phoneController.text.trim().isEmpty
            ? null
            : _phoneController.text.trim(),
      );

      final response = await UserService.updateCurrentUser(request);

      if (mounted) {
        if (response.success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديث الملف الشخصي بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(
              context, true); // Return true to indicate successful update
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.error ?? 'فشل في تحديث الملف الشخصي'),
              backgroundColor: Colors.red,
            ),
          );

          // If authentication failed, logout user
          if (response.error?.contains('Authentication') == true) {
            await AuthService.logout();
            nav.Router.pushAndRemoveUntil(context, const OnboardingPage());
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isUpdating = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.cardColor,
      appBar: AppBar(
        leading: const AppBackButton(),
        title: const Text(
          'الملف الشخصي',
        ),
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Container(
                margin: const EdgeInsets.all(AppDefaults.padding),
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDefaults.padding,
                  vertical: AppDefaults.padding * 2,
                ),
                decoration: BoxDecoration(
                  color: AppColors.scaffoldBackground,
                  borderRadius: AppDefaults.borderRadius,
                ),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      /* <----  First Name -----> */
                      const Text("الاسم الأول"),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _firstNameController,
                        keyboardType: TextInputType.text,
                        textInputAction: TextInputAction.next,
                        enabled: !isUpdating,
                        decoration: const InputDecoration(
                          hintText: 'أدخل اسمك الأول',
                        ),
                      ),
                      const SizedBox(height: AppDefaults.padding),

                      /* <---- Last Name -----> */
                      const Text("اسم العائلة"),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _lastNameController,
                        keyboardType: TextInputType.text,
                        textInputAction: TextInputAction.next,
                        enabled: !isUpdating,
                        decoration: const InputDecoration(
                          hintText: 'أدخل اسم عائلتك',
                        ),
                      ),
                      const SizedBox(height: AppDefaults.padding),

                      /* <---- Email -----> */
                      const Text("البريد الإلكتروني (اختياري)"),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _emailController,
                        keyboardType: TextInputType.emailAddress,
                        textInputAction: TextInputAction.next,
                        enabled: !isUpdating,
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            return Validators.email.call(value);
                          }
                          return null;
                        },
                        decoration: const InputDecoration(
                          hintText: 'أدخل عنوان بريدك الإلكتروني',
                        ),
                      ),
                      const SizedBox(height: AppDefaults.padding),

                      /* <---- Phone Number -----> */
                      const Text("رقم الهاتف"),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _phoneController,
                        keyboardType: TextInputType.phone,
                        textInputAction: TextInputAction.done,
                        enabled: !isUpdating,
                        validator:
                            Validators.requiredWithFieldName('رقم الهاتف').call,
                        decoration: const InputDecoration(
                          hintText: 'أدخل رقم هاتفك',
                        ),
                      ),
                      const SizedBox(height: AppDefaults.padding),

                      /* <---- Phone Verification Status -----> */
                      if (userData?.phoneVerified == false)
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.orange.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                                color: Colors.orange.withOpacity(0.3)),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.warning,
                                  color: Colors.orange, size: 20),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'رقم الهاتف غير موثق',
                                  style: TextStyle(color: Colors.orange[800]),
                                ),
                              ),
                            ],
                          ),
                        ),
                      const SizedBox(height: AppDefaults.padding * 2),

                      /* <---- Submit -----> */
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: isUpdating ? null : _updateProfile,
                          child: isUpdating
                              ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child:
                                      CircularProgressIndicator(strokeWidth: 2),
                                )
                              : const Text('حفظ التغييرات'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }
}
