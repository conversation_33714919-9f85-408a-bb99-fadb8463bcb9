import 'package:flutter/material.dart';
import 'package:grocery/services/auth.dart';

import '../../../core/constants/constants.dart';
import '../../../utils/navigation.dart' as nav;
import '../../../views/profile/profile_edit_page.dart';
import '../../../views/auth/login_or_signup_page.dart';
import 'profile_list_tile.dart';

class ProfileMenuOptions extends StatelessWidget {
  const ProfileMenuOptions({
    super.key,
    this.onProfileEdit,
  });

  final VoidCallback? onProfileEdit;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppDefaults.padding),
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: AppDefaults.boxShadow,
        borderRadius: AppDefaults.borderRadius,
      ),
      child: Column(
        children: [
          ProfileListTile(
            title: 'ملفي الشخصي',
            icon: AppIcons.profilePerson,
            onTap: () async {
              final result = await nav.Router.pushForResult(
                  context, const ProfileEditPage());
              if (result == true && onProfileEdit != null) {
                onProfileEdit!();
              }
            },
          ),
          // const Divider(thickness: 0.1),
          // ProfileListTile(
          //   title: 'الإشعارات',
          //   icon: AppIcons.profileNotification,
          //   onTap: () => Navigator.pushNamed(context, AppRoutes.notifications),
          // ),
          // const Divider(thickness: 0.1),
          // ProfileListTile(
          //   title: 'الإعدادات',
          //   icon: AppIcons.profileSetting,
          //   onTap: () => Navigator.pushNamed(context, AppRoutes.settings),
          // ),
          // const Divider(thickness: 0.1),
          // ProfileListTile(
          //   title: 'المدفوعات',
          //   icon: AppIcons.profilePayment,
          //   onTap: () => Navigator.pushNamed(context, AppRoutes.paymentMethod),
          // ),
          const Divider(thickness: 0.1),
          ProfileListTile(
            title: 'تسجيل الخروج',
            icon: AppIcons.profileLogout,
            onTap: () async {
              await AuthService.logout();
              if (context.mounted) {
                nav.Router.pushAndRemoveUntil(
                    context, const LoginOrSignUpPage());
              }
            },
          ),
        ],
      ),
    );
  }
}
