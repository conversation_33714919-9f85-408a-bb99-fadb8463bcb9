import 'package:flutter/material.dart';

import '../../core/constants/app_colors.dart';
import '../../services/user_service.dart';
import 'components/profile_header.dart';
import 'components/profile_menu_options.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  Key _profileHeaderKey = UniqueKey();

  void _refreshProfile() {
    // Force profile header to rebuild by changing its key
    setState(() {
      _profileHeaderKey = UniqueKey();
    });
    // Also clear cache to force fresh data
    UserService.clearCache();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.cardColor,
      child: <PERSON>umn(
        children: [
          ProfileHeader(key: _profileHeader<PERSON>ey),
          ProfileMenuOptions(onProfileEdit: _refreshProfile),
        ],
      ),
    );
  }
}
