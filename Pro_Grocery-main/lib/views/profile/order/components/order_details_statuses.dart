import 'package:flutter/material.dart';

import '../../../../api/orders_api.dart';
import 'order_status_row.dart';

class OrderStatusColumn extends StatelessWidget {
  const OrderStatusColumn({
    super.key,
    required this.order,
  });

  final OrderResponse order;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: _buildStatusRows(),
    );
  }

  List<Widget> _buildStatusRows() {
    final List<Widget> statusRows = [];
    final statuses = [
      OrderStatus.pending,
      OrderStatus.processing,
      OrderStatus.shipped,
      OrderStatus.delivered,
    ];

    for (int i = 0; i < statuses.length; i++) {
      final status = statuses[i];
      final isActive = _isStatusActive(status);
      final isCompleted = _isStatusCompleted(status);

      statusRows.add(
        OrderStatusRow(
          status: status,
          currentOrderStatus: order.status,
          date: order.formattedDate,
          time: _getStatusTime(status),
          isActive: isActive,
          isCompleted: isCompleted,
          isStart: i == 0,
          isEnd: i == statuses.length - 1,
        ),
      );
    }

    // <PERSON>le cancelled status separately if order is cancelled
    if (order.status == OrderStatus.cancelled) {
      statusRows.add(
        OrderStatusRow(
          status: OrderStatus.cancelled,
          currentOrderStatus: order.status,
          date: order.formattedDate,
          time: _getStatusTime(OrderStatus.cancelled),
          isActive: true,
          isCompleted: true,
          isStart: false,
          isEnd: true,
        ),
      );
    }

    return statusRows;
  }

  bool _isStatusActive(OrderStatus status) {
    final statusOrder = {
      OrderStatus.pending: 0,
      OrderStatus.processing: 1,
      OrderStatus.shipped: 2,
      OrderStatus.delivered: 3,
    };

    final currentOrder = statusOrder[order.status] ?? 0;
    final checkOrder = statusOrder[status] ?? 0;

    return checkOrder <= currentOrder;
  }

  bool _isStatusCompleted(OrderStatus status) {
    final statusOrder = {
      OrderStatus.pending: 0,
      OrderStatus.processing: 1,
      OrderStatus.shipped: 2,
      OrderStatus.delivered: 3,
    };

    final currentOrder = statusOrder[order.status] ?? 0;
    final checkOrder = statusOrder[status] ?? 0;

    return checkOrder < currentOrder;
  }

  String _getStatusTime(OrderStatus status) {
    // In a real app, you would have individual timestamps for each status
    // For now, we'll use the order creation time as a placeholder
    try {
      if (status == OrderStatus.pending) {
        final date = DateTime.parse(order.createdAt);
        return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
      }
      final date = DateTime.parse(order.statusUpdatedAt);
      return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'Unknown';
    }
  }
}
