import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../core/constants/constants.dart';
import '../../../../api/orders_api.dart';
import 'order_details_vertical_step_indicator.dart';

class OrderStatusRow extends StatelessWidget {
  const OrderStatusRow({
    super.key,
    required this.status,
    required this.currentOrderStatus,
    required this.date,
    required this.time,
    this.isActive = false,
    this.isCompleted = false,
    this.isStart = false,
    this.isEnd = false,
  });

  final OrderStatus status;
  final OrderStatus currentOrderStatus;
  final String date;
  final String time;
  final bool isStart;
  final bool isActive;
  final bool isCompleted;
  final bool isEnd;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment:
          isStart ? CrossAxisAlignment.start : CrossAxisAlignment.center,
      children: [
        Container(
          padding: const EdgeInsets.all(8.0),
          decoration: BoxDecoration(
            color: isActive ? _orderColor() : Colors.grey,
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: SvgPicture.asset(_orderIcon()),
        ),
        VerticalStepIndicator(
          isStart: isStart,
          isActive: isActive,
          isEnd: isEnd,
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _orderStatus(),
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.black,
                    ),
              ),
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    date,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  Text(
                    time,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ],
          ),
        )
      ],
    );
  }

  Color _orderColor() {
    switch (status) {
      case OrderStatus.pending:
        return const Color(0xFF45AF2A);
      case OrderStatus.processing:
        return const Color(0xFFEDC125);
      case OrderStatus.shipped:
        return const Color(0xFF2652ED);
      case OrderStatus.delivered:
        return const Color(0xFF30DFB8);
      case OrderStatus.cancelled:
        return const Color(0xFFFF1F1F);
    }
  }

  String _orderStatus() {
    switch (status) {
      case OrderStatus.pending:
        return OrderStatus.pending.displayName;
      case OrderStatus.processing:
        return OrderStatus.processing.displayName;
      case OrderStatus.shipped:
        return OrderStatus.shipped.displayName;
      case OrderStatus.delivered:
        return OrderStatus.delivered.displayName;
      case OrderStatus.cancelled:
        return OrderStatus.cancelled.displayName;
    }
  }

  String _orderIcon() {
    switch (status) {
      case OrderStatus.pending:
        return AppIcons.orderConfirmed;
      case OrderStatus.processing:
        return AppIcons.orderProcessing;
      case OrderStatus.shipped:
        return AppIcons.orderShipped;
      case OrderStatus.delivered:
        return AppIcons.orderDelivered;
      case OrderStatus.cancelled:
        return AppIcons.delete;
    }
  }
}
