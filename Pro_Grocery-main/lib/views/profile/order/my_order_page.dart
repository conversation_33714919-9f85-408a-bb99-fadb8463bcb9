import 'package:flutter/material.dart';

import '../../../core/components/app_back_button.dart';
import '../../../core/constants/app_colors.dart';
import '../../../api/orders_api.dart';
import 'components/custom_tab_label.dart';
import 'components/tab_all.dart';
import 'components/tab_completed.dart';
import 'components/tab_running.dart';

class AllOrderPage extends StatefulWidget {
  const AllOrderPage({super.key});

  @override
  State<AllOrderPage> createState() => _AllOrderPageState();
}

class _AllOrderPageState extends State<AllOrderPage> {
  List<OrderResponse> allOrders = [];
  bool isLoading = false;
  String error = '';

  @override
  void initState() {
    super.initState();
    loadOrders();
  }

  Future<void> loadOrders() async {
    try {
      setState(() {
        isLoading = true;
        error = '';
      });

      final response = await OrdersApiService.getMyOrders(limit: 100);
      setState(() {
        allOrders = response.items;
      });
    } catch (e) {
      setState(() {
        error = 'فشل في تحميل الطلبات: ${e.toString()}';
      });
      print('Error loading orders: $e');
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  List<OrderResponse> get runningOrders {
    return allOrders.where((order) => order.isRunning).toList();
  }

  List<OrderResponse> get completedOrders {
    return allOrders.where((order) => order.isCompleted).toList();
  }

  Future<void> refreshOrders() async {
    await loadOrders();
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        appBar: AppBar(
          leading: const AppBackButton(),
          title: const Text('طلباتي'),
          bottom: TabBar(
            physics: const NeverScrollableScrollPhysics(),
            tabs: [
              CustomTabLabel(label: 'الكل', value: '(${allOrders.length})'),
              CustomTabLabel(label: 'جاري', value: '(${runningOrders.length})'),
              CustomTabLabel(
                  label: 'السابق', value: '(${completedOrders.length})'),
            ],
          ),
        ),
        body: Container(
          color: AppColors.cardColor,
          child: Builder(
            builder: (context) {
              if (isLoading) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              if (error.isNotEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red.shade300,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        error,
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: refreshOrders,
                        child: const Text('إعادة المحاولة'),
                      ),
                    ],
                  ),
                );
              }

              return TabBarView(
                children: [
                  AllTab(orders: allOrders),
                  RunningTab(orders: runningOrders),
                  CompletedTab(orders: completedOrders),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
