import 'package:flutter/material.dart';
import '../../../api/orders_api.dart';

// ============================================================================
// WHOLESALER PROFILE MODELS
// ============================================================================

class WholesalerProfile {
  final int id;
  final String title;
  final String username;
  final String? category;
  final String? logoUrl;
  final String? backgroundImageUrl;

  const WholesalerProfile({
    required this.id,
    required this.title,
    required this.username,
    this.category,
    this.logoUrl,
    this.backgroundImageUrl,
  });

  factory WholesalerProfile.fromJson(Map<String, dynamic> json) {
    return WholesalerProfile(
      id: json['id'],
      title: json['title'],
      username: json['username'],
      category: json['category'],
      logoUrl: json['logo_url'],
      backgroundImageUrl: json['background_image_url'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'username': username,
      'category': category,
      'logo_url': logoUrl,
      'background_image_url': backgroundImageUrl,
    };
  }

  WholesalerProfile copyWith({
    int? id,
    String? title,
    String? username,
    String? category,
    String? logoUrl,
    String? backgroundImageUrl,
  }) {
    return WholesalerProfile(
      id: id ?? this.id,
      title: title ?? this.title,
      username: username ?? this.username,
      category: category ?? this.category,
      logoUrl: logoUrl ?? this.logoUrl,
      backgroundImageUrl: backgroundImageUrl ?? this.backgroundImageUrl,
    );
  }
}

class WholesalerCreateRequest {
  final String category;
  final String title;
  final String username;

  const WholesalerCreateRequest({
    required this.category,
    required this.title,
    required this.username,
  });

  Map<String, dynamic> toJson() {
    return {
      'category': category,
      'title': title,
      'username': username,
    };
  }
}

class WholesalerUpdateRequest {
  final String? category;
  final String? title;
  final String? username;

  const WholesalerUpdateRequest({
    this.category,
    this.title,
    this.username,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (category != null) data['category'] = category;
    if (title != null) data['title'] = title;
    if (username != null) data['username'] = username;
    return data;
  }
}

// ============================================================================
// REGION MIN CHARGE MODELS
// ============================================================================

class RegionMinCharge {
  final int id;
  final RegionInfo region;
  final double minCharge;
  final int minItems;

  const RegionMinCharge({
    required this.id,
    required this.region,
    required this.minCharge,
    required this.minItems,
  });

  factory RegionMinCharge.fromJson(Map<String, dynamic> json) {
    return RegionMinCharge(
      id: json['id'],
      region: RegionInfo.fromJson(json['region']),
      minCharge: double.parse(json['min_charge'].toString()),
      minItems: json['min_items'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'region': region.toJson(),
      'min_charge': minCharge,
      'min_items': minItems,
    };
  }
}

class RegionInfo {
  final int id;
  final String name;
  final String type;
  final String? code;

  const RegionInfo({
    required this.id,
    required this.name,
    required this.type,
    this.code,
  });

  factory RegionInfo.fromJson(Map<String, dynamic> json) {
    return RegionInfo(
      id: json['id'],
      name: json['name'],
      type: json['type'],
      code: json['code'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'code': code,
    };
  }
}

class RegionMinChargeRequest {
  final int regionId;
  final double minCharge;
  final int minItems;

  const RegionMinChargeRequest({
    required this.regionId,
    required this.minCharge,
    required this.minItems,
  });

  Map<String, dynamic> toJson() {
    return {
      'region_id': regionId,
      'min_charge': minCharge,
      'min_items': minItems,
    };
  }
}

// ============================================================================
// COMPANY AND PRODUCT MODELS
// ============================================================================

class Company {
  final int id;
  final String name;
  final String title;
  final String slug;

  const Company({
    required this.id,
    required this.name,
    required this.title,
    required this.slug,
  });

  factory Company.fromJson(Map<String, dynamic> json) {
    if (json['id'] == null) throw ArgumentError('Company id cannot be null');
    if (json['name'] == null || (json['name'] as String).isEmpty) {
      throw ArgumentError('Company name cannot be null or empty');
    }
    if (json['title'] == null || (json['title'] as String).isEmpty) {
      throw ArgumentError('Company title cannot be null or empty');
    }
    if (json['slug'] == null || (json['slug'] as String).isEmpty) {
      throw ArgumentError('Company slug cannot be null or empty');
    }

    return Company(
      id: json['id'] as int,
      name: json['name'] as String,
      title: json['title'] as String,
      slug: json['slug'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'title': title,
      'slug': slug,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Company && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Company(id: $id, name: $name, title: $title)';
}

class Category {
  final int id;
  final String name;
  final String title;
  final String slug;

  const Category({
    required this.id,
    required this.name,
    required this.title,
    required this.slug,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    if (json['id'] == null) throw ArgumentError('Category id cannot be null');
    if (json['name'] == null || (json['name'] as String).isEmpty) {
      throw ArgumentError('Category name cannot be null or empty');
    }
    if (json['title'] == null || (json['title'] as String).isEmpty) {
      throw ArgumentError('Category title cannot be null or empty');
    }
    if (json['slug'] == null || (json['slug'] as String).isEmpty) {
      throw ArgumentError('Category slug cannot be null or empty');
    }

    return Category(
      id: json['id'] as int,
      name: json['name'] as String,
      title: json['title'] as String,
      slug: json['slug'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'title': title,
      'slug': slug,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Category && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Category(id: $id, name: $name, title: $title)';
}

class Product {
  final int id;
  final String name;
  final String title;
  final String barcode;
  final String slug;
  final String description;
  final String? imageUrl;
  final int? companyId;
  final int? categoryId;
  final Company? company;
  final Category? category;
  final String unit;
  final double unitCount;

  const Product({
    required this.id,
    required this.name,
    required this.title,
    required this.barcode,
    required this.slug,
    required this.description,
    this.imageUrl,
    this.companyId,
    this.categoryId,
    this.company,
    this.category,
    required this.unit,
    required this.unitCount,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    if (json['id'] == null) throw ArgumentError('Product id cannot be null');
    if (json['name'] == null || (json['name'] as String).isEmpty) {
      throw ArgumentError('Product name cannot be null or empty');
    }
    if (json['title'] == null || (json['title'] as String).isEmpty) {
      throw ArgumentError('Product title cannot be null or empty');
    }
    if (json['barcode'] == null || (json['barcode'] as String).isEmpty) {
      throw ArgumentError('Product barcode cannot be null or empty');
    }
    if (json['slug'] == null || (json['slug'] as String).isEmpty) {
      throw ArgumentError('Product slug cannot be null or empty');
    }
    if (json['unit'] == null || (json['unit'] as String).isEmpty) {
      throw ArgumentError('Product unit cannot be null or empty');
    }
    if (json['unit_count'] == null) {
      throw ArgumentError('Product unit_count cannot be null');
    }

    return Product(
      id: json['id'] as int,
      name: json['name'] as String,
      title: json['title'] as String,
      barcode: json['barcode'] as String,
      slug: json['slug'] as String,
      description: json['description'] as String? ?? '',
      imageUrl: json['image_url'] as String?,
      companyId: json['company_id'] as int?,
      categoryId: json['category_id'] as int?,
      company: json['company'] != null
          ? Company.fromJson(json['company'] as Map<String, dynamic>)
          : null,
      category: json['category'] != null
          ? Category.fromJson(json['category'] as Map<String, dynamic>)
          : null,
      unit: json['unit'] as String,
      unitCount: (json['unit_count'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'title': title,
      'barcode': barcode,
      'slug': slug,
      'description': description,
      'image_url': imageUrl,
      'company_id': companyId,
      'category_id': categoryId,
      'company': company?.toJson(),
      'category': category?.toJson(),
      'unit': unit,
      'unit_count': unitCount,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Product && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Product(id: $id, name: $name, barcode: $barcode)';
}

// ============================================================================
// WHOLESALER ITEM MODELS
// ============================================================================

class WholesalerItem {
  final int id;
  final ProductInfo product;
  final double basePrice;
  final int inventoryCount;
  final int minimumOrderQuantity;
  final int? maximumOrderQuantity;
  final DateTime? priceExpiry;
  final DateTime createdAt;

  const WholesalerItem({
    required this.id,
    required this.product,
    required this.basePrice,
    required this.inventoryCount,
    required this.minimumOrderQuantity,
    this.maximumOrderQuantity,
    this.priceExpiry,
    required this.createdAt,
  });

  factory WholesalerItem.fromJson(Map<String, dynamic> json) {
    return WholesalerItem(
      id: json['id'],
      product: ProductInfo.fromJson(json['product']),
      basePrice: double.parse(json['base_price'].toString()),
      inventoryCount: json['inventory_count'],
      minimumOrderQuantity: json['minimum_order_quantity'] ?? 1,
      maximumOrderQuantity: json['maximum_order_quantity'],
      priceExpiry: json['price_expiry'] != null
          ? DateTime.parse(json['price_expiry'])
          : null,
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product': product.toJson(),
      'base_price': basePrice,
      'inventory_count': inventoryCount,
      'minimum_order_quantity': minimumOrderQuantity,
      'maximum_order_quantity': maximumOrderQuantity,
      'price_expiry': priceExpiry?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  WholesalerItem copyWith({
    int? id,
    ProductInfo? product,
    double? basePrice,
    int? inventoryCount,
    int? minimumOrderQuantity,
    int? maximumOrderQuantity,
    DateTime? priceExpiry,
    DateTime? createdAt,
  }) {
    return WholesalerItem(
      id: id ?? this.id,
      product: product ?? this.product,
      basePrice: basePrice ?? this.basePrice,
      inventoryCount: inventoryCount ?? this.inventoryCount,
      minimumOrderQuantity: minimumOrderQuantity ?? this.minimumOrderQuantity,
      maximumOrderQuantity: maximumOrderQuantity ?? this.maximumOrderQuantity,
      priceExpiry: priceExpiry ?? this.priceExpiry,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

class ProductInfo {
  final int id;
  final String name;
  final String? barcode;
  final String? imageUrl;

  const ProductInfo({
    required this.id,
    required this.name,
    this.barcode,
    this.imageUrl,
  });

  factory ProductInfo.fromJson(Map<String, dynamic> json) {
    return ProductInfo(
      id: json['id'],
      name: json['name'],
      barcode: json['barcode'],
      imageUrl: json['image_url'],
    );
  }

  /// Create ProductInfo from Product model
  factory ProductInfo.fromProduct(Product product) {
    return ProductInfo(
      id: product.id,
      name: product.name,
      barcode: product.barcode,
      imageUrl: product.imageUrl,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'barcode': barcode,
      'image_url': imageUrl,
    };
  }
}

class WholesalerItemCreateRequest {
  final int productId;
  final double basePrice;
  final int inventoryCount;
  final int minimumOrderQuantity;
  final int? maximumOrderQuantity;
  final DateTime? priceExpiry;

  const WholesalerItemCreateRequest({
    required this.productId,
    required this.basePrice,
    required this.inventoryCount,
    this.minimumOrderQuantity = 1,
    this.maximumOrderQuantity,
    this.priceExpiry,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'product_id': productId,
      'base_price': basePrice,
      'inventory_count': inventoryCount,
      'minimum_order_quantity': minimumOrderQuantity,
    };
    if (maximumOrderQuantity != null) {
      data['maximum_order_quantity'] = maximumOrderQuantity;
    }
    if (priceExpiry != null) {
      data['price_expiry'] = priceExpiry!.toIso8601String();
    }
    return data;
  }
}

class WholesalerItemUpdateRequest {
  final double? basePrice;
  final int? inventoryCount;
  final int? minimumOrderQuantity;
  final int? maximumOrderQuantity;
  final DateTime? priceExpiry;

  const WholesalerItemUpdateRequest({
    this.basePrice,
    this.inventoryCount,
    this.minimumOrderQuantity,
    this.maximumOrderQuantity,
    this.priceExpiry,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (basePrice != null) data['base_price'] = basePrice;
    if (inventoryCount != null) data['inventory_count'] = inventoryCount;
    if (minimumOrderQuantity != null) {
      data['minimum_order_quantity'] = minimumOrderQuantity;
    }
    if (maximumOrderQuantity != null) {
      data['maximum_order_quantity'] = maximumOrderQuantity;
    }
    if (priceExpiry != null) {
      data['price_expiry'] = priceExpiry!.toIso8601String();
    }
    return data;
  }
}

// ============================================================================
// INVENTORY TRANSACTION MODELS
// ============================================================================

enum InventoryTransactionType {
  addition('ADDITION'),
  subtraction('SUBTRACTION');

  const InventoryTransactionType(this.value);
  final String value;

  static InventoryTransactionType fromString(String value) {
    return InventoryTransactionType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => InventoryTransactionType.addition,
    );
  }
}

class InventoryTransaction {
  final int id;
  final InventoryTransactionType transactionType;
  final int quantity;
  final String? notes;
  final DateTime createdAt;

  const InventoryTransaction({
    required this.id,
    required this.transactionType,
    required this.quantity,
    this.notes,
    required this.createdAt,
  });

  factory InventoryTransaction.fromJson(Map<String, dynamic> json) {
    return InventoryTransaction(
      id: json['id'],
      transactionType:
          InventoryTransactionType.fromString(json['transaction_type']),
      quantity: json['quantity'],
      notes: json['notes'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'transaction_type': transactionType.value,
      'quantity': quantity,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

class InventoryTransactionRequest {
  final InventoryTransactionType transactionType;
  final int quantity;
  final String? notes;

  const InventoryTransactionRequest({
    required this.transactionType,
    required this.quantity,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'transaction_type': transactionType.value,
      'quantity': quantity,
      'notes': notes,
    };
  }
}

class InventoryUpdateResponse {
  final bool success;
  final String message;
  final int newInventoryCount;

  const InventoryUpdateResponse({
    required this.success,
    required this.message,
    required this.newInventoryCount,
  });

  factory InventoryUpdateResponse.fromJson(Map<String, dynamic> json) {
    return InventoryUpdateResponse(
      success: json['success'],
      message: json['message'],
      newInventoryCount: json['new_inventory_count'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'new_inventory_count': newInventoryCount,
    };
  }
}

// ============================================================================
// DASHBOARD STATISTICS MODELS
// ============================================================================

class WholesalerStats {
  final int totalProducts;
  final int totalOrders;
  final double totalRevenue;
  final int totalCustomers;
  final int lowStockItems;
  final int expiredItems;

  const WholesalerStats({
    required this.totalProducts,
    required this.totalOrders,
    required this.totalRevenue,
    required this.totalCustomers,
    required this.lowStockItems,
    required this.expiredItems,
  });

  factory WholesalerStats.empty() {
    return const WholesalerStats(
      totalProducts: 0,
      totalOrders: 0,
      totalRevenue: 0.0,
      totalCustomers: 0,
      lowStockItems: 0,
      expiredItems: 0,
    );
  }

  WholesalerStats copyWith({
    int? totalProducts,
    int? totalOrders,
    double? totalRevenue,
    int? totalCustomers,
    int? lowStockItems,
    int? expiredItems,
  }) {
    return WholesalerStats(
      totalProducts: totalProducts ?? this.totalProducts,
      totalOrders: totalOrders ?? this.totalOrders,
      totalRevenue: totalRevenue ?? this.totalRevenue,
      totalCustomers: totalCustomers ?? this.totalCustomers,
      lowStockItems: lowStockItems ?? this.lowStockItems,
      expiredItems: expiredItems ?? this.expiredItems,
    );
  }
}

// ============================================================================
// WHOLESALER ORDER MODELS
// ============================================================================

/// Store owner data for orders
class WholesalerOrderStoreOwner {
  final int id;
  final String username;
  final String email;
  final String firstName;
  final String lastName;
  final String phone;
  final bool phoneVerified;

  const WholesalerOrderStoreOwner({
    required this.id,
    required this.username,
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.phone,
    required this.phoneVerified,
  });

  factory WholesalerOrderStoreOwner.fromJson(Map<String, dynamic> json) {
    return WholesalerOrderStoreOwner(
      id: json['id'],
      username: json['username'],
      email: json['email'],
      firstName: json['first_name'],
      lastName: json['last_name'],
      phone: json['phone'],
      phoneVerified: json['phone_verified'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'first_name': firstName,
      'last_name': lastName,
      'phone': phone,
      'phone_verified': phoneVerified,
    };
  }

  String get fullName => '$firstName $lastName'.trim();
}

/// Region data for store location
class WholesalerOrderRegion {
  final int id;
  final String name;
  final String type;
  final String slug;

  const WholesalerOrderRegion({
    required this.id,
    required this.name,
    required this.type,
    required this.slug,
  });

  factory WholesalerOrderRegion.fromJson(Map<String, dynamic> json) {
    return WholesalerOrderRegion(
      id: json['id'],
      name: json['name'],
      type: json['type'],
      slug: json['slug'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'slug': slug,
    };
  }
}

/// Store data for orders
class WholesalerOrderStore {
  final int id;
  final WholesalerOrderStoreOwner owner;
  final String name;
  final String description;
  final String address;
  final WholesalerOrderRegion city;
  final WholesalerOrderRegion state;
  final WholesalerOrderRegion country;
  final DateTime createdAt;
  final DateTime updatedAt;

  const WholesalerOrderStore({
    required this.id,
    required this.owner,
    required this.name,
    required this.description,
    required this.address,
    required this.city,
    required this.state,
    required this.country,
    required this.createdAt,
    required this.updatedAt,
  });

  factory WholesalerOrderStore.fromJson(Map<String, dynamic> json) {
    return WholesalerOrderStore(
      id: json['id'],
      owner: WholesalerOrderStoreOwner.fromJson(json['owner']),
      name: json['name'],
      description: json['description'],
      address: json['address'],
      city: WholesalerOrderRegion.fromJson(json['city']),
      state: WholesalerOrderRegion.fromJson(json['state']),
      country: WholesalerOrderRegion.fromJson(json['country']),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'owner': owner.toJson(),
      'name': name,
      'description': description,
      'address': address,
      'city': city.toJson(),
      'state': state.toJson(),
      'country': country.toJson(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get fullAddress =>
      '$address, ${city.name}, ${state.name}, ${country.name}';
}

/// Product data for order items
class WholesalerOrderProduct {
  final int id;
  final String name;
  final String title;
  final String barcode;
  final String slug;
  final String description;
  final String? imageUrl;
  final String unit;
  final int unitCount;

  const WholesalerOrderProduct({
    required this.id,
    required this.name,
    required this.title,
    required this.barcode,
    required this.slug,
    required this.description,
    this.imageUrl,
    required this.unit,
    required this.unitCount,
  });

  factory WholesalerOrderProduct.fromJson(Map<String, dynamic> json) {
    return WholesalerOrderProduct(
      id: json['id'],
      name: json['name'],
      title: json['title'],
      barcode: json['barcode'],
      slug: json['slug'],
      description: json['description'],
      imageUrl: json['image_url'],
      unit: json['unit'],
      unitCount: json['unit_count'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'title': title,
      'barcode': barcode,
      'slug': slug,
      'description': description,
      'image_url': imageUrl,
      'unit': unit,
      'unit_count': unitCount,
    };
  }

  /// Get formatted unit display
  String get formattedUnit {
    if (unitCount == 1) {
      return unit;
    }
    return '$unitCount $unit';
  }
}

/// Order item data
class WholesalerOrderItem {
  final int id;
  final WholesalerOrderProduct product;
  final int quantity;
  final double pricePerUnit;
  final double totalPrice;

  const WholesalerOrderItem({
    required this.id,
    required this.product,
    required this.quantity,
    required this.pricePerUnit,
    required this.totalPrice,
  });

  factory WholesalerOrderItem.fromJson(Map<String, dynamic> json) {
    return WholesalerOrderItem(
      id: json['id'],
      product: WholesalerOrderProduct.fromJson(json['product']),
      quantity: json['quantity'],
      pricePerUnit: (json['price_per_unit'] as num).toDouble(),
      totalPrice: (json['total_price'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product': product.toJson(),
      'quantity': quantity,
      'price_per_unit': pricePerUnit,
      'total_price': totalPrice,
    };
  }

  /// Get formatted price per unit
  String get formattedPricePerUnit {
    return '${pricePerUnit.toStringAsFixed(2)} ج.م';
  }

  /// Get formatted total price
  String get formattedTotalPrice {
    return '${totalPrice.toStringAsFixed(2)} ج.م';
  }
}

/// Main wholesaler order model
class WholesalerOrder {
  final int id;
  final WholesalerOrderStore store;
  final double totalPrice;
  final double fees;
  final DateTime deliverAt;
  final double productsTotalPrice;
  final int productsTotalQuantity;
  final OrderStatus status;
  final String? statusReason;
  final DateTime statusUpdatedAt;
  final WholesalerOrderStoreOwner? statusUpdatedBy;
  final DateTime? completedAt;
  final double? finalCompletedPrice;
  final List<WholesalerOrderItem> orderItems;

  const WholesalerOrder({
    required this.id,
    required this.store,
    required this.totalPrice,
    required this.fees,
    required this.deliverAt,
    required this.productsTotalPrice,
    required this.productsTotalQuantity,
    required this.status,
    this.statusReason,
    required this.statusUpdatedAt,
    this.statusUpdatedBy,
    this.completedAt,
    this.finalCompletedPrice,
    required this.orderItems,
  });

  factory WholesalerOrder.fromJson(Map<String, dynamic> json) {
    return WholesalerOrder(
      id: json['id'],
      store: WholesalerOrderStore.fromJson(json['store']),
      totalPrice: (json['total_price'] as num).toDouble(),
      fees: (json['fees'] as num).toDouble(),
      deliverAt: DateTime.parse(json['deliver_at']),
      productsTotalPrice: (json['products_total_price'] as num).toDouble(),
      productsTotalQuantity: json['products_total_quantity'],
      status: OrderStatus.fromString(json['status']),
      statusReason: json['status_reason'],
      statusUpdatedAt: DateTime.parse(json['status_updated_at']),
      statusUpdatedBy: json['status_updated_by'] != null
          ? WholesalerOrderStoreOwner.fromJson(json['status_updated_by'])
          : null,
      completedAt: json['completed_at'] != null
          ? DateTime.parse(json['completed_at'])
          : null,
      finalCompletedPrice: (json['final_completed_price'] as num?)?.toDouble(),
      orderItems: (json['order_items'] as List<dynamic>?)
              ?.map((item) => WholesalerOrderItem.fromJson(item))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'store': store.toJson(),
      'total_price': totalPrice,
      'fees': fees,
      'deliver_at': deliverAt.toIso8601String(),
      'products_total_price': productsTotalPrice,
      'products_total_quantity': productsTotalQuantity,
      'status': status.name,
      'status_reason': statusReason,
      'status_updated_at': statusUpdatedAt.toIso8601String(),
      'status_updated_by': statusUpdatedBy?.toJson(),
      'completed_at': completedAt?.toIso8601String(),
      'final_completed_price': finalCompletedPrice,
      'order_items': orderItems.map((item) => item.toJson()).toList(),
    };
  }

  /// Get formatted delivery date
  String get formattedDeliveryDate {
    return '${deliverAt.day}/${deliverAt.month}/${deliverAt.year}';
  }

  /// Get formatted status updated date
  String get formattedStatusDate {
    return '${statusUpdatedAt.day}/${statusUpdatedAt.month}/${statusUpdatedAt.year}';
  }

  /// Check if order can be accepted
  bool get canAccept => status == OrderStatus.pending;

  /// Check if order can be rejected
  bool get canReject =>
      status == OrderStatus.pending || status == OrderStatus.processing;

  /// Check if order can be cancelled
  bool get canCancel => status == OrderStatus.processing;

  /// Check if order can be completed
  bool get canComplete => status == OrderStatus.processing;

  /// Get status color
  Color get statusColor {
    switch (status) {
      case OrderStatus.pending:
        return const Color(0xFF4044AA);
      case OrderStatus.processing:
        return const Color(0xFF41A954);
      case OrderStatus.shipped:
        return const Color(0xFFE19603);
      case OrderStatus.delivered:
        return const Color(0xFF41AA55);
      case OrderStatus.cancelled:
        return const Color(0xFFFF1F1F);
    }
  }
}

/// Response model for wholesaler orders list
class WholesalerOrdersListResponse {
  final List<WholesalerOrder> orders;

  const WholesalerOrdersListResponse({
    required this.orders,
  });

  factory WholesalerOrdersListResponse.fromJson(Map<String, dynamic> json) {
    return WholesalerOrdersListResponse(
      orders: (json['orders'] as List)
          .map((orderJson) => WholesalerOrder.fromJson(orderJson))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'orders': orders.map((order) => order.toJson()).toList(),
    };
  }
}

/// Request model for completing an order
class CompleteOrderRequest {
  final int orderId;
  final double? finalCompletedPrice;

  const CompleteOrderRequest({
    required this.orderId,
    this.finalCompletedPrice,
  });

  Map<String, dynamic> toJson() {
    return {
      'order_id': orderId,
      'final_completed_price': finalCompletedPrice,
    };
  }
}

/// Generic API response wrapper
class WholesalerOrderApiResponse {
  final bool success;
  final String? error;

  const WholesalerOrderApiResponse({
    required this.success,
    this.error,
  });

  factory WholesalerOrderApiResponse.fromJson(Map<String, dynamic> json) {
    return WholesalerOrderApiResponse(
      success: json['success'] ?? false,
      error: json['error'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'error': error,
    };
  }
}
