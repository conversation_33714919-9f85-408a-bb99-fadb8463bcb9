import 'package:flutter/foundation.dart';
import '../../../api/orders_api.dart';
import '../models/wholesaler_models.dart';
import '../api/wholesaler_orders_api_service.dart';

/// Repository for managing wholesaler orders state
class WholesalerOrdersRepository extends ChangeNotifier {
  // ============================================================================
  // PRIVATE FIELDS
  // ============================================================================

  List<WholesalerOrder> _orders = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  String? _error;
  int _currentOffset = 0;
  final int _limit = 10;
  bool _hasMoreData = true;
  String _searchQuery = '';
  OrderStatus? _statusFilter;

  // ============================================================================
  // PUBLIC GETTERS
  // ============================================================================

  /// Get all orders
  List<WholesalerOrder> get orders => _orders;

  /// Get filtered orders based on search and status filter
  List<WholesalerOrder> get filteredOrders {
    var filtered = _orders;

    // Apply status filter
    if (_statusFilter != null) {
      filtered =
          filtered.where((order) => order.status == _statusFilter).toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((order) {
        return order.store.name
                .toLowerCase()
                .contains(_searchQuery.toLowerCase()) ||
            order.store.owner.fullName
                .toLowerCase()
                .contains(_searchQuery.toLowerCase()) ||
            order.id.toString().contains(_searchQuery);
      }).toList();
    }

    return filtered;
  }

  /// Check if currently loading
  bool get isLoading => _isLoading;

  /// Check if loading more data
  bool get isLoadingMore => _isLoadingMore;

  /// Get current error message
  String? get error => _error;

  /// Check if has more data to load
  bool get hasMoreData => _hasMoreData;

  /// Get current search query
  String get searchQuery => _searchQuery;

  /// Get current status filter
  OrderStatus? get statusFilter => _statusFilter;

  /// Get orders count by status
  int getOrdersCountByStatus(OrderStatus status) {
    return _orders.where((order) => order.status == status).length;
  }

  /// Get pending orders count
  int get pendingOrdersCount => getOrdersCountByStatus(OrderStatus.pending);

  /// Get processing orders count
  int get processingOrdersCount =>
      getOrdersCountByStatus(OrderStatus.processing);

  /// Get delivered orders count
  int get deliveredOrdersCount => getOrdersCountByStatus(OrderStatus.delivered);

  /// Get cancelled orders count
  int get cancelledOrdersCount => getOrdersCountByStatus(OrderStatus.cancelled);

  // ============================================================================
  // PUBLIC METHODS
  // ============================================================================

  /// Load orders (refresh)
  Future<void> loadOrders({bool forceRefresh = false}) async {
    if (_isLoading) return;

    if (forceRefresh || _orders.isEmpty) {
      _isLoading = true;
      _error = null;
      _currentOffset = 0;
      _hasMoreData = true;
      notifyListeners();

      try {
        final response = await WholesalerOrdersApiService.getOrders(
          offset: 0,
          limit: _limit,
        );

        _orders = response.orders;
        _currentOffset = response.orders.length;
        _hasMoreData = response.orders.length >= _limit;
        _error = null;
      } catch (e) {
        if (kDebugMode) rethrow;
        _error = e.toString();
      } finally {
        _isLoading = false;
        notifyListeners();
      }
    }
  }

  /// Load more orders (pagination)
  Future<void> loadMoreOrders() async {
    if (_isLoadingMore || !_hasMoreData || _isLoading) return;

    _isLoadingMore = true;
    notifyListeners();

    try {
      final response = await WholesalerOrdersApiService.getOrders(
        offset: _currentOffset,
        limit: _limit,
      );

      _orders.addAll(response.orders);
      _currentOffset += response.orders.length;
      _hasMoreData = response.orders.length >= _limit;
    } catch (e) {
      if (kDebugMode) rethrow;
      _error = e.toString();
    } finally {
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  /// Set search query
  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  /// Set status filter
  void setStatusFilter(OrderStatus? status) {
    _statusFilter = status;
    notifyListeners();
  }

  /// Clear filters
  void clearFilters() {
    _searchQuery = '';
    _statusFilter = null;
    notifyListeners();
  }

  /// Accept an order
  Future<bool> acceptOrder(int orderId) async {
    try {
      final response = await WholesalerOrdersApiService.acceptOrder(orderId);

      if (response.success) {
        // Update local order status
        final orderIndex = _orders.indexWhere((order) => order.id == orderId);
        if (orderIndex != -1) {
          // Reload orders to get updated data
          await loadOrders(forceRefresh: true);
        }
        return true;
      } else {
        _error = response.error ?? 'فشل في قبول الطلب';
        notifyListeners();
        return false;
      }
    } catch (e) {
      if (kDebugMode) rethrow;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Reject an order
  Future<bool> rejectOrder(int orderId) async {
    try {
      final response = await WholesalerOrdersApiService.rejectOrder(orderId);

      if (response.success) {
        // Update local order status
        final orderIndex = _orders.indexWhere((order) => order.id == orderId);
        if (orderIndex != -1) {
          // Reload orders to get updated data
          await loadOrders(forceRefresh: true);
        }
        return true;
      } else {
        _error = response.error ?? 'فشل في رفض الطلب';
        notifyListeners();
        return false;
      }
    } catch (e) {
      if (kDebugMode) rethrow;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Cancel an order
  Future<bool> cancelOrder(int orderId) async {
    try {
      final response = await WholesalerOrdersApiService.cancelOrder(orderId);

      if (response.success) {
        // Update local order status
        final orderIndex = _orders.indexWhere((order) => order.id == orderId);
        if (orderIndex != -1) {
          // Reload orders to get updated data
          await loadOrders(forceRefresh: true);
        }
        return true;
      } else {
        _error = response.error ?? 'فشل في إلغاء الطلب';
        notifyListeners();
        return false;
      }
    } catch (e) {
      if (kDebugMode) rethrow;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Complete an order
  Future<bool> completeOrder(int orderId, {double? finalPrice}) async {
    try {
      final request = CompleteOrderRequest(
        orderId: orderId,
        finalCompletedPrice: finalPrice,
      );

      final response = await WholesalerOrdersApiService.completeOrder(request);

      if (response.success) {
        // Update local order status
        final orderIndex = _orders.indexWhere((order) => order.id == orderId);
        if (orderIndex != -1) {
          // Reload orders to get updated data
          await loadOrders(forceRefresh: true);
        }
        return true;
      } else {
        _error = response.error ?? 'فشل في إكمال الطلب';
        notifyListeners();
        return false;
      }
    } catch (e) {
      if (kDebugMode) rethrow;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  /// Get order by ID from local cache
  WholesalerOrder? getOrderById(int orderId) {
    try {
      return _orders.firstWhere((order) => order.id == orderId);
    } catch (e) {
      return null;
    }
  }

  /// Fetch detailed order information by ID including order items
  Future<WholesalerOrder?> fetchOrderDetails(int orderId) async {
    try {
      final detailedOrder =
          await WholesalerOrdersApiService.getOrderById(orderId);

      // Update the order in local cache if it exists
      final orderIndex = _orders.indexWhere((order) => order.id == orderId);
      if (orderIndex != -1) {
        _orders[orderIndex] = detailedOrder;
        notifyListeners();
      }

      return detailedOrder;
    } catch (e) {
      if (kDebugMode) rethrow;
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  /// Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }
}
