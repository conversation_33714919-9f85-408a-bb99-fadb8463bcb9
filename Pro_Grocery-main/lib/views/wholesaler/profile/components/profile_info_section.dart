import 'package:flutter/material.dart';
import '../../../../core/constants/constants.dart';
import '../models/profile_models.dart';

class ProfileInfoSection extends StatelessWidget {
  final WholesalerProfileData profileData;

  const ProfileInfoSection({
    super.key,
    required this.profileData,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Personal Information
        _buildInfoCard(
          context,
          title: 'المعلومات الشخصية',
          icon: Icons.person,
          children: [
            _buildInfoRow(
              context,
              'الاسم الكامل',
              profileData.userData.fullName,
              Icons.person_outline,
            ),
            _buildInfoRow(
              context,
              'البريد الإلكتروني',
              profileData.userData.email ?? 'غير محدد',
              Icons.email_outlined,
            ),
            _buildInfoRow(
              context,
              'رقم الهاتف',
              profileData.userData.phone,
              Icons.phone_outlined,
              trailing: profileData.userData.phoneVerified
                  ? const Icon(Icons.verified, color: Colors.green, size: 20)
                  : const Icon(Icons.warning, color: Colors.orange, size: 20),
            ),
            _buildInfoRow(
              context,
              'تاريخ الانضمام',
              _formatDate(profileData.userData.dateJoined),
              Icons.calendar_today_outlined,
            ),
          ],
        ),
        const SizedBox(height: AppDefaults.padding),

        // Store Information
        _buildInfoCard(
          context,
          title: 'معلومات المتجر',
          icon: Icons.store,
          children: [
            _buildInfoRow(
              context,
              'اسم المتجر',
              profileData.wholesalerData.title,
              Icons.store_outlined,
            ),
            _buildInfoRow(
              context,
              'اسم المستخدم',
              '@${profileData.wholesalerData.username}',
              Icons.alternate_email_outlined,
            ),
            _buildInfoRow(
              context,
              'فئة المتجر',
              StoreCategories.getDisplayName(
                  profileData.wholesalerData.category ?? ''),
              Icons.category_outlined,
            ),
            // Note: WholesalerProfile doesn't have createdAt field
            // _buildInfoRow(
            //   context,
            //   'تاريخ إنشاء المتجر',
            //   _formatDate(profileData.wholesalerData.createdAt),
            //   Icons.store_mall_directory_outlined,
            // ),
          ],
        ),
      ],
    );
  }

  Widget _buildInfoCard(
    BuildContext context, {
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: AppDefaults.borderRadius,
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppDefaults.padding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: AppColors.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    String label,
    String value,
    IconData icon, {
    Widget? trailing,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: AppColors.placeholder,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.placeholder,
                        fontWeight: FontWeight.w500,
                      ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ],
            ),
          ),
          if (trailing != null) trailing,
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
