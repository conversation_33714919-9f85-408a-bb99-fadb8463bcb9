import 'package:flutter/material.dart';
import '../../../../core/constants/constants.dart';
import '../models/profile_models.dart';

class ProfileEditDialog extends StatefulWidget {
  final WholesalerProfileData profileData;
  final Function(ProfileUpdateRequest) onSave;

  const ProfileEditDialog({
    super.key,
    required this.profileData,
    required this.onSave,
  });

  @override
  State<ProfileEditDialog> createState() => _ProfileEditDialogState();
}

class _ProfileEditDialogState extends State<ProfileEditDialog> {
  final _formKey = GlobalKey<FormState>();
  late ProfileFormData _formData;
  Map<String, String> _errors = {};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _formData = ProfileFormData.fromProfileData(widget.profileData);
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: AppDefaults.borderRadius,
      ),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(AppDefaults.padding),
              decoration: const BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AppDefaults.radius),
                  topRight: Radius.circular(AppDefaults.radius),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.edit,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'تعديل الملف الشخصي',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: _isLoading ? null : () => Navigator.pop(context),
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),

            // Form Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppDefaults.padding),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Personal Information Section
                      _buildSectionHeader('المعلومات الشخصية'),
                      const SizedBox(height: 12),

                      _buildTextField(
                        label: 'الاسم الأول',
                        value: _formData.firstName,
                        onChanged: (value) => _formData.firstName = value,
                        errorKey: 'firstName',
                      ),
                      const SizedBox(height: 12),

                      _buildTextField(
                        label: 'الاسم الأخير',
                        value: _formData.lastName,
                        onChanged: (value) => _formData.lastName = value,
                        errorKey: 'lastName',
                      ),
                      const SizedBox(height: 12),

                      _buildTextField(
                        label: 'البريد الإلكتروني',
                        value: _formData.email,
                        onChanged: (value) => _formData.email = value,
                        errorKey: 'email',
                        keyboardType: TextInputType.emailAddress,
                      ),
                      const SizedBox(height: 12),

                      _buildTextField(
                        label: 'رقم الهاتف',
                        value: _formData.phone,
                        onChanged: (value) => _formData.phone = value,
                        errorKey: 'phone',
                        keyboardType: TextInputType.phone,
                      ),
                      const SizedBox(height: 24),

                      // Store Information Section
                      _buildSectionHeader('معلومات المتجر'),
                      const SizedBox(height: 12),

                      _buildTextField(
                        label: 'اسم المتجر',
                        value: _formData.title,
                        onChanged: (value) => _formData.title = value,
                        errorKey: 'title',
                      ),
                      const SizedBox(height: 12),

                      _buildTextField(
                        label: 'اسم المستخدم',
                        value: _formData.username,
                        onChanged: (value) => _formData.username = value,
                        errorKey: 'username',
                      ),
                      // const SizedBox(height: 12),

                      // _buildCategoryDropdown(),
                    ],
                  ),
                ),
              ),
            ),

            // Actions
            Container(
              padding: const EdgeInsets.all(AppDefaults.padding),
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(
                    color: Colors.grey.withValues(alpha: 0.2),
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed:
                          _isLoading ? null : () => Navigator.pop(context),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _handleSave,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Text('حفظ'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: AppColors.primary,
      ),
    );
  }

  Widget _buildTextField({
    required String label,
    required String value,
    required Function(String) onChanged,
    required String errorKey,
    TextInputType? keyboardType,
  }) {
    return TextFormField(
      initialValue: value,
      onChanged: onChanged,
      keyboardType: keyboardType,
      decoration: InputDecoration(
        labelText: label,
        errorText: _errors[errorKey],
        border: const OutlineInputBorder(),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.primary),
        ),
      ),
    );
  }

  Widget _buildCategoryDropdown() {
    return DropdownButtonFormField<String>(
      value: _formData.category,
      decoration: InputDecoration(
        labelText: 'فئة المتجر',
        errorText: _errors['category'],
        border: const OutlineInputBorder(),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.primary),
        ),
      ),
      items: StoreCategories.categories.map((category) {
        return DropdownMenuItem(
          value: category,
          child: Text(category),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _formData.category = value;
          });
        }
      },
    );
  }

  Future<void> _handleSave() async {
    setState(() {
      _isLoading = true;
      _errors = _formData.validate();
    });

    if (_errors.isNotEmpty) {
      setState(() {
        _isLoading = false;
      });
      return;
    }

    final updateRequest = _formData.toUpdateRequest(widget.profileData);

    if (updateRequest.isEmpty) {
      setState(() {
        _isLoading = false;
      });
      Navigator.pop(context);
      return;
    }

    try {
      await widget.onSave(updateRequest);
      if (mounted) {
        Navigator.pop(context);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
