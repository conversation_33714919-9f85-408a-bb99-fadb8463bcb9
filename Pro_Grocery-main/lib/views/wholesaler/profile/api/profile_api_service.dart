import 'package:flutter/foundation.dart';
import '../../../../api/auth/user.dart';
import '../../api/wholesaler_api_service.dart';
import '../../models/wholesaler_models.dart';
import '../models/profile_models.dart';

/// Exception for profile API operations
class ProfileApiException implements Exception {
  final String message;
  final int? statusCode;

  ProfileApiException(this.message, [this.statusCode]);

  @override
  String toString() => 'ProfileApiException: $message';
}

/// API service for wholesaler profile operations
class ProfileApiService {
  /// Load complete profile data (user + wholesaler)
  static Future<WholesalerProfileData> loadProfileData() async {
    try {
      // Load user data and wholesaler data in parallel
      final results = await Future.wait([
        UserApi.getCurrentUser(),
        WholesalerApiService.getMyWholesaler(),
      ]);

      final userResponse = results[0] as UserResponse;
      final wholesalerData = results[1] as WholesalerProfile;

      if (!userResponse.success || userResponse.user == null) {
        throw ProfileApiException(
          userResponse.error ?? 'فشل في تحميل بيانات المستخدم',
        );
      }

      return WholesalerProfileData(
        userData: userResponse.user!,
        wholesalerData: wholesalerData,
      );
    } catch (e) {
      if (e is ProfileApiException) rethrow;
      if (kDebugMode) rethrow;
      throw ProfileApiException('خطأ في تحميل البيانات: ${e.toString()}');
    }
  }

  /// Update profile data
  static Future<WholesalerProfileData> updateProfile(
    ProfileUpdateRequest request,
  ) async {
    try {
      List<Future> updateFutures = [];

      // Add user update if needed
      if (request.hasUserUpdate) {
        updateFutures.add(UserApi.updateCurrentUser(request.userUpdate!));
      }

      // Add wholesaler update if needed
      if (request.hasWholesalerUpdate) {
        updateFutures.add(
          WholesalerApiService.updateMyWholesaler(request.wholesalerUpdate!),
        );
      }

      if (updateFutures.isEmpty) {
        throw ProfileApiException('لا توجد تغييرات للحفظ');
      }

      // Execute updates in parallel
      final results = await Future.wait(updateFutures);

      UserResponse? userResponse;
      dynamic wholesalerData;

      int resultIndex = 0;

      if (request.hasUserUpdate) {
        userResponse = results[resultIndex] as UserResponse;
        resultIndex++;

        if (!userResponse.success || userResponse.user == null) {
          throw ProfileApiException(
            userResponse.error ?? 'فشل في تحديث بيانات المستخدم',
          );
        }
      }

      if (request.hasWholesalerUpdate) {
        wholesalerData = results[resultIndex];
      }

      // If only one type was updated, fetch the other
      if (!request.hasUserUpdate) {
        final response = await UserApi.getCurrentUser();
        if (!response.success || response.user == null) {
          throw ProfileApiException('فشل في تحميل بيانات المستخدم المحدثة');
        }
        userResponse = response;
      }

      if (!request.hasWholesalerUpdate) {
        wholesalerData = await WholesalerApiService.getMyWholesaler();
      }

      return WholesalerProfileData(
        userData: userResponse!.user!,
        wholesalerData: wholesalerData,
      );
    } catch (e) {
      if (e is ProfileApiException) rethrow;
      if (kDebugMode) rethrow;
      throw ProfileApiException('خطأ في تحديث البيانات: ${e.toString()}');
    }
  }

  /// Upload profile image
  static Future<WholesalerProfileData> uploadProfileImage(
      String filePath) async {
    try {
      // Upload logo to wholesaler
      final wholesalerData = await WholesalerApiService.uploadLogo(filePath);

      // Get current user data
      final userResponse = await UserApi.getCurrentUser();
      if (!userResponse.success || userResponse.user == null) {
        throw ProfileApiException('فشل في تحميل بيانات المستخدم');
      }

      return WholesalerProfileData(
        userData: userResponse.user!,
        wholesalerData: wholesalerData,
      );
    } catch (e) {
      if (e is ProfileApiException) rethrow;
      if (kDebugMode) rethrow;
      throw ProfileApiException('خطأ في رفع الصورة: ${e.toString()}');
    }
  }

  /// Upload background image
  static Future<WholesalerProfileData> uploadBackgroundImage(
      String filePath) async {
    try {
      // Upload background to wholesaler
      final wholesalerData =
          await WholesalerApiService.uploadBackground(filePath);

      // Get current user data
      final userResponse = await UserApi.getCurrentUser();
      if (!userResponse.success || userResponse.user == null) {
        throw ProfileApiException('فشل في تحميل بيانات المستخدم');
      }

      return WholesalerProfileData(
        userData: userResponse.user!,
        wholesalerData: wholesalerData,
      );
    } catch (e) {
      if (e is ProfileApiException) rethrow;
      if (kDebugMode) rethrow;
      throw ProfileApiException('خطأ في رفع صورة الخلفية: ${e.toString()}');
    }
  }
}
