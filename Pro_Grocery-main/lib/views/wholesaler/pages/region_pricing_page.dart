import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/constants/constants.dart';
import '../../../services/app_services.dart';
import '../../../api/regions.dart';
import '../repository/wholesaler_repository.dart';
import '../models/wholesaler_models.dart';
import '../components/wholesaler_dashboard_header.dart';

class RegionPricingPage extends StatefulWidget {
  const RegionPricingPage({super.key});

  @override
  State<RegionPricingPage> createState() => _RegionPricingPageState();
}

class _RegionPricingPageState extends State<RegionPricingPage> {
  late final WholesalerRepository _repository;

  @override
  void initState() {
    super.initState();
    _repository = WholesalerRepository();
    _repository.addListener(_onRepositoryChanged);
    _loadMinCharges();
  }

  @override
  void dispose() {
    _repository.removeListener(_onRepositoryChanged);
    super.dispose();
  }

  void _onRepositoryChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _loadMinCharges() async {
    await _repository.loadMinCharges(forceRefresh: true);
  }

  Future<void> _showAddMinChargeDialog() async {
    await showDialog(
      context: context,
      builder: (context) => AddMinChargeDialog(repository: _repository),
    );
  }

  Future<void> _showEditMinChargeDialog(RegionMinCharge minCharge) async {
    await showDialog(
      context: context,
      builder: (context) => AddMinChargeDialog(
        repository: _repository,
        existingMinCharge: minCharge,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const WholesalerAppBar(
        canBack: true,
        title: 'أسعار المناطق',
      ),
      body: RefreshIndicator(
        onRefresh: _loadMinCharges,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppDefaults.padding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Section
              Text(
                'الحد الأدنى للرسوم حسب المنطقة',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 8),
              Text(
                'تحديد متطلبات الحد الأدنى للطلب للمناطق المختلفة',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
              const SizedBox(height: AppDefaults.padding),

              // Add Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _showAddMinChargeDialog,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة تسعير منطقة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(height: AppDefaults.padding),

              // Min Charges List
              if (_repository.isLoadingMinCharges &&
                  _repository.minCharges.isEmpty)
                const Center(
                  child: Padding(
                    padding: EdgeInsets.all(AppDefaults.padding),
                    child: CircularProgressIndicator(),
                  ),
                )
              else if (_repository.minChargesError != null)
                _buildErrorState()
              else if (_repository.minCharges.isEmpty)
                _buildEmptyState()
              else
                _buildMinChargesList(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: AppDefaults.borderRadius,
        border: Border.all(color: Colors.red[200]!),
      ),
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            'خطأ في تحميل أسعار المناطق',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.red[800],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            _repository.minChargesError!,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.red[600],
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadMinCharges,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDefaults.padding * 2),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: AppDefaults.borderRadius,
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(
            Icons.location_on,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لم يتم تحديد أسعار المناطق',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'حدد الحد الأدنى للرسوم ومتطلبات المنتجات للمناطق المختلفة لتحسين تكاليف التوصيل',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[500],
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showAddMinChargeDialog,
            icon: const Icon(Icons.add),
            label: const Text('إضافة تسعير منطقة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMinChargesList() {
    return Column(
      children: _repository.minCharges.map((minCharge) {
        return Card(
          margin: const EdgeInsets.only(bottom: AppDefaults.margin),
          child: ListTile(
            leading: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.location_on,
                color: AppColors.primary,
                size: 20,
              ),
            ),
            title: Text(
              minCharge.region.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('الرمز: ${minCharge.region.code}'),
                const SizedBox(height: 4),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الحد الأدنى للرسوم: ${minCharge.minCharge.toStringAsFixed(2)} ج.م',
                      style: TextStyle(
                        color: Colors.green[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      'الحد الأدنى للمنتجات: ${minCharge.minItems}',
                      style: TextStyle(
                        color: Colors.blue[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            trailing: PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'edit') {
                  _showEditMinChargeDialog(minCharge);
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit, size: 16),
                      SizedBox(width: 8),
                      Text('تعديل'),
                    ],
                  ),
                ),
              ],
            ),
            isThreeLine: true,
          ),
        );
      }).toList(),
    );
  }
}

class AddMinChargeDialog extends StatefulWidget {
  final WholesalerRepository repository;
  final RegionMinCharge? existingMinCharge;

  const AddMinChargeDialog({
    super.key,
    required this.repository,
    this.existingMinCharge,
  });

  @override
  State<AddMinChargeDialog> createState() => _AddMinChargeDialogState();
}

class _AddMinChargeDialogState extends State<AddMinChargeDialog> {
  final _minChargeController = TextEditingController();
  final _minItemsController = TextEditingController();
  RegionInfo? _selectedRegion;
  bool _isLoading = false;
  String? _error;

  bool get isEditing => widget.existingMinCharge != null;

  @override
  void initState() {
    super.initState();
    if (isEditing) {
      final minCharge = widget.existingMinCharge!;
      _selectedRegion = minCharge.region;
      _minChargeController.text = minCharge.minCharge.toString();
      _minItemsController.text = minCharge.minItems.toString();
    }
  }

  @override
  void dispose() {
    _minChargeController.dispose();
    _minItemsController.dispose();
    super.dispose();
  }

  Future<void> _selectRegion() async {
    final selectedRegion = await showDialog<RegionInfo>(
      context: context,
      builder: (context) => const MinChargeRegionSelectionDialog(),
    );

    if (selectedRegion != null) {
      setState(() {
        _selectedRegion = selectedRegion;
        _error = null; // Clear any previous error
      });
    }
  }

  Future<void> _submitMinCharge() async {
    final minCharge = double.tryParse(_minChargeController.text);
    final minItems = int.tryParse(_minItemsController.text);

    if (_selectedRegion == null) {
      setState(() {
        _error = 'يرجى اختيار منطقة';
      });
      return;
    }

    if (minCharge == null || minCharge <= 0) {
      setState(() {
        _error = 'يرجى إدخال حد أدنى صحيح للرسوم';
      });
      return;
    }

    if (minItems == null || minItems <= 0) {
      setState(() {
        _error = 'يرجى إدخال عدد صحيح للحد الأدنى من المنتجات';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final request = RegionMinChargeRequest(
        regionId: _selectedRegion!.id,
        minCharge: minCharge,
        minItems: minItems,
      );

      final success = await widget.repository.createMinCharge(request);

      if (success && mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isEditing
                ? 'تم تحديث تسعير المنطقة بنجاح'
                : 'تم إضافة تسعير المنطقة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        setState(() {
          _error =
              widget.repository.minChargesError ?? 'فشل في حفظ تسعير المنطقة';
        });
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(isEditing ? 'تعديل تسعير المنطقة' : 'إضافة تسعير منطقة'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Region Selection
            Text(
              'المنطقة',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            InkWell(
              onTap: isEditing ? null : _selectRegion,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: isEditing ? Colors.grey[300]! : AppColors.primary,
                  ),
                  borderRadius: BorderRadius.circular(8),
                  color: isEditing ? Colors.grey[100] : Colors.white,
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      color: isEditing ? Colors.grey[500] : AppColors.primary,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _selectedRegion?.name ?? 'اختر منطقة',
                        style: TextStyle(
                          color: _selectedRegion != null
                              ? (isEditing ? Colors.grey[700] : Colors.black)
                              : Colors.grey[500],
                        ),
                      ),
                    ),
                    if (!isEditing)
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: Colors.grey[400],
                      ),
                  ],
                ),
              ),
            ),
            if (isEditing) ...[
              const SizedBox(height: 4),
              Text(
                'لا يمكن تغيير المنطقة عند التعديل',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                      fontStyle: FontStyle.italic,
                    ),
              ),
            ],
            const SizedBox(height: 16),

            // Minimum Charge
            TextField(
              controller: _minChargeController,
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
              ],
              decoration: const InputDecoration(
                labelText: 'الحد الأدنى للرسوم (ج.م)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.attach_money),
              ),
            ),
            const SizedBox(height: 16),

            // Minimum Items
            TextField(
              controller: _minItemsController,
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              decoration: const InputDecoration(
                labelText: 'الحد الأدنى للمنتجات',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.shopping_cart),
              ),
            ),

            // Error display
            if (_error != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red[600], size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _error!,
                        style: TextStyle(color: Colors.red[800], fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _submitMinCharge,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Text(isEditing ? 'تحديث' : 'إضافة'),
        ),
      ],
    );
  }
}

/// Dialog for selecting a region for minimum charge configuration
class MinChargeRegionSelectionDialog extends StatefulWidget {
  const MinChargeRegionSelectionDialog({super.key});

  @override
  State<MinChargeRegionSelectionDialog> createState() =>
      _MinChargeRegionSelectionDialogState();
}

class _MinChargeRegionSelectionDialogState
    extends State<MinChargeRegionSelectionDialog> {
  final TextEditingController _searchController = TextEditingController();
  late final _regionService = AppServices().regionService;
  List<RegionModel> _filteredRegions = [];
  String _selectedType = 'ALL'; // ALL, COUNTRY, STATE, DISTRICT
  RegionModel? _selectedRegion;

  @override
  void initState() {
    super.initState();
    _regionService.addListener(_onRegionServiceChanged);
    // Defer loading regions until after the build phase
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadRegions();
    });
  }

  void _onRegionServiceChanged() {
    if (mounted) {
      _filterRegions(_regionService.allRegions);
    }
  }

  void _loadRegions() {
    _regionService.fetchRegions();
  }

  void _filterRegions(List<RegionModel> allRegions) {
    List<RegionModel> regions;

    switch (_selectedType) {
      case 'COUNTRY':
        regions = allRegions.where((r) => r.isCountry).toList();
        break;
      case 'STATE':
        regions = allRegions.where((r) => r.isState).toList();
        break;
      case 'DISTRICT':
        regions = allRegions.where((r) => r.isDistrict).toList();
        break;
      default:
        regions = allRegions;
    }

    final query = _searchController.text.toLowerCase();
    if (query.isNotEmpty) {
      regions = regions.where((region) {
        return region.name.toLowerCase().contains(query) ||
            region.hierarchicalName.toLowerCase().contains(query);
      }).toList();
    }

    setState(() {
      _filteredRegions = regions;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(AppDefaults.padding),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                Text(
                  'اختر المنطقة',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Search field
            TextField(
              controller: _searchController,
              onChanged: (_) => _filterRegions(_regionService.allRegions),
              decoration: InputDecoration(
                hintText: 'ابحث عن منطقة...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Filter chips
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildFilterChip('الكل', 'ALL'),
                  _buildFilterChip('الدول', 'COUNTRY'),
                  _buildFilterChip('المحافظات', 'STATE'),
                  _buildFilterChip('المناطق', 'DISTRICT'),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Content
            Expanded(
              child: _buildContent(),
            ),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('إلغاء'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _selectedRegion != null
                        ? () {
                            // Convert RegionModel to RegionInfo
                            final regionInfo = RegionInfo(
                              id: _selectedRegion!.id,
                              name: _selectedRegion!.name,
                              type: _selectedRegion!.type,
                              code: _selectedRegion!.code,
                            );
                            Navigator.of(context).pop(regionInfo);
                          }
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('تأكيد'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, String type) {
    final isSelected = _selectedType == type;
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (_) {
          setState(() {
            _selectedType = type;
          });
          _filterRegions(_regionService.allRegions);
        },
        selectedColor: AppColors.primary.withValues(alpha: 0.2),
        checkmarkColor: AppColors.primary,
      ),
    );
  }

  Widget _buildContent() {
    if (_regionService.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_regionService.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 48, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              'فشل في تحميل المناطق',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              _regionService.error!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _regionService.fetchRegions(forceRefresh: true),
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_filteredRegions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.location_off, size: 48, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              'لا توجد مناطق',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'لم يتم العثور على مناطق مطابقة لبحثك',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _filteredRegions.length,
      itemBuilder: (context, index) {
        final region = _filteredRegions[index];
        final isSelected = _selectedRegion?.id == region.id;

        return ListTile(
          title: Text(region.name),
          subtitle: region.hierarchicalName != region.name
              ? Text(region.hierarchicalName)
              : null,
          leading: CircleAvatar(
            backgroundColor: _getRegionTypeColor(region.type),
            child: Text(
              _getRegionTypeInitial(region.type),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          trailing: isSelected
              ? const Icon(Icons.check_circle, color: AppColors.primary)
              : null,
          selected: isSelected,
          selectedTileColor: AppColors.primary.withValues(alpha: 0.1),
          onTap: () {
            setState(() {
              _selectedRegion = region;
            });
          },
        );
      },
    );
  }

  Color _getRegionTypeColor(String type) {
    switch (type) {
      case 'COUNTRY':
        return Colors.blue;
      case 'STATE':
        return Colors.green;
      case 'DISTRICT':
        return Colors.orange;
      case 'CITY':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  String _getRegionTypeInitial(String type) {
    switch (type) {
      case 'COUNTRY':
        return 'د'; // دولة
      case 'STATE':
        return 'م'; // محافظة
      case 'DISTRICT':
        return 'ن'; // منطقة
      case 'CITY':
        return 'ن'; // محافظة
      default:
        return '؟';
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _regionService.removeListener(_onRegionServiceChanged);
    super.dispose();
  }
}
