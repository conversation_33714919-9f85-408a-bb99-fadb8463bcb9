import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/constants/constants.dart';
import '../repository/wholesaler_repository.dart';
import '../models/wholesaler_models.dart';
import '../components/wholesaler_dashboard_header.dart';
import '../components/wholesaler_item_list.dart';

class InventoryManagementPage extends StatefulWidget {
  const InventoryManagementPage({super.key});

  @override
  State<InventoryManagementPage> createState() =>
      _InventoryManagementPageState();
}

class _InventoryManagementPageState extends State<InventoryManagementPage> {
  late final WholesalerRepository _repository;
  String _filterType = 'all'; // all, low_stock, out_of_stock

  @override
  void initState() {
    super.initState();
    _repository = WholesalerRepository();
    _repository.addListener(_onRepositoryChanged);
    _loadItems();
  }

  @override
  void dispose() {
    _repository.removeListener(_onRepositoryChanged);
    super.dispose();
  }

  void _onRepositoryChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _loadItems() async {
    await _repository.loadItems(forceRefresh: true);
  }

  List<WholesalerItem> get _filteredItems {
    List<WholesalerItem> items = _repository.items;

    switch (_filterType) {
      case 'low_stock':
        items = items
            .where(
                (item) => item.inventoryCount > 0 && item.inventoryCount < 10)
            .toList();
        break;
      case 'out_of_stock':
        items = items.where((item) => item.inventoryCount == 0).toList();
        break;
      case 'all':
      default:
        // No additional filtering
        break;
    }

    return items;
  }

  Future<void> _showInventoryUpdateDialog(WholesalerItem item) async {
    await showDialog(
      context: context,
      builder: (context) => InventoryUpdateDialog(
        item: item,
        repository: _repository,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final filteredItems = _filteredItems;
    final lowStockCount = _repository.getLowStockItems().length;
    final outOfStockCount =
        _repository.items.where((item) => item.inventoryCount == 0).length;

    return Scaffold(
      appBar: const WholesalerAppBar(
        title: 'إدارة المخزون',
        canBack: true,
      ),
      body: Column(
        children: [
          // Summary Cards
          Container(
            padding: const EdgeInsets.all(AppDefaults.padding),
            child: Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'إجمالي المنتجات',
                    _repository.items.length.toString(),
                    Icons.inventory,
                    AppColors.primary,
                  ),
                ),
                const SizedBox(width: AppDefaults.margin),
                Expanded(
                  child: _buildSummaryCard(
                    'مخزون منخفض',
                    lowStockCount.toString(),
                    Icons.warning,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: AppDefaults.margin),
                Expanded(
                  child: _buildSummaryCard(
                    'نفد المخزون',
                    outOfStockCount.toString(),
                    Icons.error,
                    Colors.red,
                  ),
                ),
              ],
            ),
          ),

          // Filter Section
          Container(
            padding:
                const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
            child: Row(
              children: [
                Text(
                  'تصفية:',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        FilterChip(
                          label: Text('الكل (${_repository.items.length})'),
                          selected: _filterType == 'all',
                          onSelected: (selected) {
                            setState(() {
                              _filterType = 'all';
                            });
                          },
                        ),
                        const SizedBox(width: 8),
                        FilterChip(
                          label: Text('مخزون منخفض ($lowStockCount)'),
                          selected: _filterType == 'low_stock',
                          onSelected: (selected) {
                            setState(() {
                              _filterType = 'low_stock';
                            });
                          },
                        ),
                        const SizedBox(width: 8),
                        FilterChip(
                          label: Text('نفد المخزون ($outOfStockCount)'),
                          selected: _filterType == 'out_of_stock',
                          onSelected: (selected) {
                            setState(() {
                              _filterType = 'out_of_stock';
                            });
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: AppDefaults.margin),

          // Items List
          Expanded(
            child: WholesalerItemList(
              items: filteredItems,
              isLoading: _repository.isLoadingItems,
              error: _repository.itemsError,
              onRefresh: _loadItems,
              onInventoryUpdate: _showInventoryUpdateDialog,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: AppDefaults.borderRadius,
        boxShadow: AppDefaults.boxShadow,
      ),
      child: Column(
        children: [
          Icon(icon, size: 24, color: color),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class InventoryUpdateDialog extends StatefulWidget {
  final WholesalerItem item;
  final WholesalerRepository repository;

  const InventoryUpdateDialog({
    super.key,
    required this.item,
    required this.repository,
  });

  @override
  State<InventoryUpdateDialog> createState() => _InventoryUpdateDialogState();
}

class _InventoryUpdateDialogState extends State<InventoryUpdateDialog> {
  final _quantityController = TextEditingController();
  final _notesController = TextEditingController();
  InventoryTransactionType _transactionType = InventoryTransactionType.addition;
  bool _isLoading = false;
  String? _error;

  @override
  void dispose() {
    _quantityController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _submitUpdate() async {
    final quantity = int.tryParse(_quantityController.text);
    if (quantity == null || quantity <= 0) {
      setState(() {
        _error = 'يرجى إدخال كمية صحيحة';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final request = InventoryTransactionRequest(
        transactionType: _transactionType,
        quantity: quantity,
        notes: _notesController.text.isNotEmpty ? _notesController.text : null,
      );

      final success =
          await widget.repository.updateInventory(widget.item.id, request);

      if (success && mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تحديث المخزون لـ ${widget.item.product.name}'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        setState(() {
          _error = widget.repository.itemsError ?? 'فشل في تحديث المخزون';
        });
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('تحديث المخزون - ${widget.item.product.name}'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current inventory display
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.warehouse, color: AppColors.primary),
                  const SizedBox(width: 8),
                  Text(
                    'المخزون الحالي: ${widget.item.inventoryCount}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Transaction type selection
            Text(
              'نوع المعاملة',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<InventoryTransactionType>(
                    title: const Text('إضافة'),
                    value: InventoryTransactionType.addition,
                    groupValue: _transactionType,
                    onChanged: (value) {
                      setState(() {
                        _transactionType = value!;
                      });
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                Expanded(
                  child: RadioListTile<InventoryTransactionType>(
                    title: const Text('إزالة'),
                    value: InventoryTransactionType.subtraction,
                    groupValue: _transactionType,
                    onChanged: (value) {
                      setState(() {
                        _transactionType = value!;
                      });
                    },
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Quantity input
            TextField(
              controller: _quantityController,
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              decoration: const InputDecoration(
                labelText: 'الكمية',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.numbers),
              ),
            ),
            const SizedBox(height: 16),

            // Notes input
            TextField(
              controller: _notesController,
              maxLines: 3,
              decoration: const InputDecoration(
                labelText: 'ملاحظات (اختياري)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.note),
              ),
            ),

            // Error display
            if (_error != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red[600], size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _error!,
                        style: TextStyle(color: Colors.red[800], fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _submitUpdate,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('تحديث'),
        ),
      ],
    );
  }
}
