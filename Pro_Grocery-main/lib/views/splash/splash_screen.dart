import 'package:flutter/material.dart';
import 'package:grocery/api/api.dart';
import '../../core/constants/constants.dart';
import '../../services/auth.dart';
import '../../services/user_service.dart';
import '../../utils/navigation.dart' as nav;
import '../../views/onboarding/onboarding_page.dart';
import '../../views/entrypoint/entrypoint_ui.dart';
import '../../views/entrypoint/wholesaler_entrypoint_ui.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _textController;
  late AnimationController _loadingController;
  late Animation<double> _logoScaleAnimation;
  late Animation<double> _logoOpacityAnimation;
  late Animation<double> _textOpacityAnimation;
  late Animation<double> _loadingOpacityAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _checkAuthenticationAndRedirect();
  }

  void _initializeAnimations() {
    // Logo animation controller
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // Text animation controller
    _textController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Loading animation controller
    _loadingController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // Logo animations
    _logoScaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    _logoOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeInOut),
    ));

    // Text animations
    _textOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeInOut,
    ));

    // Loading animations
    _loadingOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadingController,
      curve: Curves.easeInOut,
    ));

    // Start animations
    _logoController.forward();
    Future.delayed(const Duration(milliseconds: 400), () {
      if (mounted) _textController.forward();
    });
    Future.delayed(const Duration(milliseconds: 800), () {
      if (mounted) _loadingController.forward();
    });
  }

  Future<void> _checkAuthenticationAndRedirect() async {
    // Add a delay to show the splash screen with animations
    await Future.delayed(const Duration(milliseconds: 2500));

    if (!mounted) return;

    try {
      // Check if user has a stored token
      final token = await AuthService.getToken();

      if (token == null || token.isEmpty) {
        // No token stored, redirect to onboarding
        if (mounted) {
          nav.Router.pushReplacement(context, const OnboardingPage());
        }
        return;
      }

      // Set token for API calls
      HttpService.instance.setToken(token);

      if (!mounted) return;

      // Validate token by attempting to fetch current user data
      final userResponse = await UserService.getCurrentUser(forceRefresh: true);

      if (!mounted) return;

      if (userResponse != null) {
        // Token is valid, user data retrieved successfully
        // Check user type for appropriate redirect
        final isWholesaler = await AuthService.isWholesaler();

        if (!mounted) return;

        if (isWholesaler) {
          // Redirect to wholesaler dashboard
          nav.Router.pushReplacement(context, const WholesalerEntryPointUI());
        } else {
          // Redirect to main app entry point
          nav.Router.pushReplacement(context, const EntryPointUI());
        }
      } else {
        // Token is invalid/expired, clear stored data and redirect to login
        await AuthService.logout();
        if (mounted) {
          nav.Router.pushReplacement(context, const OnboardingPage());
        }
      }
    } catch (e) {
      // Handle any errors by clearing auth data and redirecting to onboarding
      await AuthService.logout();
      if (mounted) {
        nav.Router.pushReplacement(context, const OnboardingPage());
      }
    }
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    _loadingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Animated Logo
              AnimatedBuilder(
                animation: _logoController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _logoScaleAnimation.value,
                    child: Opacity(
                      opacity: _logoOpacityAnimation.value,
                      child: Container(
                        width: 140,
                        height: 140,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(28),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.primary.withValues(alpha: 0.15),
                              spreadRadius: 8,
                              blurRadius: 25,
                              offset: const Offset(0, 8),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(28),
                          child: Image.asset(
                            'assets/logo_cropped.png',
                            width: 90,
                            height: 90,
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: 40),

              // Animated App Name
              AnimatedBuilder(
                animation: _textController,
                builder: (context, child) {
                  return Opacity(
                    opacity: _textOpacityAnimation.value,
                    child: Column(
                      children: [
                        Text(
                          'تاجر بلس',
                          style: Theme.of(context)
                              .textTheme
                              .headlineMedium
                              ?.copyWith(
                                color: AppColors.primary,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 1.5,
                                fontSize: 32,
                              ),
                        ),
                      ],
                    ),
                  );
                },
              ),

              const SizedBox(height: 60),

              // Animated Loading Widget
              AnimatedBuilder(
                animation: _loadingController,
                builder: (context, child) {
                  return Opacity(
                    opacity: _loadingOpacityAnimation.value,
                    child: Column(
                      children: [
                        // Custom animated loading indicator
                        SizedBox(
                          width: 40,
                          height: 40,
                          child: Stack(
                            children: [
                              // Outer rotating circle
                              SizedBox(
                                width: 40,
                                height: 40,
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    AppColors.primary.withValues(alpha: 0.3),
                                  ),
                                  strokeWidth: 3,
                                ),
                              ),
                              // Inner pulsing circle
                              Center(
                                child: TweenAnimationBuilder<double>(
                                  tween: Tween(begin: 0.0, end: 1.0),
                                  duration: const Duration(milliseconds: 1500),
                                  builder: (context, value, child) {
                                    return Transform.scale(
                                      scale: 0.5 + (0.3 * value),
                                      child: Container(
                                        width: 20,
                                        height: 20,
                                        decoration: BoxDecoration(
                                          color: AppColors.primary,
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                    );
                                  },
                                  onEnd: () {
                                    // Restart the animation
                                    if (mounted) {
                                      setState(() {});
                                    }
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'جارِ التحميل...',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: AppColors.placeholder,
                                    fontWeight: FontWeight.w500,
                                  ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
