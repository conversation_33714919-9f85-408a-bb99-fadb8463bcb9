import 'package:animations/animations.dart';
import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_defaults.dart';
import '../wholesaler/wholesaler_home_page.dart';
import '../wholesaler/pages/item_list_page.dart';
import '../wholesaler/pages/orders_listing_page.dart';
import '../wholesaler/profile/wholesaler_profile_page.dart';
import 'components/wholesaler_navigation_bar.dart';

/// This page will contain all the bottom navigation tabs for wholesaler
class WholesalerEntryPointUI extends StatefulWidget {
  const WholesalerEntryPointUI({super.key});

  @override
  State<WholesalerEntryPointUI> createState() => _WholesalerEntryPointUIState();
}

class _WholesalerEntryPointUIState extends State<WholesalerEntryPointUI> {
  /// Current Page
  int currentIndex = 0;

  /// On bottom navigation tap
  void onBottomNavigationTap(int index) {
    currentIndex = index;
    setState(() {});
  }

  /// All the pages
  List<Widget> get pages => [
        const WholesalerHomePage(), // Home
        const WholesalerOrdersListingPage(), // Orders
        const ItemListPage(canPop: false), // Items
        const WholesalerProfilePage(), // Profile
      ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageTransitionSwitcher(
        transitionBuilder: (child, primaryAnimation, secondaryAnimation) {
          return SharedAxisTransition(
            animation: primaryAnimation,
            secondaryAnimation: secondaryAnimation,
            transitionType: SharedAxisTransitionType.horizontal,
            fillColor: AppColors.scaffoldBackground,
            child: child,
          );
        },
        duration: AppDefaults.duration,
        child: pages[currentIndex],
      ),
      bottomNavigationBar: WholesalerBottomNavigationBar(
        currentIndex: currentIndex,
        onNavTap: onBottomNavigationTap,
      ),
    );
  }
}
