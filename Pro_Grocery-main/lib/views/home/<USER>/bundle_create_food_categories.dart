import 'package:flutter/material.dart';

import '../../../core/constants/constants.dart';
import 'app_chip.dart';

class FoodCategories extends StatelessWidget {
  const FoodCategories({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(
        horizontal: AppDefaults.padding,
      ),
      child: Row(
        children: [
          AppChip(
            isActive: true,
            label: 'خضروات',
            onPressed: () {},
          ),
          AppChip(
            isActive: false,
            label: 'لحوم وأسماك',
            onPressed: () {},
          ),
          AppChip(
            isActive: false,
            label: 'أدوية',
            onPressed: () {},
          ),
          AppChip(
            isActive: false,
            label: 'رعاية الأطفال',
            onPressed: () {},
          ),
        ],
      ),
    );
  }
}
