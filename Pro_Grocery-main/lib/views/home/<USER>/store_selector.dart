import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_defaults.dart';
import '../../../services/store_service.dart';
import '../dialogs/store_selection_dialog.dart';

class StoreSelector extends StatefulWidget {
  const StoreSelector({super.key});

  @override
  State<StoreSelector> createState() => _StoreSelectorState();
}

class _StoreSelectorState extends State<StoreSelector> {
  late StoreService _storeService;

  @override
  void initState() {
    super.initState();
    _storeService = context.read<StoreService>();
    _storeService.addListener(_onStoreServiceChanged);
  }

  void _onStoreServiceChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _showStoreSelectionDialog() async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false, // Don't allow closing without selection
      builder: (context) => const StoreSelectionDialog(),
    );

    if (result == true && mounted) {
      setState(() {}); // Refresh the display
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDefaults.padding,
        vertical: 8,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.store,
            color: AppColors.primary,
            size: 20,
          ),
        ),
        title: Text(
          'المتجر الحالي',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _storeService.currentStoreDisplayName,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
            ),
            if (_storeService.hasSelectedStore)
              Text(
                _storeService.currentStoreLocationDisplay,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[500],
                    ),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_storeService.isLoading)
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            else
              const Icon(
                Icons.edit_location_alt,
                color: AppColors.primary,
                size: 20,
              ),
            const SizedBox(width: 4),
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.grey[400],
              size: 16,
            ),
          ],
        ),
        onTap: _storeService.isLoading ? null : _showStoreSelectionDialog,
      ),
    );
  }

  @override
  void dispose() {
    _storeService.removeListener(_onStoreServiceChanged);
    super.dispose();
  }
}
