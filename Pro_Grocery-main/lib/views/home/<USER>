import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../core/components/app_back_button.dart';
import '../../core/components/bundle_tile_square.dart';
import '../../core/components/product_tile_square.dart';
import '../../core/constants/constants.dart';
import '../../services/home_service.dart';
import '../../utils/navigation.dart' as nav;
import '../../views/home/<USER>';

class PopularPackPage extends StatefulWidget {
  const PopularPackPage({super.key});

  @override
  State<PopularPackPage> createState() => _PopularPackPageState();
}

class _PopularPackPageState extends State<PopularPackPage> {
  final HomeService _homeService = HomeService();

  @override
  void initState() {
    super.initState();
    // Load popular products when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _homeService.loadPopularProducts();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المنتجات الشائعة'),
        leading: const AppBackButton(),
      ),
      body: SafeArea(
        child: Stack(
          children: [
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
              child: ListenableBuilder(
                listenable: _homeService,
                builder: (context, child) {
                  // Show loading state
                  if (_homeService.isLoadingPopularProducts) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  // Show error state
                  if (_homeService.popularProductsError != null &&
                      _homeService.popularProducts.isEmpty) {
                    return Container(
                      padding: const EdgeInsets.all(AppDefaults.padding),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.error_outline,
                                size: 48, color: Colors.grey),
                            const SizedBox(height: 16),
                            Text(
                              'فشل في تحميل المنتجات الشائعة',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge
                                  ?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: () => _homeService.loadPopularProducts(
                                  forceRefresh: true),
                              child: const Text('إعادة المحاولة'),
                            ),
                          ],
                        ),
                      ),
                    );
                  }

                  // Show products
                  final products = _homeService.popularProducts.isNotEmpty
                      ? _homeService.popularProductsForUI()
                      : Dummy.bundles; // Fallback to dummy data

                  return GridView.builder(
                    padding: const EdgeInsets.only(top: AppDefaults.padding),
                    gridDelegate:
                        const SliverGridDelegateWithMaxCrossAxisExtent(
                      maxCrossAxisExtent: 200,
                      childAspectRatio: 0.73,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                    ),
                    itemCount: products.length,
                    itemBuilder: (context, index) {
                      // Check if this is API data (ProductModel from ProductWithPricing)
                      final productId = _homeService.popularProducts.isNotEmpty
                          ? _homeService.popularProducts[index].id
                          : null;

                      // Use ProductTileSquare for individual products
                      if (_homeService.popularProducts.isNotEmpty) {
                        final productModel = _homeService.popularProducts[index]
                            .toProductModel();
                        return ProductTileSquare(
                          data: productModel,
                          productId: productId,
                        );
                      } else {
                        // Fallback to BundleTileSquare for dummy data
                        return BundleTileSquare(
                          data: products[index],
                        );
                      }
                    },
                  );
                },
              ),
            ),
            Positioned(
              bottom: 0,
              right: 0,
              left: 0,
              child: Container(
                padding: const EdgeInsets.all(AppDefaults.padding * 2),
                decoration: const BoxDecoration(
                  color: Colors.white60,
                ),
                child: ElevatedButton(
                  onPressed: () {
                    nav.Router.push(context, const BundleCreatePage());
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(AppIcons.shoppingBag),
                      const SizedBox(width: AppDefaults.padding),
                      const Text('إنشاء حزمة خاصة'),
                    ],
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
