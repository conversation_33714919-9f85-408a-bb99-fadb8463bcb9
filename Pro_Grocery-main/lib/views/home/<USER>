import 'package:flutter/material.dart';

import '../../api/products.dart';
import '../../api/wholesaler_api.dart';
import '../../core/components/app_back_button.dart';
import '../../core/components/buy_now_row_button.dart';
import '../../core/components/network_image.dart';
import '../../core/components/product_images_slider.dart';
import '../../core/constants/app_defaults.dart';
import '../../services/app_services.dart';
import '../../models/cart_models.dart';
import '../../utils/navigation.dart' as nav;
import '../../views/cart/cart_page.dart';
import '../../views/home/<USER>';
import 'dialogs/wholesaler_selection_dialog.dart';
import '../cart/dialogs/change_wholesaler_dialog.dart';

class ProductDetailsPage extends StatefulWidget {
  const ProductDetailsPage({
    super.key,
    this.productId,
    this.wholesalerMode = false,
    this.wholesalerId,
    this.wholesalerItemId,
    this.wholesalerInfo,
  });

  final int? productId;
  final bool wholesalerMode;
  final int? wholesalerId;
  final int? wholesalerItemId;
  final WholesalerInfo? wholesalerInfo;

  @override
  State<ProductDetailsPage> createState() => _ProductDetailsPageState();
}

class _ProductDetailsPageState extends State<ProductDetailsPage> {
  ProductDetailResponse? _product;
  bool _isLoading = true;
  String? _error;
  late final _regionService = AppServices().regionService;
  late final _cartService = AppServices().cartService;
  late final _multiCartService = AppServices().multiCartService;
  bool _isAddingToCart = false;

  @override
  void initState() {
    super.initState();
    _initializeAndLoadProduct();
    // Listen to cart changes to update UI
    _cartService.addListener(_onCartChanged);
  }

  @override
  void dispose() {
    _cartService.removeListener(_onCartChanged);
    super.dispose();
  }

  void _onCartChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _initializeAndLoadProduct() async {
    // Service is already initialized globally
    _loadProductDetails();
  }

  Future<void> _loadProductDetails() async {
    if (widget.productId == null) {
      setState(() {
        _error = 'Product ID not provided';
        _isLoading = false;
      });
      return;
    }

    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      if (widget.wholesalerMode && widget.wholesalerItemId != null) {
        // Load product details for wholesaler mode
        await _loadWholesalerProductDetails();
      } else {
        // Load normal product details with all wholesaler prices
        final product = await ProductDetailsApiService.getProductById(
          widget.productId!,
          regionId: _regionService.defaultRegionId,
        );

        setState(() {
          _product = product;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  /// Load product details for wholesaler mode
  Future<void> _loadWholesalerProductDetails() async {
    if (widget.wholesalerItemId == null || widget.wholesalerInfo == null) {
      setState(() {
        _error = 'Wholesaler information not provided';
        _isLoading = false;
      });
      return;
    }

    try {
      // Load general product details
      final product = await ProductDetailsApiService.getProductById(
        widget.productId!,
        regionId: _regionService.defaultRegionId,
      );

      // Get specific wholesaler item details
      final wholesalerItem = await WholesalerApiService.getWholesalerItemById(
        widget.wholesalerItemId!,
      );

      // Create a modified product with only current wholesaler pricing
      final wholesalerPriceInfo = ProductPriceInfo(
        price: wholesalerItem.price,
        wholesalerId: widget.wholesalerInfo!.id,
        wholesaler: WholesalerOut(
          id: widget.wholesalerInfo!.id,
          title: widget.wholesalerInfo!.title,
          username: widget.wholesalerInfo!.username,
          category: '', // We don't have category in WholesalerInfo
          logo: widget.wholesalerInfo!.logoUrl,
          backgroundImage: widget.wholesalerInfo!.backgroundImageUrl,
        ),
        inventoryCount: wholesalerItem.inventoryCount,
        minimumOrderQuantity: wholesalerItem.minimumOrderQuantity,
        maximumOrderQuantity: wholesalerItem.maximumOrderQuantity,
        priceExpiry: wholesalerItem.priceExpiry,
        itemId: wholesalerItem.id,
      );

      final modifiedProduct = ProductDetailResponse(
        id: product.id,
        name: product.name,
        title: product.title,
        barcode: product.barcode,
        slug: product.slug,
        description: product.description,
        imageUrl: product.imageUrl,
        companyId: product.companyId,
        categoryId: product.categoryId,
        company: product.company,
        category: product.category,
        unit: product.unit,
        unitCount: product.unitCount,
        prices: [wholesalerPriceInfo], // Only current wholesaler
      );

      setState(() {
        _product = modifiedProduct;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _retry() async {
    await _loadProductDetails();
  }

  /// Handle buy now - same as add to cart but navigate to checkout
  Future<void> _handleBuyNow() async {
    final success = await _handleAddToCart();
    if (success) {
      // Navigate to cart page for checkout
      nav.Router.push(context, const CartPage());
    }
  }

  /// Handle add to cart with wholesaler selection
  Future<bool> _handleAddToCart() async {
    if (_product == null || _product!.prices.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('لا توجد أسعار متاحة لهذا المنتج')),
      );
      return false;
    }

    setState(() {
      _isAddingToCart = true;
    });

    try {
      // In wholesaler mode, directly add to cart using the specific wholesaler
      if (widget.wholesalerMode && _product!.prices.isNotEmpty) {
        final priceInfo =
            _product!.prices.first; // Should be only one wholesaler
        return await _addToCartFromWholesaler(priceInfo);
      }

      // Normal mode - Sort prices by price (lowest first)
      final sortedPrices = List<ProductPriceInfo>.from(_product!.prices)
        ..sort((a, b) => a.price.compareTo(b.price));

      // With multi-cart system, we can add to any wholesaler
      // Check if there's an active wholesaler and this product is available from them
      if (_multiCartService.activeWholesalerId != null) {
        final activeWholesalerId = _multiCartService.activeWholesalerId!;
        final matchingPrice = sortedPrices
            .where(
              (price) => price.wholesaler.id == activeWholesalerId,
            )
            .firstOrNull;

        if (matchingPrice != null) {
          // Add to the active wholesaler's cart
          return await _addToCartFromWholesaler(matchingPrice);
        }
      }

      // No active wholesaler or product not available from active wholesaler
      // Show wholesaler selection dialog
      return await _showWholesalerSelectionDialog(sortedPrices);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ: ${e.toString()}')),
      );
      return false;
    } finally {
      setState(() {
        _isAddingToCart = false;
      });
    }
  }

  /// Show wholesaler selection dialog
  Future<bool> _showWholesalerSelectionDialog(
      List<ProductPriceInfo> prices) async {
    bool success = false;

    await showWholesalerSelectionDialog(
      context: context,
      wholesalerPrices: prices,
      productName: _product!.title,
      onWholesalerSelected: (selectedPrice) async {
        success = await _addToCartFromWholesaler(selectedPrice);
      },
    );

    return success;
  }

  /// Show change wholesaler dialog
  Future<void> _showChangeWholesalerDialog(
      List<ProductPriceInfo> prices) async {
    final currentCart = _cartService.cart!;
    final currentWholesaler = WholesalerInfo(
      id: currentCart.wholesalerId,
      title: currentCart.wholesalerTitle,
      username: '',
      logoUrl: currentCart.wholesalerLogo,
    );

    // Get the cheapest option from new wholesalers
    final newWholesaler = prices.first.wholesaler;
    final newWholesalerInfo = WholesalerInfo(
      id: newWholesaler.id,
      title: newWholesaler.title,
      username: newWholesaler.username,
      logoUrl: newWholesaler.logo,
      backgroundImageUrl: newWholesaler.backgroundImage,
    );

    await showChangeWholesalerDialog(
      context: context,
      currentWholesaler: currentWholesaler,
      newWholesaler: newWholesalerInfo,
      currentCartItemCount: currentCart.itemCount,
      onContinueWithCurrent: () {
        Navigator.of(context).pop();
        // Navigate to wholesaler products page
        nav.Router.push(
          context,
          WholesalerProductsPage(wholesalerId: currentCart.wholesalerId),
        );
      },
      onSwitchWholesaler: () async {
        Navigator.of(context).pop();
        await _cartService.clearCart();
        await _showWholesalerSelectionDialog(prices);
      },
    );
  }

  /// Add product to cart from selected wholesaler
  Future<bool> _addToCartFromWholesaler(ProductPriceInfo selectedPrice) async {
    final wholesalerInfo = WholesalerInfo(
      id: selectedPrice.wholesaler.id,
      title: selectedPrice.wholesaler.title,
      username: selectedPrice.wholesaler.username,
      logoUrl: selectedPrice.wholesaler.logo,
      backgroundImageUrl: selectedPrice.wholesaler.backgroundImage,
    );

    // Use multi-cart service instead of single cart service
    final result = await _multiCartService.addToCart(
      wholesalerId: selectedPrice.wholesaler.id,
      wholesalerItemId: selectedPrice.itemId,
      wholesalerInfo: wholesalerInfo,
      quantity: 1,
    );

    if (result.isSuccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم إضافة ${_product!.title} إلى السلة'),
          backgroundColor: Colors.green,
          action: SnackBarAction(
            label: 'عرض السلة',
            textColor: Colors.white,
            onPressed: () {
              nav.Router.push(context, const CartPage());
            },
          ),
        ),
      );
      return true;
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result.message ?? 'فشل في إضافة المنتج إلى السلة'),
          backgroundColor: Colors.red,
        ),
      );
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        leading: const AppBackButton(),
        title: const Text('تفاصيل المنتج'),
      ),
      bottomNavigationBar: _product != null
          ? SafeArea(
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
                child: BuyNowRow(
                  onBuyButtonTap: _handleBuyNow,
                  onCartButtonTap: _handleAddToCart,
                ),
              ),
            )
          : null,
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(AppDefaults.padding),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.grey),
              const SizedBox(height: 16),
              Text(
                'حدث خطأ في تحميل تفاصيل المنتج',
                style: Theme.of(context).textTheme.titleMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                _error!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _retry,
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      );
    }

    if (_product == null) {
      return const Center(
        child: Text('المنتج غير موجود'),
      );
    }

    return SingleChildScrollView(
      child: Column(
        children: [
          // Product Images
          ProductImagesSlider(
            images: _product!.imageUrls.isNotEmpty
                ? _product!.imageUrls
                : ['https://via.placeholder.com/300x300.png?text=No+Image'],
            productId: _product!.id,
          ),

          // Product Info
          SizedBox(
            width: double.infinity,
            child: Padding(
              padding: const EdgeInsets.all(AppDefaults.padding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _product!.title,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 8),
                  Text('الوزن: ${_product!.unitCount} ${_product!.unit}'),
                  if (_product!.company != null) ...[
                    const SizedBox(height: 4),
                    Text('الشركة: ${_product!.company!.title}'),
                  ],
                  if (_product!.category != null) ...[
                    const SizedBox(height: 4),
                    Text('الفئة: ${_product!.category!.title}'),
                  ],
                ],
              ),
            ),
          ),

          // Price Summary
          _buildPriceSummary(),

          const SizedBox(height: 8),

          // Product Description
          if (_product!.description.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(AppDefaults.padding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تفاصيل المنتج',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                  ),
                  const SizedBox(height: 8),
                  Text(_product!.description),
                ],
              ),
            ),

          // Wholesaler Prices
          _buildWholesalerPrices(),
        ],
      ),
    );
  }

  Widget _buildPriceSummary() {
    final minPrice = _product!.minPrice;
    final maxPrice = _product!.maxPrice;

    if (minPrice == null) {
      return const Padding(
        padding: EdgeInsets.symmetric(horizontal: AppDefaults.padding),
        child: Text('لا توجد أسعار متاحة'),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
      child: Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'أفضل سعر',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
              Text(
                '\$${minPrice.toStringAsFixed(2)}',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Colors.green,
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
          if (maxPrice != null && maxPrice != minPrice) ...[
            const SizedBox(width: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'أعلى سعر',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                ),
                Text(
                  '\$${maxPrice.toStringAsFixed(2)}',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.orange,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
          ],
          const Spacer(),
          Text(
            '${_product!.prices.length} تاجر',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildWholesalerPrices() {
    if (_product!.prices.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(AppDefaults.padding),
        child: Text('لا توجد أسعار متاحة من التجار'),
      );
    }

    // Sort prices by price (lowest first)
    final sortedPrices = List<ProductPriceInfo>.from(_product!.prices)
      ..sort((a, b) => a.price.compareTo(b.price));

    return Padding(
      padding: const EdgeInsets.all(AppDefaults.padding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Divider(thickness: 0.1),
          Text(
            'الأسعار من التجار المختلفين',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
          ),
          const SizedBox(height: 16),
          ...sortedPrices.asMap().entries.map((entry) {
            final index = entry.key;
            final priceInfo = entry.value;
            final isLowestPrice = index == 0;

            return _buildPriceCard(priceInfo, isLowestPrice);
          }),
        ],
      ),
    );
  }

  Widget _buildPriceCard(ProductPriceInfo priceInfo, bool isLowestPrice) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(
          color: isLowestPrice ? Colors.green : Colors.grey[300]!,
          width: isLowestPrice ? 2 : 1,
        ),
        borderRadius: AppDefaults.borderRadius,
        color:
            isLowestPrice ? Colors.green.withValues(alpha: 0.05) : Colors.white,
      ),
      child: Row(
        children: [
          // Wholesaler logo or avatar
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.grey[200],
            ),
            child: priceInfo.wholesaler.logo != null
                ? ClipOval(
                    child: NetworkImageWithLoader(
                      priceInfo.wholesaler.logo!,
                      fit: BoxFit.cover,
                    ),
                  )
                : Icon(
                    Icons.store,
                    color: Colors.grey[400],
                  ),
          ),
          const SizedBox(width: 12),

          // Wholesaler info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  priceInfo.wholesaler.title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                Text(
                  priceInfo.wholesaler.category,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                ),
                // if (priceInfo.inventoryCount > 0)
                //   Text(
                //     'متوفر: ${priceInfo.inventoryCount} قطعة',
                //     style: Theme.of(context).textTheme.bodySmall?.copyWith(
                //           color: Colors.green,
                //         ),
                //   )
                if (priceInfo.inventoryCount < 0)
                  Text(
                    'غير متوفر',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.red,
                        ),
                  ),
                // Quantity constraints
                Text(
                  _getQuantityConstraintText(priceInfo),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                        fontSize: 11,
                      ),
                ),
              ],
            ),
          ),

          // Price and badge
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              if (isLowestPrice)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'أفضل سعر',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
              const SizedBox(height: 4),
              Text(
                '\$${priceInfo.price.toStringAsFixed(2)}',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: isLowestPrice ? Colors.green : Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getQuantityConstraintText(ProductPriceInfo priceInfo) {
    if (priceInfo.maximumOrderQuantity != null) {
      return 'الحد الأقصى للكمية: ${priceInfo.maximumOrderQuantity}';
    } else {
      return 'الحد الأقصى للكمية: غير محدد';
    }
  }
}
