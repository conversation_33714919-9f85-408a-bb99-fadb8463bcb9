import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:grocery/views/entrypoint/entrypoint_ui.dart';
import '../../api/wholesaler_api.dart';
import '../../core/components/app_back_button.dart';
import '../../core/constants/app_defaults.dart';
import '../../core/constants/app_icons.dart';
import '../../models/cart_models.dart';
import '../../services/app_services.dart';

import '../../utils/navigation.dart' as nav;
import '../../views/home/<USER>';
import '../../views/cart/cart_page.dart';
import '../../views/drawer/drawer_page.dart';
import 'components/wholesaler_header.dart';
import 'components/wholesaler_product_card.dart';
import 'components/category_filter_chips.dart';
import 'components/product_sort_controls.dart';

class WholesalerProductsPage extends StatefulWidget {
  final int wholesalerId;
  final bool isHomePage;

  const WholesalerProductsPage({
    super.key,
    required this.wholesalerId,
    this.isHomePage = false,
  });

  @override
  State<WholesalerProductsPage> createState() => _WholesalerProductsPageState();
}

class _WholesalerProductsPageState extends State<WholesalerProductsPage> {
  late final _cartService = AppServices().cartService;
  late final _multiCartService = AppServices().multiCartService;

  WholesalerInfo? _wholesalerInfo;
  List<WholesalerItemResponse> _products = [];
  bool _isLoading = true;
  String? _error;
  String _searchQuery = '';
  String? _selectedCategoryKey; // null means "All" is selected
  SortOption _selectedSort = SortOption.inventoryDesc;

  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  /// Exit wholesaler and clear cart
  Future<void> _exitWholesaler() async {
    await _multiCartService.clearCart(widget.wholesalerId);
    // If this page was accessed directly (not home mode), pop to previous screen
    if (!widget.isHomePage && mounted) {
      Navigator.pop(context);
    }
  }

  @override
  void initState() {
    super.initState();
    _loadWholesalerData();
    _cartService.addListener(_onCartChanged);
    _multiCartService.addListener(_onCartChanged);
  }

  @override
  void dispose() {
    _cartService.removeListener(_onCartChanged);
    _multiCartService.removeListener(_onCartChanged);
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onCartChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _loadWholesalerData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Load wholesaler info and products in parallel
      final results = await Future.wait([
        WholesalerApiService.getWholesalerById(widget.wholesalerId),
        WholesalerApiService.getWholesalerItems(widget.wholesalerId),
      ]);

      _wholesalerInfo = results[0] as WholesalerInfo;
      _products = results[1] as List<WholesalerItemResponse>;

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _refresh() async {
    await _loadWholesalerData();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query.toLowerCase();
    });
  }

  void _onCategorySelected(String? categoryKey) {
    setState(() {
      _selectedCategoryKey = categoryKey;
    });
  }

  void _onSortChanged(SortOption sortOption) {
    setState(() {
      _selectedSort = sortOption;
    });
  }

  List<WholesalerItemResponse> get _filteredProducts {
    List<WholesalerItemResponse> products = _products;

    // Apply category filter first
    if (_selectedCategoryKey != null) {
      products = products.where((product) {
        if (_selectedCategoryKey == 'no_category') {
          return product.category == null;
        } else {
          final categoryId =
              int.tryParse(_selectedCategoryKey!.replaceFirst('category_', ''));
          return product.category?.id == categoryId;
        }
      }).toList();
    }

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      products = products.where((product) {
        return product.productName.toLowerCase().contains(_searchQuery) ||
            (product.company?.title.toLowerCase().contains(_searchQuery) ??
                false) ||
            (product.category?.title.toLowerCase().contains(_searchQuery) ??
                false);
      }).toList();
    }

    // Apply sorting
    switch (_selectedSort) {
      case SortOption.inventoryAsc:
        products.sort((a, b) => a.inventoryCount.compareTo(b.inventoryCount));
        break;
      case SortOption.inventoryDesc:
        products.sort((a, b) => b.inventoryCount.compareTo(a.inventoryCount));
        break;
      case SortOption.priceAsc:
        products.sort((a, b) => a.price.compareTo(b.price));
        break;
      case SortOption.priceDesc:
        products.sort((a, b) => b.price.compareTo(a.price));
        break;
      case SortOption.nameAsc:
        products.sort((a, b) => a.productName.compareTo(b.productName));
        break;
      case SortOption.nameDesc:
        products.sort((a, b) => b.productName.compareTo(a.productName));
        break;
      case SortOption.none:
        // No sorting applied
        break;
    }

    return products;
  }

  Future<void> _addToCart(WholesalerItemResponse product) async {
    if (_wholesalerInfo == null) return;

    final result = await _multiCartService.addToCart(
      wholesalerId: widget.wholesalerId,
      wholesalerItemId: product.id,
      wholesalerInfo: _wholesalerInfo!,
      quantity: 1,
    );

    if (!mounted) return;

    if (result.isSuccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم إضافة ${product.productName} إلى السلة'),
          backgroundColor: Colors.green,
          action: SnackBarAction(
            label: 'عرض السلة',
            textColor: Colors.white,
            onPressed: () {
              nav.Router.push(context, const CartPage());
            },
          ),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result.message ?? 'فشل في إضافة المنتج إلى السلة'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Navigate to product details in wholesaler mode
  void _navigateToProductDetails(WholesalerItemResponse product) {
    nav.Router.push(
      context,
      ProductDetailsPage(
        productId: product.productId,
        wholesalerMode: true,
        wholesalerId: widget.wholesalerId,
        wholesalerItemId: product.id,
        wholesalerInfo: _wholesalerInfo,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return _buildErrorState();
    }

    return RefreshIndicator(
      onRefresh: _refresh,
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // App Bar
          _buildAppBar(),

          // Wholesaler header
          if (_wholesalerInfo != null)
            SliverToBoxAdapter(
              child: WholesalerHeader(
                wholesaler: _wholesalerInfo!,
                cartItemCount: _multiCartService
                        .getCartForWholesaler(widget.wholesalerId)
                        ?.itemCount ??
                    0,
                totalPrice: _multiCartService
                        .getCartForWholesaler(widget.wholesalerId)
                        ?.totalPrice ??
                    0.0,
                onExit: _exitWholesaler,
              ),
            ),

          // Search bar
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(AppDefaults.padding),
              child: TextField(
                controller: _searchController,
                onChanged: _onSearchChanged,
                decoration: InputDecoration(
                  hintText:
                      'ابحث في منتجات ${_wholesalerInfo?.title ?? "التاجر"}',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          onPressed: () {
                            _searchController.clear();
                            _onSearchChanged('');
                          },
                          icon: const Icon(Icons.clear),
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ),

          // Category filter chips
          SliverToBoxAdapter(
            child: CategoryFilterChips(
              products: _products,
              selectedCategoryKey: _selectedCategoryKey,
              onCategorySelected: _onCategorySelected,
            ),
          ),

          // Sorting controls
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.only(
                top: 8,
                bottom: 16,
              ),
              child: ProductSortControls(
                selectedSort: _selectedSort,
                onSortChanged: _onSortChanged,
              ),
            ),
          ),

          // Products header
          SliverToBoxAdapter(
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
              child: Text(
                'المنتجات (${_filteredProducts.length})',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ),
          ),

          const SliverToBoxAdapter(child: SizedBox(height: 12)),

          // Products grid or empty state
          if (_filteredProducts.isEmpty)
            SliverFillRemaining(
              child: _buildEmptyState(),
            )
          else
            SliverPadding(
              padding:
                  const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
              sliver: SliverGrid(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 0.43,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                ),
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final product = _filteredProducts[index];
                    return WholesalerProductCard(
                      product: product,
                      onAddToCart: () => _addToCart(product),
                      onTap: () => _navigateToProductDetails(product),
                      isInCart: _multiCartService.isInCart(
                          widget.wholesalerId, product.id),
                      cartQuantity: _multiCartService.getItemQuantity(
                          widget.wholesalerId, product.id),
                    );
                  },
                  childCount: _filteredProducts.length,
                ),
              ),
            ),

          const SliverToBoxAdapter(child: SizedBox(height: 100)),
        ],
      ),
    );
  }

  Widget _buildAppBar() {
    if (widget.isHomePage) {
      // Home page style app bar
      return SliverAppBar(
        leading: Padding(
          padding: const EdgeInsets.only(left: 8),
          child: ElevatedButton(
            onPressed: () {
              nav.Router.push(context, const DrawerPage());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFF2F6F3),
              shape: const CircleBorder(),
            ),
            child: SvgPicture.asset(AppIcons.sidebarIcon),
          ),
        ),
        floating: true,
        title: Row(
          children: [
            Image.asset(
              "assets/logo_cropped.png",
              height: 32,
            ),
            const SizedBox(width: 8),
            Text(
              "تاجر بلس",
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ],
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 8, top: 4, bottom: 4),
            child: ElevatedButton(
              onPressed: () {
                final entryPointState =
                    context.findAncestorStateOfType<EntryPointUIState>();
                entryPointState?.onBottomNavigationTap(1);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFF2F6F3),
                shape: const CircleBorder(),
              ),
              child: SvgPicture.asset(AppIcons.search),
            ),
          ),
        ],
      );
    } else {
      // Regular page style app bar
      return SliverAppBar(
        leading: const AppBackButton(),
        floating: true,
        title: Text(_wholesalerInfo?.title ?? 'منتجات التاجر'),
        actions: [
          // Cart icon with badge
          Stack(
            children: [
              IconButton(
                onPressed: () {
                  nav.Router.push(context, const CartPage());
                },
                icon: const Icon(Icons.shopping_cart_outlined),
              ),
              if ((_multiCartService
                          .getCartForWholesaler(widget.wholesalerId)
                          ?.itemCount ??
                      0) >
                  0)
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      '${_multiCartService.getCartForWholesaler(widget.wholesalerId)?.itemCount ?? 0}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(width: 8),
        ],
      );
    }
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppDefaults.padding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ في تحميل منتجات التاجر',
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadWholesalerData,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.search_off, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty
                ? 'لا توجد منتجات تطابق البحث'
                : 'لا توجد منتجات متاحة',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          if (_searchQuery.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              'جرب البحث بكلمات مختلفة',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                _searchController.clear();
                _onSearchChanged('');
              },
              child: const Text('مسح البحث'),
            ),
          ],
        ],
      ),
    );
  }
}
