import 'package:flutter/material.dart';

import '../../core/components/app_back_button.dart';
import '../../core/components/product_tile_square.dart';
import '../../core/constants/constants.dart';
import '../../services/home_service.dart';

class NewItemsPage extends StatefulWidget {
  const NewItemsPage({super.key});

  @override
  State<NewItemsPage> createState() => _NewItemsPageState();
}

class _NewItemsPageState extends State<NewItemsPage> {
  final HomeService _homeService = HomeService();

  @override
  void initState() {
    super.initState();
    // Load new products when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _homeService.loadNewProducts();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المنتجات الجديدة'),
        leading: const AppBackButton(),
      ),
      body: Safe<PERSON><PERSON>(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
          child: ListenableBuilder(
            listenable: _homeService,
            builder: (context, child) {
              // Show loading state
              if (_homeService.isLoadingNewProducts) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              // Show error state
              if (_homeService.newProductsError != null &&
                  _homeService.newProducts.isEmpty) {
                return Container(
                  padding: const EdgeInsets.all(AppDefaults.padding),
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.error_outline,
                            size: 48, color: Colors.grey),
                        const SizedBox(height: 16),
                        Text(
                          'فشل في تحميل المنتجات الجديدة',
                          style:
                              Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () =>
                              _homeService.loadNewProducts(forceRefresh: true),
                          child: const Text('إعادة المحاولة'),
                        ),
                      ],
                    ),
                  ),
                );
              }

              // Show products
              final products = _homeService.newProducts.isNotEmpty
                  ? _homeService.newProductsForUI()
                  : Dummy.products; // Fallback to dummy data

              return GridView.builder(
                padding: const EdgeInsets.only(top: AppDefaults.padding),
                gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
                  maxCrossAxisExtent: 200,
                  childAspectRatio: 0.64,
                  mainAxisSpacing: 16,
                ),
                itemCount: products.length,
                itemBuilder: (context, index) {
                  final product = products[index];
                  // Check if this is API data (ProductModel from ProductWithPricing)
                  final productId = _homeService.newProducts.isNotEmpty
                      ? _homeService.newProducts[index].id
                      : null;

                  return ProductTileSquare(
                    data: product,
                    productId: productId,
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }
}
