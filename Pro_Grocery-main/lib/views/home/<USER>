import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_defaults.dart';
import '../../core/components/app_back_button.dart';
import '../../api/wholesaler_api.dart';
import '../../models/cart_models.dart';
import '../../services/multi_cart_service.dart';
import '../../services/app_services.dart';
import '../../services/store_service.dart';
import '../../utils/navigation.dart' as nav;
import '../home/<USER>';

class AllWholesalersPage extends StatefulWidget {
  const AllWholesalersPage({super.key});

  @override
  State<AllWholesalersPage> createState() => _AllWholesalersPageState();
}

class _AllWholesalersPageState extends State<AllWholesalersPage> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  List<WholesalerInfo> _wholesalers = [];
  List<WholesalerInfo> _filteredWholesalers = [];
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _hasMoreData = true;
  String? _error;
  String _searchQuery = '';

  int _currentPage = 1;
  static const int _pageSize = 20;

  @override
  void initState() {
    super.initState();
    _loadWholesalers();
    _scrollController.addListener(_onScroll);

    // Listen for store changes to refresh wholesalers
    AppServices().storeService.addListener(_onStoreChanged);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    AppServices().storeService.removeListener(_onStoreChanged);
    super.dispose();
  }

  void _onStoreChanged() {
    // Refresh wholesalers when store selection changes
    _loadWholesalers(refresh: true);
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreWholesalers();
    }
  }

  Future<void> _loadWholesalers({bool refresh = false}) async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _error = null;
      if (refresh) {
        _wholesalers.clear();
        _currentPage = 1;
        _hasMoreData = true;
      }
    });

    try {
      // Get current region from store service
      final storeService = AppServices().storeService;
      final regionId = storeService.selectedStore?.city.id;

      final response = await WholesalerApiService.getAllWholesalers(
        page: _currentPage,
        pageSize: _pageSize,
        regionId: regionId,
      );

      setState(() {
        if (refresh) {
          _wholesalers = response.wholesalers;
        } else {
          _wholesalers.addAll(response.wholesalers);
        }
        _hasMoreData = response.wholesalers.length == _pageSize;
        _isLoading = false;
        _filterWholesalers();
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreWholesalers() async {
    if (_isLoadingMore || !_hasMoreData || _searchQuery.isNotEmpty) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      _currentPage++;
      // Get current region from store service
      final storeService = AppServices().storeService;
      final regionId = storeService.selectedStore?.city.id;

      final response = await WholesalerApiService.getAllWholesalers(
        page: _currentPage,
        pageSize: _pageSize,
        regionId: regionId,
      );

      setState(() {
        _wholesalers.addAll(response.wholesalers);
        _hasMoreData = response.wholesalers.length == _pageSize;
        _isLoadingMore = false;
        _filterWholesalers();
      });
    } catch (e) {
      setState(() {
        _currentPage--; // Revert page increment on error
        _isLoadingMore = false;
      });
    }
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query.toLowerCase();
      _filterWholesalers();
    });
  }

  void _filterWholesalers() {
    if (_searchQuery.isEmpty) {
      _filteredWholesalers = List.from(_wholesalers);
    } else {
      _filteredWholesalers = _wholesalers.where((wholesaler) {
        return wholesaler.title.toLowerCase().contains(_searchQuery) ||
            wholesaler.username.toLowerCase().contains(_searchQuery);
      }).toList();
    }
  }

  void _navigateToWholesaler(WholesalerInfo wholesaler) {
    nav.Router.push(
      context,
      WholesalerProductsPage(
        wholesalerId: wholesaler.id,
        isHomePage: false,
      ),
    );
  }

  Future<void> _refreshWholesalers() async {
    await _loadWholesalers(refresh: true);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.cardColor,
      appBar: AppBar(
        leading: const AppBackButton(),
        title: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'جميع التجار',
              style: TextStyle(
                fontFamily: 'Gilroy',
                fontWeight: FontWeight.bold,
              ),
            ),
            Consumer<StoreService>(
              builder: (context, storeService, child) {
                final store = storeService.selectedStore;
                if (store != null) {
                  return Text(
                    'في ${store.city.name}',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.normal,
                      color: Colors.grey,
                    ),
                  );
                }
                return const Text(
                  'جميع المناطق',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.normal,
                    color: Colors.grey,
                  ),
                );
              },
            ),
          ],
        ),
        backgroundColor: AppColors.cardColor,
        elevation: 0,
        centerTitle: true,
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            margin: const EdgeInsets.all(AppDefaults.margin),
            child: TextField(
              controller: _searchController,
              onChanged: _onSearchChanged,
              decoration: InputDecoration(
                hintText: 'البحث في التجار...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _onSearchChanged('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.white,
              ),
            ),
          ),

          // Content
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading && _wholesalers.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null && _wholesalers.isEmpty) {
      return _buildErrorWidget();
    }

    if (_filteredWholesalers.isEmpty) {
      return _buildEmptyWidget();
    }

    return RefreshIndicator(
      onRefresh: _refreshWholesalers,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.symmetric(horizontal: AppDefaults.margin),
        itemCount: _filteredWholesalers.length + (_isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _filteredWholesalers.length) {
            return const Padding(
              padding: EdgeInsets.all(16),
              child: Center(child: CircularProgressIndicator()),
            );
          }

          final wholesaler = _filteredWholesalers[index];
          return _WholesalerListTile(
            wholesaler: wholesaler,
            onTap: () => _navigateToWholesaler(wholesaler),
          );
        },
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل التجار',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => _loadWholesalers(refresh: true),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _searchQuery.isNotEmpty ? Icons.search_off : Icons.store_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty
                ? 'لا توجد نتائج للبحث'
                : 'لا توجد تجار متاحون',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty
                ? 'جرب كلمات بحث أخرى'
                : 'سيتم إضافة التجار قريباً',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          if (_searchQuery.isNotEmpty) ...[
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                _searchController.clear();
                _onSearchChanged('');
              },
              child: const Text('مسح البحث'),
            ),
          ],
        ],
      ),
    );
  }
}

class _WholesalerListTile extends StatelessWidget {
  final WholesalerInfo wholesaler;
  final VoidCallback onTap;

  const _WholesalerListTile({
    required this.wholesaler,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<MultiCartService>(
      builder: (context, multiCartService, child) {
        final hasCart =
            multiCartService.getCartForWholesaler(wholesaler.id) != null;
        final cartItemCount =
            multiCartService.getCartForWholesaler(wholesaler.id)?.itemCount ??
                0;

        return Container(
          margin: const EdgeInsets.only(bottom: AppDefaults.margin),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: hasCart ? AppColors.primary : Colors.grey[300]!,
              width: hasCart ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.all(AppDefaults.padding),
            leading: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: wholesaler.logoUrl != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        wholesaler.logoUrl!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Icon(
                            Icons.store,
                            size: 30,
                            color: Colors.grey[400],
                          );
                        },
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Center(
                            child: CircularProgressIndicator(
                              value: loadingProgress.expectedTotalBytes != null
                                  ? loadingProgress.cumulativeBytesLoaded /
                                      loadingProgress.expectedTotalBytes!
                                  : null,
                              strokeWidth: 2,
                            ),
                          );
                        },
                      ),
                    )
                  : Icon(
                      Icons.store,
                      size: 30,
                      color: Colors.grey[400],
                    ),
            ),
            title: Text(
              wholesaler.title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: hasCart ? AppColors.primary : Colors.black,
                  ),
            ),
            subtitle: wholesaler.username.isNotEmpty
                ? Text(
                    '@${wholesaler.username}',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                  )
                : null,
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (hasCart) ...[
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      cartItemCount.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.grey[400],
                ),
              ],
            ),
            onTap: onTap,
          ),
        );
      },
    );
  }
}
