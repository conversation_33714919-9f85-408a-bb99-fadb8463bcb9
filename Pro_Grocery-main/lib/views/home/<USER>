import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:grocery/views/entrypoint/entrypoint_ui.dart';
import '../../core/constants/app_icons.dart';
import '../../services/app_services.dart';
import '../../services/home_service.dart';

import '../../core/constants/app_defaults.dart';
import '../../utils/navigation.dart' as nav;
import '../../views/drawer/drawer_page.dart';
import 'components/ad_space.dart';
import 'components/our_new_item.dart';
import 'components/popular_packs.dart';
import 'components/store_selector.dart';
import 'components/wholesaler_grid.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final HomeService _homeService = HomeService();

  @override
  void initState() {
    super.initState();
    // Load home data when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _homeService.loadHomeData(
          regionId: AppServices().regionService.selectedRegion?.id);
    });
  }

  Future<void> _handleRefresh() async {
    await _homeService.refresh();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: _handleRefresh,
          child: CustomScrollView(
            slivers: [
              SliverAppBar(
                leading: Padding(
                  padding: const EdgeInsets.only(left: 8),
                  child: ElevatedButton(
                    onPressed: () {
                      nav.Router.push(context, const DrawerPage());
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFF2F6F3),
                      shape: const CircleBorder(),
                    ),
                    child: SvgPicture.asset(AppIcons.sidebarIcon),
                  ),
                ),
                floating: true,
                title: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image.asset(
                      AppIcons.logoCropped,
                      height: 32,
                    ),
                    const SizedBox(width: 11),
                    Text(
                      "تاجر بلس",
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ],
                ),
                actions: [
                  Padding(
                    padding: const EdgeInsets.only(right: 8, top: 4, bottom: 4),
                    child: ElevatedButton(
                      onPressed: () {
                        // get the state of EntryPointUIState from context
                        final entryPointState = context
                            .findAncestorStateOfType<EntryPointUIState>();
                        entryPointState?.onBottomNavigationTap(1);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFFF2F6F3),
                        shape: const CircleBorder(),
                      ),
                      child: SvgPicture.asset(AppIcons.search),
                    ),
                  ),
                ],
              ),
              const SliverToBoxAdapter(
                child: StoreSelector(),
              ),
              const SliverToBoxAdapter(
                child: AdSpace(),
              ),
              const SliverToBoxAdapter(
                child: WholesalerGrid(),
              ),
              const SliverPadding(
                padding: EdgeInsets.symmetric(vertical: AppDefaults.padding),
                sliver: SliverToBoxAdapter(
                  child: PopularPacks(),
                ),
              ),
              const SliverPadding(
                padding: EdgeInsets.symmetric(vertical: AppDefaults.padding),
                sliver: SliverToBoxAdapter(
                  child: OurNewItem(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
