import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../api/wholesaler_api.dart';
import '../../../core/constants/constants.dart';
import '../../../core/components/network_image.dart';

class WholesalerProductCard extends StatelessWidget {
  final WholesalerItemResponse product;
  final VoidCallback onAddToCart;
  final VoidCallback? onTap;
  final bool isInCart;
  final int cartQuantity;

  const WholesalerProductCard({
    super.key,
    required this.product,
    required this.onAddToCart,
    this.onTap,
    required this.isInCart,
    required this.cartQuantity,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isInCart
            ? const BorderSide(
                color: AppColors.primary,
                width: 2,
              )
            : BorderSide.none,
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: onTap,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product image
            Expanded(
              flex: 3,
              child: Stack(
                children: [
                  Container(
                    width: double.infinity,
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.vertical(
                        top: Radius.circular(12),
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(12),
                      ),
                      child: product.imageUrl != null
                          ? NetworkImageWithLoader(
                              product.imageUrl!,
                              fit: BoxFit.fitWidth,
                            )
                          : Container(
                              color: Colors.grey[200],
                              child: Center(
                                child: SvgPicture.asset(
                                  AppIcons.shoppingBag,
                                  width: 40,
                                  height: 40,
                                  colorFilter: ColorFilter.mode(
                                    Colors.grey[400]!,
                                    BlendMode.srcIn,
                                  ),
                                ),
                              ),
                            ),
                    ),
                  ),

                  // In cart indicator
                  if (isInCart)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: AppColors.primary,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.2),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Text(
                          '$cartQuantity',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),

                  // Stock indicator
                  if (product.inventoryCount <= 0)
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'نفذ المخزون',
                          style:
                              Theme.of(context).textTheme.labelSmall?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                      ),
                    )
                  else if (product.inventoryCount <= 5)
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.orange,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'كمية محدودة',
                          style:
                              Theme.of(context).textTheme.labelSmall?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // Product details
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Product name
                    Text(
                      product.productName,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 4),

                    // Unit info
                    Text(
                      '${product.unitCount} ${product.unit}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                    ),

                    if (product.company != null) ...[
                      const SizedBox(height: 2),
                      Text(
                        product.company!.title,
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                              color: Colors.grey[500],
                            ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],

                    const Spacer(),

                    // Price and action
                    Row(
                      children: [
                        // Price
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '${product.price.toStringAsFixed(2)} ج.م',
                                style: Theme.of(context)
                                    .textTheme
                                    .titleMedium
                                    ?.copyWith(
                                      color: AppColors.primary,
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                              if (product.priceExpiry != null)
                                Text(
                                  'عرض محدود',
                                  style: Theme.of(context)
                                      .textTheme
                                      .labelSmall
                                      ?.copyWith(
                                        color: Colors.orange,
                                      ),
                                ),
                              // Quantity constraints
                              Text(
                                _getQuantityConstraintText(),
                                style: Theme.of(context)
                                    .textTheme
                                    .labelSmall
                                    ?.copyWith(
                                      color: Colors.grey[600],
                                      fontSize: 10,
                                    ),
                              ),
                            ],
                          ),
                        ),

                        // Add to cart button
                        SizedBox(
                          width: 40,
                          height: 40,
                          child: Material(
                            color: product.inventoryCount <= 0
                                ? Colors.grey[300]
                                : isInCart
                                    ? AppColors.primary
                                    : AppColors.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            child: InkWell(
                              borderRadius: BorderRadius.circular(8),
                              onTap: product.inventoryCount <= 0
                                  ? null
                                  : onAddToCart,
                              child: Icon(
                                isInCart ? Icons.check : Icons.add,
                                color: product.inventoryCount <= 0
                                    ? Colors.grey[500]
                                    : isInCart
                                        ? Colors.white
                                        : AppColors.primary,
                                size: 20,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getQuantityConstraintText() {
    if (product.maximumOrderQuantity != null) {
      return 'الكمية: ${product.minimumOrderQuantity} - ${product.maximumOrderQuantity}';
    } else {
      return 'الحد الأدنى: ${product.minimumOrderQuantity}';
    }
  }
}
