import 'package:flutter/material.dart';
import '../../../api/wholesaler_api.dart';
import '../../../core/constants/constants.dart';

class CategoryFilterChips extends StatelessWidget {
  final List<WholesalerItemResponse> products;
  final String? selectedCategoryKey;
  final Function(String?) onCategorySelected;

  const CategoryFilterChips({
    super.key,
    required this.products,
    required this.selectedCategoryKey,
    required this.onCategorySelected,
  });

  @override
  Widget build(BuildContext context) {
    final categories = _extractCategories();

    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = selectedCategoryKey == category.key;

          return Container(
            margin: EdgeInsets.only(
              left: index < categories.length - 1 ? 8 : 0, // RTL spacing
            ),
            child: FilterChip(
              label: Text(
                category.title,
                style: TextStyle(
                  color: isSelected ? Colors.white : AppColors.primary,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
              selected: isSelected,
              onSelected: (selected) {
                onCategorySelected(selected ? category.key : null);
              },
              backgroundColor: Colors.grey[100],
              selectedColor: AppColors.primary,
              checkmarkColor: Colors.white,
              side: BorderSide(
                color: isSelected ? AppColors.primary : Colors.grey[300]!,
                width: 1,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
          );
        },
      ),
    );
  }

  /// Extract unique categories from products and create filter options
  List<CategoryFilterOption> _extractCategories() {
    final List<CategoryFilterOption> categories = [];

    // Add "All" option first
    categories.add(CategoryFilterOption(
      key: null,
      title: 'الكل',
      count: products.length,
    ));

    // Group products by category
    final Map<String, List<WholesalerItemResponse>> categoryGroups = {};

    for (final product in products) {
      String categoryKey;

      if (product.category != null) {
        categoryKey = 'category_${product.category!.id}';
      } else {
        categoryKey = 'no_category';
      }

      if (!categoryGroups.containsKey(categoryKey)) {
        categoryGroups[categoryKey] = [];
      }
      categoryGroups[categoryKey]!.add(product);
    }

    // Convert to filter options and sort
    final categoryOptions = categoryGroups.entries.map((entry) {
      return CategoryFilterOption(
        key: entry.key,
        title: _getCategoryTitle(entry.key),
        count: entry.value.length,
      );
    }).toList();

    // Sort categories: named categories first, then "other products"
    categoryOptions.sort((a, b) {
      if (a.key == 'no_category') return 1;
      if (b.key == 'no_category') return -1;
      return a.title.compareTo(b.title);
    });

    categories.addAll(categoryOptions);

    return categories;
  }

  /// Get category title from category key
  String _getCategoryTitle(String categoryKey) {
    if (categoryKey == 'no_category') {
      return 'منتجات أخرى';
    }

    // Extract category ID from key and find the category
    final categoryId = int.tryParse(categoryKey.replaceFirst('category_', ''));
    if (categoryId != null) {
      try {
        final product = products.firstWhere(
          (p) => p.category?.id == categoryId,
        );
        return product.category?.title ?? 'فئة غير محددة';
      } catch (e) {
        return 'فئة غير محددة';
      }
    }

    return 'فئة غير محددة';
  }
}

class CategoryFilterOption {
  final String? key;
  final String title;
  final int count;

  CategoryFilterOption({
    required this.key,
    required this.title,
    required this.count,
  });
}
