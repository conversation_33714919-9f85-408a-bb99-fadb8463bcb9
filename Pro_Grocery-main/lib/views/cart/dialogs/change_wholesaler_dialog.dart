import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../core/constants/constants.dart';
import '../../../core/components/network_image.dart';
import '../../../models/cart_models.dart';

class ChangeWholesalerDialog extends StatelessWidget {
  final WholesalerInfo currentWholesaler;
  final WholesalerInfo newWholesaler;
  final int currentCartItemCount;
  final VoidCallback onContinueWithCurrent;
  final VoidCallback onSwitchWholesaler;

  const ChangeWholesalerDialog({
    super.key,
    required this.currentWholesaler,
    required this.newWholesaler,
    required this.currentCartItemCount,
    required this.onContinueWithCurrent,
    required this.onSwitchWholesaler,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Warning icon
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.warning_amber_rounded,
                color: Colors.orange,
                size: 32,
              ),
            ),

            const SizedBox(height: 16),

            // Title
            Text(
              'تغيير التاجر؟',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 12),

            // Warning message
            Text(
              'لديك $currentCartItemCount منتج في السلة من ${currentWholesaler.title}.',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 8),

            Text(
              'إذا اخترت التاجر الجديد سيتم حذف جميع المنتجات الحالية من السلة.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.red[600],
                    fontWeight: FontWeight.w500,
                  ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 20),

            // Current vs New wholesaler comparison
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Column(
                children: [
                  // Current wholesaler
                  _buildWholesalerRow(
                    context: context,
                    title: 'التاجر الحالي',
                    wholesaler: currentWholesaler,
                    badgeText: '$currentCartItemCount منتج',
                    badgeColor: AppColors.primary,
                  ),

                  const SizedBox(height: 12),

                  // Divider with arrow
                  Row(
                    children: [
                      Expanded(child: Divider(color: Colors.grey[300])),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Icon(
                          Icons.keyboard_arrow_down,
                          color: Colors.grey[400],
                        ),
                      ),
                      Expanded(child: Divider(color: Colors.grey[300])),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // New wholesaler
                  _buildWholesalerRow(
                    context: context,
                    title: 'التاجر الجديد',
                    wholesaler: newWholesaler,
                    badgeText: 'جديد',
                    badgeColor: Colors.green,
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Action buttons
            Column(
              children: [
                // Continue with current wholesaler (primary action)
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: onContinueWithCurrent,
                    icon: const Icon(Icons.shopping_cart),
                    label: Text('متابعة مع ${currentWholesaler.title}'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),

                const SizedBox(height: 8),

                // Switch to new wholesaler (secondary action)
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton.icon(
                    onPressed: onSwitchWholesaler,
                    icon: const Icon(Icons.delete_outline),
                    label: Text('حذف السلة والتبديل لـ${newWholesaler.title}'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red,
                      side: const BorderSide(color: Colors.red),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),

                const SizedBox(height: 8),

                // Cancel button
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إلغاء'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWholesalerRow({
    required BuildContext context,
    required String title,
    required WholesalerInfo wholesaler,
    required String badgeText,
    required Color badgeColor,
  }) {
    return Row(
      children: [
        // Wholesaler logo
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey[200],
          ),
          child: wholesaler.logoUrl != null
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: NetworkImageWithLoader(
                    wholesaler.logoUrl!,
                    fit: BoxFit.cover,
                  ),
                )
              : Center(
                  child: SvgPicture.asset(
                    AppIcons.shoppingBag,
                    width: 20,
                    height: 20,
                    colorFilter: ColorFilter.mode(
                      Colors.grey[400]!,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
        ),

        const SizedBox(width: 12),

        // Wholesaler info
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
              const SizedBox(height: 2),
              Text(
                wholesaler.title,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
        ),

        // Badge
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: badgeColor,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            badgeText,
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
          ),
        ),
      ],
    );
  }
}

/// Helper function to show the change wholesaler dialog
Future<void> showChangeWholesalerDialog({
  required BuildContext context,
  required WholesalerInfo currentWholesaler,
  required WholesalerInfo newWholesaler,
  required int currentCartItemCount,
  required VoidCallback onContinueWithCurrent,
  required VoidCallback onSwitchWholesaler,
}) {
  return showDialog(
    context: context,
    barrierDismissible: true,
    builder: (context) => ChangeWholesalerDialog(
      currentWholesaler: currentWholesaler,
      newWholesaler: newWholesaler,
      currentCartItemCount: currentCartItemCount,
      onContinueWithCurrent: onContinueWithCurrent,
      onSwitchWholesaler: onSwitchWholesaler,
    ),
  );
}
