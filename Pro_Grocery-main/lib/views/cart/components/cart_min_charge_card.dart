import 'package:flutter/material.dart';

import '../../../core/constants/constants.dart';
import '../../../api/wholesaler_api.dart';

class CartMinChargeCard extends StatelessWidget {
  final WholesalerMinChargeResponse minChargeInfo;
  final double currentTotal;
  final int currentItems;

  const CartMinChargeCard({
    super.key,
    required this.minChargeInfo,
    required this.currentTotal,
    required this.currentItems,
  });

  bool get _meetsMinCharge => currentTotal >= minChargeInfo.minCharge;
  bool get _meetsMinItems => currentItems >= minChargeInfo.minItems;
  bool get _canProceed => _meetsMinCharge && _meetsMinItems;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: _canProceed ? Colors.green.shade50 : Colors.orange.shade50,
        borderRadius: BorderRadius.circular(AppDefaults.radius),
        border: Border.all(
          color: _canProceed ? Colors.green.shade200 : Colors.orange.shade200,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                _canProceed ? Icons.check_circle : Icons.info,
                color: _canProceed
                    ? Colors.green.shade600
                    : Colors.orange.shade600,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'شروط الحد الأدنى للطلب',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontFamily: 'Gilroy',
                      fontWeight: FontWeight.bold,
                      color: _canProceed
                          ? Colors.green.shade700
                          : Colors.orange.shade700,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Minimum Charge Progress
          _buildProgressItem(
            context,
            'الحد الأدنى للمبلغ',
            '${minChargeInfo.minCharge.toStringAsFixed(2)} ج.م',
            '${currentTotal.toStringAsFixed(2)} ج.م',
            currentTotal / minChargeInfo.minCharge,
            _meetsMinCharge,
          ),

          const SizedBox(height: 12),

          // Minimum Items Progress
          _buildProgressItem(
            context,
            'الحد الأدنى للمنتجات',
            '${minChargeInfo.minItems} منتج',
            '$currentItems منتج',
            currentItems / minChargeInfo.minItems,
            _meetsMinItems,
          ),

          if (!_canProceed) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.warning_amber,
                    color: Colors.orange.shade700,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _buildRequirementMessage(),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontFamily: 'Gilroy',
                            color: Colors.orange.shade700,
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                  ),
                ],
              ),
            ),
          ],

          if (_canProceed) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: Colors.green.shade700,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'تم الوصول للحد الأدنى! يمكنك الآن المتابعة للدفع',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontFamily: 'Gilroy',
                            color: Colors.green.shade700,
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProgressItem(
    BuildContext context,
    String label,
    String requirement,
    String current,
    double progress,
    bool isComplete,
  ) {
    final clampedProgress = progress.clamp(0.0, 1.0);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontFamily: 'Gilroy',
                    fontWeight: FontWeight.w600,
                  ),
            ),
            Row(
              children: [
                Text(
                  current,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontFamily: 'Gilroy',
                        fontWeight: FontWeight.bold,
                        color: isComplete
                            ? Colors.green.shade700
                            : Colors.orange.shade700,
                      ),
                ),
                Text(
                  ' / $requirement',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontFamily: 'Gilroy',
                        color: Colors.grey.shade600,
                      ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: clampedProgress,
          backgroundColor: Colors.grey.shade200,
          valueColor: AlwaysStoppedAnimation<Color>(
            isComplete ? Colors.green.shade600 : Colors.orange.shade600,
          ),
        ),
      ],
    );
  }

  String _buildRequirementMessage() {
    final messages = <String>[];

    if (!_meetsMinCharge) {
      final needed = minChargeInfo.minCharge - currentTotal;
      messages.add('${needed.toStringAsFixed(2)} ج.م إضافية');
    }

    if (!_meetsMinItems) {
      final needed = minChargeInfo.minItems - currentItems;
      messages.add('$needed منتج إضافي');
    }

    if (messages.length == 1) {
      return 'تحتاج إلى ${messages.first} للوصول للحد الأدنى';
    } else {
      return 'تحتاج إلى ${messages.join(' و ')} للوصول للحد الأدنى';
    }
  }
}
