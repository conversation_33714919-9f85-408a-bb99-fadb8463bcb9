import 'package:flutter/material.dart';
import '../../../core/constants/constants.dart';
import '../../../api/stores_api.dart';
import '../../../models/store_models.dart';
import '../../../services/app_services.dart';
import '../dialogs/create_store_dialog.dart';

class StoreSelector extends StatefulWidget {
  final StoreData? selectedStore;
  final Function(StoreData) onStoreSelected;
  final int? requiredRegionId;

  const StoreSelector({
    super.key,
    this.selectedStore,
    required this.onStoreSelected,
    this.requiredRegionId,
  });

  @override
  State<StoreSelector> createState() => _StoreSelectorState();
}

class _StoreSelectorState extends State<StoreSelector> {
  List<StoreData> _stores = [];
  bool _isLoading = false;
  String? _error;
  StoreData? _selectedStore;

  @override
  void initState() {
    super.initState();
    _selectedStore = widget.selectedStore;
    _loadStores();
  }

  Future<void> _loadStores() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await StoresApiService.getUserStores();
      final stores = response.data ?? <StoreData>[];

      setState(() {
        _stores = stores;
        _isLoading = false;
      });

      // Auto-select first compatible store if none selected
      if (_selectedStore == null && stores.isNotEmpty) {
        final compatibleStore = _findCompatibleStore(stores);
        if (compatibleStore != null) {
          _selectStore(compatibleStore);
        }
      }
    } catch (e) {
      setState(() {
        _error = 'حدث خطأ في تحميل المتاجر: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  StoreData? _findCompatibleStore(List<StoreData> stores) {
    if (widget.requiredRegionId == null) return stores.first;

    return stores.firstWhere(
      (store) => _isStoreCompatible(store),
      orElse: () => stores.first,
    );
  }

  bool _isStoreCompatible(StoreData store) {
    if (widget.requiredRegionId == null) return true;

    // Check if store's region matches the required region
    // This assumes the region hierarchy: country -> state -> city
    return store.city.id == widget.requiredRegionId ||
        store.state.id == widget.requiredRegionId ||
        store.country.id == widget.requiredRegionId;
  }

  void _selectStore(StoreData store) {
    setState(() {
      _selectedStore = store;
    });
    widget.onStoreSelected(store);
  }

  Future<void> _showCreateStoreDialog() async {
    final regionService = AppServices().regionService;

    if (regionService.selectedRegion == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب اختيار المنطقة أولاً'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final newStore = await showDialog<StoreData>(
      context: context,
      builder: (context) => CreateStoreDialog(
        requiredRegionId: widget.requiredRegionId,
      ),
    );

    if (newStore != null) {
      setState(() {
        _stores.add(newStore);
      });
      _selectStore(newStore);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppDefaults.padding),
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppDefaults.radius),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.store,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'عنوان التوصيل',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontFamily: 'Gilroy',
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
              ),
              const Spacer(),
              TextButton.icon(
                onPressed: _showCreateStoreDialog,
                icon: const Icon(Icons.add, size: 18),
                label: const Text(
                  'إضافة عنوان جديد',
                  style: TextStyle(
                    fontFamily: 'Gilroy',
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          if (_isLoading) ...[
            const Center(child: CircularProgressIndicator()),
          ] else if (_error != null) ...[
            _buildErrorWidget(),
          ] else if (_stores.isEmpty) ...[
            _buildEmptyStoresWidget(),
          ] else ...[
            // Store List
            ...(_stores.map((store) => _buildStoreCard(store))),
          ],
        ],
      ),
    );
  }

  Widget _buildStoreCard(StoreData store) {
    final isSelected = _selectedStore?.id == store.id;
    final isCompatible = _isStoreCompatible(store);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: GestureDetector(
        onTap: isCompatible
            ? () => _selectStore(store)
            : () => _showIncompatibleStoreMessage(store),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isSelected
                ? AppColors.coloredBackground
                : (isCompatible ? Colors.grey.shade50 : Colors.red.shade50),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected
                  ? Theme.of(context).primaryColor
                  : (isCompatible ? Colors.grey.shade200 : Colors.red.shade200),
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              // Radio button
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected
                        ? Theme.of(context).primaryColor
                        : (isCompatible
                            ? Colors.grey.shade400
                            : Colors.red.shade400),
                    width: 2,
                  ),
                  color: isSelected
                      ? Theme.of(context).primaryColor
                      : Colors.transparent,
                ),
                child: isSelected
                    ? const Icon(
                        Icons.check,
                        size: 12,
                        color: Colors.white,
                      )
                    : null,
              ),
              const SizedBox(width: 16),

              // Store info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      store.name,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            fontFamily: 'Gilroy',
                            fontWeight: FontWeight.bold,
                            color: isSelected
                                ? Theme.of(context).primaryColor
                                : Colors.black87,
                          ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      store.fullAddress,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontFamily: 'Gilroy',
                            color: Colors.grey.shade600,
                          ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (!isCompatible) ...[
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.red.shade100,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'المنطقة غير متوافقة مع التاجر',
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    fontFamily: 'Gilroy',
                                    color: Colors.red.shade700,
                                    fontWeight: FontWeight.w500,
                                  ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red.shade600),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _error!,
              style: TextStyle(
                color: Colors.red.shade700,
                fontFamily: 'Gilroy',
              ),
            ),
          ),
          TextButton(
            onPressed: _loadStores,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyStoresWidget() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Icon(
            Icons.store_outlined,
            size: 48,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد عناوين محفوظة',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontFamily: 'Gilroy',
                  color: Colors.grey.shade600,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'أضف عنوان توصيل للمتابعة',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontFamily: 'Gilroy',
                  color: Colors.grey.shade500,
                ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _showCreateStoreDialog,
            icon: const Icon(Icons.add),
            label: const Text(
              'إضافة عنوان جديد',
              style: TextStyle(fontFamily: 'Gilroy'),
            ),
          ),
        ],
      ),
    );
  }

  void _showIncompatibleStoreMessage(StoreData store) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
          'هذا العنوان في منطقة مختلفة عن التاجر المحدد. يجب أن يكون العنوان في نفس منطقة التاجر.',
          style: TextStyle(fontFamily: 'Gilroy'),
        ),
        backgroundColor: Colors.orange,
        duration: Duration(seconds: 4),
      ),
    );
  }
}
