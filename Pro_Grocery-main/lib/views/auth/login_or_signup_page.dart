import 'package:flutter/material.dart';

import '../../core/components/network_image.dart';
import '../../core/constants/constants.dart';
import '../../utils/navigation.dart' as nav;
import '../../views/auth/login_page.dart';

class LoginOrSignUpPage extends StatelessWidget {
  const LoginOrSignUpPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Column(
        children: [
          Spacer(flex: 2),
          _AppLogoAndHeadline(),
          Spacer(),
          _Footer(),
          Spacer(),
        ],
      ),
    );
  }
}

class _Footer extends StatelessWidget {
  const _Footer();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: Padding(
            padding: const EdgeInsets.all(AppDefaults.padding),
            child: ElevatedButton(
              onPressed: () => nav.Router.push(context, const LoginPage()),
              child: const Text('تسجيل الدخول رقم الهاتف'),
            ),
          ),
        ),
        const SizedBox(height: AppDefaults.margin),
        // Text(
        //   'أو',
        //   style: Theme.of(context)
        //       .textTheme
        //       .titleLarge
        //       ?.copyWith(fontWeight: FontWeight.bold),
        // ),
        // const SizedBox(height: AppDefaults.margin),
        // Row(
        //   mainAxisAlignment: MainAxisAlignment.center,
        //   children: [
        //     IconButton(
        //       onPressed: () {},
        //       icon: SvgPicture.asset(AppIcons.appleIcon),
        //       iconSize: 48,
        //     ),
        //     IconButton(
        //       onPressed: () {},
        //       icon: SvgPicture.asset(AppIcons.googleIcon),
        //       iconSize: 48,
        //     ),
        //     IconButton(
        //       onPressed: () {},
        //       icon: SvgPicture.asset(AppIcons.twitterIcon),
        //       iconSize: 48,
        //     ),
        //     IconButton(
        //       onPressed: () {},
        //       icon: SvgPicture.asset(AppIcons.facebookIcon),
        //       iconSize: 48,
        //     ),
        //   ],
        // ),
      ],
    );
  }
}

class _AppLogoAndHeadline extends StatelessWidget {
  const _AppLogoAndHeadline();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          width: MediaQuery.of(context).size.width * 0.3,
          child: const AspectRatio(
            aspectRatio: 1 / 1,
            child: NetworkImageWithLoader(AppImages.roundedLogo),
          ),
        ),
        Text(
          'اهلا بعودتك',
          style: Theme.of(context)
              .textTheme
              .headlineSmall
              ?.copyWith(fontWeight: FontWeight.bold),
        ),
        Text(
          'تاجر بلس',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
        )
      ],
    );
  }
}
