import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';

import '../../../core/constants/constants.dart';
import '../../../core/utils/validators.dart';
import '../../../services/auth.dart';
import '../../../utils/navigation.dart' as nav;
import '../../../views/auth/number_verification_page.dart';
import 'already_have_accout.dart';
import 'sign_up_button.dart';

class SignUpForm extends StatefulWidget {
  const SignUpForm({
    super.key,
  });

  @override
  State<SignUpForm> createState() => _SignUpFormState();
}

class _SignUpFormState extends State<SignUpForm> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _emailController = TextEditingController();

  bool isPasswordShown = false;
  bool isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  void onPassShowClicked() {
    isPasswordShown = !isPasswordShown;
    setState(() {});
  }

  Future<void> onSignUp() async {
    final bool isFormOkay = _formKey.currentState?.validate() ?? false;
    if (!isFormOkay) return;

    setState(() {
      isLoading = true;
    });

    try {
      final authResult = await AuthService.signup(
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim(),
        password: _passwordController.text,
        email: _emailController.text.trim().isEmpty
            ? null
            : _emailController.text.trim(),
      );

      if (!mounted) return;

      if (authResult.success) {
        // For now, we'll redirect to number verification
        // In the future, if verification is skipped, redirect based on user type
        nav.Router.push(context, const NumberVerificationPage());

        // Show success message
        final message =
            authResult.signupData?.message ?? 'تم إنشاء الحساب بنجاح!';
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authResult.error ?? 'فشل التسجيل'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ غير متوقع'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppDefaults.margin),
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: AppDefaults.boxShadow,
        borderRadius: AppDefaults.borderRadius,
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text("الاسم"),
            const SizedBox(height: 8),
            TextFormField(
              controller: _nameController,
              validator: Validators.requiredWithFieldName('الاسم').call,
              textInputAction: TextInputAction.next,
              enabled: !isLoading,
            ),
            const SizedBox(height: AppDefaults.padding),
            const Text("رقم الهاتف"),
            const SizedBox(height: 8),
            TextFormField(
              controller: _phoneController,
              textInputAction: TextInputAction.next,
              validator: Validators.requiredWithFieldName('رقم الهاتف').call,
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              enabled: !isLoading,
            ),
            const SizedBox(height: AppDefaults.padding),
            const Text("البريد الإلكتروني (اختياري)"),
            const SizedBox(height: 8),
            TextFormField(
              controller: _emailController,
              textInputAction: TextInputAction.next,
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  return Validators.email.call(value);
                }
                return null;
              },
              keyboardType: TextInputType.emailAddress,
              enabled: !isLoading,
            ),
            const SizedBox(height: AppDefaults.padding),
            const Text("كلمة المرور"),
            const SizedBox(height: 8),
            TextFormField(
              controller: _passwordController,
              validator: Validators.password.call,
              textInputAction: TextInputAction.done,
              obscureText: !isPasswordShown,
              enabled: !isLoading,
              onFieldSubmitted: (v) => onSignUp(),
              decoration: InputDecoration(
                suffixIcon: Material(
                  color: Colors.transparent,
                  child: IconButton(
                    onPressed: onPassShowClicked,
                    icon: SvgPicture.asset(
                      AppIcons.eye,
                      width: 24,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: AppDefaults.padding),
            SignUpButton(
              onPressed: isLoading ? null : onSignUp,
              isLoading: isLoading,
            ),
            const AlreadyHaveAnAccount(),
            const SizedBox(height: AppDefaults.padding),
          ],
        ),
      ),
    );
  }
}
