// Store-related data models matching Django API schema

class RegionData {
  final int id;
  final String name;
  final String type;
  final String slug;

  RegionData({
    required this.id,
    required this.name,
    required this.type,
    required this.slug,
  });

  factory RegionData.fromJson(Map<String, dynamic> json) {
    return RegionData(
      id: json['id'] as int,
      name: json['name'] as String,
      type: json['type'] as String,
      slug: json['slug'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'slug': slug,
    };
  }
}

class StoreOwnerData {
  final int id;
  final String username;
  final String email;
  final String firstName;
  final String lastName;
  final String phone;
  final bool phoneVerified;

  StoreOwnerData({
    required this.id,
    required this.username,
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.phone,
    required this.phoneVerified,
  });

  factory StoreOwnerData.fromJson(Map<String, dynamic> json) {
    return StoreOwnerData(
      id: json['id'] as int,
      username: json['username'] as String,
      email: json['email'] as String,
      firstName: json['first_name'] as String,
      lastName: json['last_name'] as String,
      phone: json['phone'] as String,
      phoneVerified: json['phone_verified'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'first_name': firstName,
      'last_name': lastName,
      'phone': phone,
      'phone_verified': phoneVerified,
    };
  }

  String get fullName => '$firstName $lastName'.trim();
}

class StoreData {
  final int id;
  final StoreOwnerData owner;
  final String name;
  final String description;
  final String address;
  final RegionData city;
  final RegionData state;
  final RegionData country;
  final DateTime createdAt;
  final DateTime updatedAt;

  StoreData({
    required this.id,
    required this.owner,
    required this.name,
    required this.description,
    required this.address,
    required this.city,
    required this.state,
    required this.country,
    required this.createdAt,
    required this.updatedAt,
  });

  factory StoreData.fromJson(Map<String, dynamic> json) {
    return StoreData(
      id: json['id'] as int,
      owner: StoreOwnerData.fromJson(json['owner'] as Map<String, dynamic>),
      name: json['name'] as String,
      description: json['description'] as String,
      address: json['address'] as String,
      city: RegionData.fromJson(json['city'] as Map<String, dynamic>),
      state: RegionData.fromJson(json['state'] as Map<String, dynamic>),
      country: RegionData.fromJson(json['country'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'owner': owner.toJson(),
      'name': name,
      'description': description,
      'address': address,
      'city': city.toJson(),
      'state': state.toJson(),
      'country': country.toJson(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get fullAddress =>
      '$address, ${city.name}, ${state.name}, ${country.name}';
}

class StoreCreateRequest {
  final String name;
  final String description;
  final String address;
  final int cityId;
  final int stateId;
  final int countryId;

  StoreCreateRequest({
    required this.name,
    required this.description,
    required this.address,
    required this.cityId,
    required this.stateId,
    required this.countryId,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'address': address,
      'city_id': cityId,
      'state_id': stateId,
      'country_id': countryId,
    };
  }
}

class StoreUpdateRequest {
  final String name;
  final String description;
  final String address;
  final int cityId;
  final int stateId;
  final int countryId;

  StoreUpdateRequest({
    required this.name,
    required this.description,
    required this.address,
    required this.cityId,
    required this.stateId,
    required this.countryId,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'address': address,
      'city_id': cityId,
      'state_id': stateId,
      'country_id': countryId,
    };
  }
}

class PaginatedStoreResponse {
  final int total;
  final List<StoreData> items;

  PaginatedStoreResponse({
    required this.total,
    required this.items,
  });

  factory PaginatedStoreResponse.fromJson(Map<String, dynamic> json) {
    return PaginatedStoreResponse(
      total: json['total'] as int,
      items: (json['items'] as List<dynamic>)
          .map((item) => StoreData.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total': total,
      'items': items.map((item) => item.toJson()).toList(),
    };
  }
}

class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final Map<String, dynamic>? errors;

  ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.errors,
  });

  factory ApiResponse.success(T data, {String? message}) {
    return ApiResponse(
      success: true,
      data: data,
      message: message,
    );
  }

  factory ApiResponse.error(String message, {Map<String, dynamic>? errors}) {
    return ApiResponse(
      success: false,
      message: message,
      errors: errors,
    );
  }
}
