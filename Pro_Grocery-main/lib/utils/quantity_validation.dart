/// Utility class for validating product quantities against minimum and maximum constraints
class QuantityValidation {
  /// Validation result for quantity checks
  static const String validationSuccess = 'success';
  
  /// Validate quantity against minimum and maximum constraints
  static QuantityValidationResult validateQuantity({
    required int quantity,
    required int minimumOrderQuantity,
    int? maximumOrderQuantity,
    String? productName,
  }) {
    // Check minimum quantity constraint
    if (quantity < minimumOrderQuantity) {
      return QuantityValidationResult(
        isValid: false,
        errorType: QuantityValidationError.belowMinimum,
        errorMessage: _generateMinimumErrorMessage(
          quantity: quantity,
          minimumOrderQuantity: minimumOrderQuantity,
          productName: productName,
        ),
        suggestedQuantity: minimumOrderQuantity,
      );
    }

    // Check maximum quantity constraint (if set)
    if (maximumOrderQuantity != null && quantity > maximumOrderQuantity) {
      return QuantityValidationResult(
        isValid: false,
        errorType: QuantityValidationError.aboveMaximum,
        errorMessage: _generateMaximumErrorMessage(
          quantity: quantity,
          maximumOrderQuantity: maximumOrderQuantity,
          productName: productName,
        ),
        suggestedQuantity: maximumOrderQuantity,
      );
    }

    // Quantity is valid
    return const QuantityValidationResult(
      isValid: true,
      errorType: null,
      errorMessage: null,
      suggestedQuantity: null,
    );
  }

  /// Validate if a quantity can be incremented
  static QuantityValidationResult validateIncrement({
    required int currentQuantity,
    required int minimumOrderQuantity,
    int? maximumOrderQuantity,
    String? productName,
    int incrementBy = 1,
  }) {
    final newQuantity = currentQuantity + incrementBy;
    return validateQuantity(
      quantity: newQuantity,
      minimumOrderQuantity: minimumOrderQuantity,
      maximumOrderQuantity: maximumOrderQuantity,
      productName: productName,
    );
  }

  /// Validate if a quantity can be decremented
  static QuantityValidationResult validateDecrement({
    required int currentQuantity,
    required int minimumOrderQuantity,
    int? maximumOrderQuantity,
    String? productName,
    int decrementBy = 1,
  }) {
    final newQuantity = currentQuantity - decrementBy;
    
    // Special case: if decrementing would go below minimum, suggest removal
    if (newQuantity < minimumOrderQuantity) {
      return QuantityValidationResult(
        isValid: false,
        errorType: QuantityValidationError.belowMinimum,
        errorMessage: _generateDecrementBelowMinimumMessage(
          minimumOrderQuantity: minimumOrderQuantity,
          productName: productName,
        ),
        suggestedQuantity: 0, // Suggest removal
      );
    }

    return validateQuantity(
      quantity: newQuantity,
      minimumOrderQuantity: minimumOrderQuantity,
      maximumOrderQuantity: maximumOrderQuantity,
      productName: productName,
    );
  }

  /// Get the valid quantity range as a string for display
  static String getQuantityRangeText({
    required int minimumOrderQuantity,
    int? maximumOrderQuantity,
  }) {
    if (maximumOrderQuantity != null) {
      return 'الكمية المسموحة: $minimumOrderQuantity - $maximumOrderQuantity';
    } else {
      return 'الحد الأدنى للكمية: $minimumOrderQuantity';
    }
  }

  /// Check if quantity controls should be disabled based on constraints
  static bool shouldDisableIncrement({
    required int currentQuantity,
    int? maximumOrderQuantity,
  }) {
    return maximumOrderQuantity != null && currentQuantity >= maximumOrderQuantity;
  }

  /// Check if decrement should be disabled
  static bool shouldDisableDecrement({
    required int currentQuantity,
    required int minimumOrderQuantity,
  }) {
    return currentQuantity <= minimumOrderQuantity;
  }

  /// Generate error message for quantity below minimum
  static String _generateMinimumErrorMessage({
    required int quantity,
    required int minimumOrderQuantity,
    String? productName,
  }) {
    final product = productName ?? 'هذا المنتج';
    return 'الكمية المطلوبة ($quantity) أقل من الحد الأدنى المطلوب لـ $product ($minimumOrderQuantity)';
  }

  /// Generate error message for quantity above maximum
  static String _generateMaximumErrorMessage({
    required int quantity,
    required int maximumOrderQuantity,
    String? productName,
  }) {
    final product = productName ?? 'هذا المنتج';
    return 'الكمية المطلوبة ($quantity) أكبر من الحد الأقصى المسموح لـ $product ($maximumOrderQuantity)';
  }

  /// Generate error message for decrement below minimum
  static String _generateDecrementBelowMinimumMessage({
    required int minimumOrderQuantity,
    String? productName,
  }) {
    final product = productName ?? 'هذا المنتج';
    return 'لا يمكن تقليل الكمية أقل من الحد الأدنى المطلوب لـ $product ($minimumOrderQuantity). يمكنك حذف المنتج من السلة بدلاً من ذلك.';
  }
}

/// Result of quantity validation
class QuantityValidationResult {
  final bool isValid;
  final QuantityValidationError? errorType;
  final String? errorMessage;
  final int? suggestedQuantity;

  const QuantityValidationResult({
    required this.isValid,
    required this.errorType,
    required this.errorMessage,
    required this.suggestedQuantity,
  });

  /// Check if the validation was successful
  bool get isSuccess => isValid;

  /// Check if the validation failed
  bool get isFailure => !isValid;
}

/// Types of quantity validation errors
enum QuantityValidationError {
  belowMinimum,
  aboveMaximum,
}

/// Extension to get Arabic descriptions for validation errors
extension QuantityValidationErrorExtension on QuantityValidationError {
  String get arabicDescription {
    switch (this) {
      case QuantityValidationError.belowMinimum:
        return 'أقل من الحد الأدنى';
      case QuantityValidationError.aboveMaximum:
        return 'أكبر من الحد الأقصى';
    }
  }
}
