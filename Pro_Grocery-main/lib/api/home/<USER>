import '../../core/models/dummy_product_model.dart';
import '../../core/models/dummy_bundle_model.dart';

// Company model for product details
class CompanyOut {
  final int id;
  final String name;
  final String? logoUrl;

  CompanyOut({
    required this.id,
    required this.name,
    this.logoUrl,
  });

  factory CompanyOut.fromJson(Map<String, dynamic> json) {
    return CompanyOut(
      id: json['id'],
      name: json['name'],
      logoUrl: json['logo_url'],
    );
  }
}

// Category model for product details
class CategoryOut {
  final int id;
  final String name;
  final String? iconUrl;

  CategoryOut({
    required this.id,
    required this.name,
    this.iconUrl,
  });

  factory CategoryOut.fromJson(Map<String, dynamic> json) {
    return CategoryOut(
      id: json['id'],
      name: json['name'],
      iconUrl: json['icon_url'],
    );
  }
}

// Main product model that matches the backend ProductWithPricing schema
class ProductWithPricing {
  final int id;
  final String name;
  final String title;
  final String barcode;
  final String slug;
  final String description;
  final String? imageUrl;
  final int? companyId;
  final int? categoryId;
  final CompanyOut? company;
  final CategoryOut? category;
  final String unit;
  final double unitCount;
  final double? basePrice; // lowest price
  final double? otherPrice; // highest price

  ProductWithPricing({
    required this.id,
    required this.name,
    required this.title,
    required this.barcode,
    required this.slug,
    required this.description,
    this.imageUrl,
    this.companyId,
    this.categoryId,
    this.company,
    this.category,
    required this.unit,
    required this.unitCount,
    this.basePrice,
    this.otherPrice,
  });

  factory ProductWithPricing.fromJson(Map<String, dynamic> json) {
    return ProductWithPricing(
      id: json['id'],
      name: json['name'],
      title: json['title'],
      barcode: json['barcode'],
      slug: json['slug'],
      description: json['description'],
      imageUrl: json['image_url'],
      companyId: json['company_id'],
      categoryId: json['category_id'],
      company:
          json['company'] != null ? CompanyOut.fromJson(json['company']) : null,
      category: json['category'] != null
          ? CategoryOut.fromJson(json['category'])
          : null,
      unit: json['unit'],
      unitCount: double.parse(json['unit_count'].toString()),
      basePrice: json['base_price'] != null
          ? double.parse(json['base_price'].toString())
          : null,
      otherPrice: json['other_price'] != null
          ? double.parse(json['other_price'].toString())
          : null,
    );
  }

  // Convert to ProductModel for ProductTileSquare component
  ProductModel toProductModel() {
    // Handle unit count formatting
    String formattedWeight;
    if (unitCount == 1.0) {
      formattedWeight = unit;
    } else if (unitCount == unitCount.toInt()) {
      // If it's a whole number, don't show decimal places
      formattedWeight = '${unitCount.toInt()} $unit';
    } else {
      formattedWeight = '${unitCount.toStringAsFixed(1)} $unit';
    }

    // Handle price formatting - ensure we have valid prices
    final displayPrice = basePrice?.toDouble() ?? 0.0;
    final displayMainPrice =
        otherPrice?.toDouble() ?? basePrice?.toDouble() ?? 0.0;

    return ProductModel(
      name:
          name.isNotEmpty ? name : title, // Fallback to title if name is empty
      weight: formattedWeight,
      cover: imageUrl ?? '', // Use empty string if no image
      images: imageUrl != null ? [imageUrl!] : [],
      price: displayPrice,
      mainPrice: displayMainPrice,
    );
  }

  // Convert to BundleModel for BundleTileSquare component
  BundleModel toBundleModel() {
    return BundleModel(
      name: name,
      cover: imageUrl ?? '', // Use empty string if no image
      itemNames: [title], // Use title as item name
      price: basePrice?.toDouble() ?? 0.0,
      mainPrice: otherPrice?.toDouble() ?? basePrice?.toDouble() ?? 0.0,
    );
  }
}

// Paginated response model that matches the backend
class PaginatedProductResponse {
  final List<ProductWithPricing> products;
  final int totalCount;
  final int page;
  final int pageSize;
  final int totalPages;
  final bool hasNext;
  final bool hasPrevious;

  PaginatedProductResponse({
    required this.products,
    required this.totalCount,
    required this.page,
    required this.pageSize,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrevious,
  });

  factory PaginatedProductResponse.fromJson(Map<String, dynamic> json) {
    return PaginatedProductResponse(
      products: (json['products'] as List)
          .map((product) => ProductWithPricing.fromJson(product))
          .toList(),
      totalCount: json['total_count'],
      page: json['page'],
      pageSize: json['page_size'],
      totalPages: json['total_pages'],
      hasNext: json['has_next'],
      hasPrevious: json['has_previous'],
    );
  }
}

// Filter parameters for the API request
class ProductFilters {
  final int? regionId;
  final int? wholesalerId;
  final String? search;
  final int? categoryId;
  final int? companyId;
  final int page;
  final int pageSize;

  ProductFilters({
    this.regionId,
    this.wholesalerId,
    this.search,
    this.categoryId,
    this.companyId,
    this.page = 1,
    this.pageSize = 20,
  });

  Map<String, dynamic> toQueryParams() {
    final params = <String, dynamic>{
      'page': page,
      'page_size': pageSize,
    };

    if (regionId != null) params['region_id'] = regionId;
    if (wholesalerId != null) params['wholesaler_id'] = wholesalerId;
    if (search != null && search!.isNotEmpty) params['search'] = search;
    if (categoryId != null) params['category_id'] = categoryId;
    if (companyId != null) params['company_id'] = companyId;

    return params;
  }
}
