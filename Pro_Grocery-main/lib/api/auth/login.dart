import 'package:dio/dio.dart';
import '../api.dart';

// Response Models
class LoginResponse {
  final bool success;
  final String? token;
  final int? userId;
  final String? phone;
  final bool? isPhoneVerified;
  final int? wholesalerId;
  final String? error;

  LoginResponse({
    required this.success,
    this.token,
    this.userId,
    this.phone,
    this.isPhoneVerified,
    this.wholesalerId,
    this.error,
  });

  factory LoginResponse.success({
    required String token,
    required int userId,
    required String phone,
    required bool isPhoneVerified,
    int? wholesalerId,
  }) {
    return LoginResponse(
      success: true,
      token: token,
      userId: userId,
      phone: phone,
      isPhoneVerified: isPhoneVerified,
      wholesalerId: wholesalerId,
    );
  }

  factory LoginResponse.error(String errorMessage) {
    return LoginResponse(
      success: false,
      error: errorMessage,
    );
  }

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      success: json['success'] ?? false,
      token: json['token'],
      userId: json['user_id'],
      phone: json['phone'],
      isPhoneVerified: json['is_phone_verified'],
      wholesalerId: json['wholesaler_id'],
      error: json['error'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      if (token != null) 'token': token,
      if (userId != null) 'user_id': userId,
      if (phone != null) 'phone': phone,
      if (isPhoneVerified != null) 'is_phone_verified': isPhoneVerified,
      if (wholesalerId != null) 'wholesaler_id': wholesalerId,
      if (error != null) 'error': error,
    };
  }

  // Helper methods
  bool get isWholesaler => wholesalerId != null;
  String get userType => isWholesaler ? 'wholesaler' : 'customer';
}

class LoginApi {
  static Future<LoginResponse> login({
    required String phone,
    required String password,
  }) async {
    try {
      final response = await HttpService.instance.post(
        '/api/v2/login',
        {
          'phone': phone,
          'password': password,
        },
      );

      // Create success response from API data
      return LoginResponse.fromJson(response);
    } on DioException catch (e) {
      String errorMessage = 'Login failed';

      if (e.response?.statusCode == 401) {
        errorMessage = 'Invalid phone number or password';
      } else if (e.response?.statusCode == 500) {
        errorMessage = 'Server error. Please try again later';
      } else if (e.response?.data != null &&
          e.response!.data['error'] != null) {
        errorMessage = e.response!.data['error'];
      }

      return LoginResponse.error(errorMessage);
    } catch (e) {
      return LoginResponse.error('Network error. Please check your connection');
    }
  }
}
