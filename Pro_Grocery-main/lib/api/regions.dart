import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'api.dart';

// Region model that matches the backend RegionWithHierarchyOut schema
class RegionModel {
  final int id;
  final String name;
  final String type;
  final String code;
  final int? parentId;
  final String hierarchicalName;

  RegionModel({
    required this.id,
    required this.name,
    required this.type,
    required this.code,
    this.parentId,
    required this.hierarchicalName,
  });

  factory RegionModel.fromJson(Map<String, dynamic> json) {
    return RegionModel(
      id: json['id'],
      name: json['name'],
      type: json['type'],
      code: json['code'],
      parentId: json['parent_id'],
      hierarchicalName: json['hierarchical_name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'code': code,
      'parent_id': parentId,
      'hierarchical_name': hierarchicalName,
    };
  }

  // Helper method to get display name
  String get displayName => hierarchicalName;

  // Helper method to check if this is a country
  bool get isCountry => type == 'COUNTRY';

  // Helper method to check if this is a state
  bool get isState => type == 'STATE';

  // Helper method to check if this is a district
  bool get isDistrict => type == 'DISTRICT';

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RegionModel &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() =>
      'RegionModel(id: $id, name: $name, hierarchicalName: $hierarchicalName)';
}

class RegionsApiService {
  static const String _baseEndpoint = '/api/v2/regions';

  /// Get all regions with hierarchical names
  static Future<List<RegionModel>> getAllRegions() async {
    try {
      final response = await HttpService.instance.get(_baseEndpoint);
      return (response as List)
          .map((region) => RegionModel.fromJson(region))
          .toList();
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) {
        rethrow;
      }
      throw RegionsApiException('Unexpected error occurred: ${e.toString()}');
    }
  }

  /// Get all countries
  static Future<List<RegionModel>> getCountries() async {
    try {
      final response =
          await HttpService.instance.get('$_baseEndpoint/countries');
      return (response as List)
          .map((region) => RegionModel.fromJson(region))
          .toList();
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) {
        rethrow;
      }
      throw RegionsApiException('Unexpected error occurred: ${e.toString()}');
    }
  }

  /// Get all states
  static Future<List<RegionModel>> getStates() async {
    try {
      final response = await HttpService.instance.get('$_baseEndpoint/states');
      return (response as List)
          .map((region) => RegionModel.fromJson(region))
          .toList();
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) {
        rethrow;
      }
      throw RegionsApiException('Unexpected error occurred: ${e.toString()}');
    }
  }

  /// Get all districts
  static Future<List<RegionModel>> getDistricts() async {
    try {
      final response =
          await HttpService.instance.get('$_baseEndpoint/districts');
      return (response as List)
          .map((region) => RegionModel.fromJson(region))
          .toList();
    } on DioException catch (e) {
      throw _handleApiError(e);
    } catch (e) {
      if (kDebugMode) {
        rethrow;
      }
      throw RegionsApiException('Unexpected error occurred: ${e.toString()}');
    }
  }

  /// Handle API errors and convert them to user-friendly messages
  static RegionsApiException _handleApiError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return RegionsApiException(
            'Connection timeout. Please check your internet connection.');

      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message =
            error.response?.data?['message'] ?? 'Server error occurred';

        switch (statusCode) {
          case 400:
            return RegionsApiException('Invalid request: $message');
          case 401:
            return RegionsApiException(
                'Authentication required. Please log in again.');
          case 403:
            return RegionsApiException('Access denied: $message');
          case 404:
            return RegionsApiException('Regions not found');
          case 500:
            return RegionsApiException('Server error. Please try again later.');
          default:
            return RegionsApiException('Error $statusCode: $message');
        }

      case DioExceptionType.cancel:
        return RegionsApiException('Request was cancelled');

      case DioExceptionType.connectionError:
        return RegionsApiException(
            'No internet connection. Please check your network.');

      default:
        return RegionsApiException('Network error: ${error.message}');
    }
  }
}

/// Custom exception for regions API errors
class RegionsApiException implements Exception {
  final String message;

  RegionsApiException(this.message);

  @override
  String toString() => 'RegionsApiException: $message';
}
