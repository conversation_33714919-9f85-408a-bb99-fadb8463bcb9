import 'api.dart';
import '../models/store_models.dart';

class StoresApiService {
  static const String _baseUrl = '/api/v2/stores';

  /// Get all stores for the authenticated user
  /// GET: /api/v2/stores/me
  static Future<ApiResponse<List<StoreData>>> getUserStores() async {
    try {
      final data = await HttpService.instance.get('$_baseUrl/me');

      final List<dynamic> listData = data as List<dynamic>;
      final stores = listData
          .map((json) => StoreData.fromJson(json as Map<String, dynamic>))
          .toList();

      return ApiResponse.success(stores,
          message: 'Stores retrieved successfully');
    } catch (e) {
      return ApiResponse.error('خطأ في الاتصال. يرجى المحاولة مرة أخرى');
    }
  }

  /// Get a single store by its ID
  /// GET: /api/v2/stores/{store_id}
  static Future<ApiResponse<StoreData>> getStoreById(int storeId) async {
    try {
      final data = await HttpService.instance.get('$_baseUrl/$storeId');

      final store = StoreData.fromJson(data as Map<String, dynamic>);
      return ApiResponse.success(store,
          message: 'Store retrieved successfully');
    } catch (e) {
      return ApiResponse.error('خطأ في الاتصال. يرجى المحاولة مرة أخرى');
    }
  }

  /// Create a new store
  /// POST: /api/v2/stores/
  static Future<ApiResponse<StoreData>> createStore(
      StoreCreateRequest request) async {
    try {
      final data = await HttpService.instance.post(
        '$_baseUrl/',
        request.toJson(),
      );

      final store = StoreData.fromJson(data as Map<String, dynamic>);
      return ApiResponse.success(store, message: 'تم إنشاء المتجر بنجاح');
    } catch (e) {
      if (e.toString().contains('400')) {
        return ApiResponse.error(
            'بيانات غير صحيحة. يرجى التحقق من البيانات المدخلة');
      } else if (e.toString().contains('401')) {
        return ApiResponse.error('يجب تسجيل الدخول أولاً');
      } else if (e.toString().contains('403')) {
        return ApiResponse.error('ليس لديك صلاحية لإنشاء متجر');
      } else {
        return ApiResponse.error('خطأ في الاتصال. يرجى المحاولة مرة أخرى');
      }
    }
  }

  /// Update an existing store
  /// PUT: /api/v2/stores/{store_id}
  static Future<ApiResponse<StoreData>> updateStore(
    int storeId,
    StoreUpdateRequest request,
  ) async {
    try {
      final data = await HttpService.instance.put(
        '$_baseUrl/$storeId',
        request.toJson(),
      );

      final store = StoreData.fromJson(data as Map<String, dynamic>);
      return ApiResponse.success(store, message: 'تم تحديث المتجر بنجاح');
    } catch (e) {
      if (e.toString().contains('400')) {
        return ApiResponse.error(
            'بيانات غير صحيحة. يرجى التحقق من البيانات المدخلة');
      } else if (e.toString().contains('401')) {
        return ApiResponse.error('يجب تسجيل الدخول أولاً');
      } else if (e.toString().contains('403')) {
        return ApiResponse.error('ليس لديك صلاحية لتحديث هذا المتجر');
      } else if (e.toString().contains('404')) {
        return ApiResponse.error('المتجر غير موجود');
      } else {
        return ApiResponse.error('خطأ في الاتصال. يرجى المحاولة مرة أخرى');
      }
    }
  }

  /// Delete a store
  /// DELETE: /api/v2/stores/{store_id}
  static Future<ApiResponse<bool>> deleteStore(int storeId) async {
    try {
      await HttpService.instance.delete('$_baseUrl/$storeId');

      return ApiResponse.success(true, message: 'تم حذف المتجر بنجاح');
    } catch (e) {
      if (e.toString().contains('401')) {
        return ApiResponse.error('يجب تسجيل الدخول أولاً');
      } else if (e.toString().contains('403')) {
        return ApiResponse.error('ليس لديك صلاحية لحذف هذا المتجر');
      } else if (e.toString().contains('404')) {
        return ApiResponse.error('المتجر غير موجود');
      } else {
        return ApiResponse.error('خطأ في الاتصال. يرجى المحاولة مرة أخرى');
      }
    }
  }

  /// Validate store data before submission
  static Map<String, String>? validateStoreData({
    required String name,
    required String description,
    required String address,
    required int? cityId,
    required int? stateId,
    required int? countryId,
  }) {
    Map<String, String> errors = {};

    if (name.trim().isEmpty) {
      errors['name'] = 'اسم المتجر مطلوب';
    } else if (name.trim().length < 2) {
      errors['name'] = 'اسم المتجر يجب أن يكون حرفين على الأقل';
    }

    if (description.trim().isEmpty) {
      errors['description'] = 'وصف المتجر مطلوب';
    } else if (description.trim().length < 10) {
      errors['description'] = 'وصف المتجر يجب أن يكون 10 أحرف على الأقل';
    }

    if (address.trim().isEmpty) {
      errors['address'] = 'عنوان المتجر مطلوب';
    } else if (address.trim().length < 10) {
      errors['address'] = 'عنوان المتجر يجب أن يكون 10 أحرف على الأقل';
    }

    if (cityId == null) {
      errors['city'] = 'اختيار المدينة مطلوب';
    }

    if (stateId == null) {
      errors['state'] = 'اختيار المحافظة مطلوب';
    }

    if (countryId == null) {
      errors['country'] = 'اختيار الدولة مطلوب';
    }

    return errors.isEmpty ? null : errors;
  }
}
