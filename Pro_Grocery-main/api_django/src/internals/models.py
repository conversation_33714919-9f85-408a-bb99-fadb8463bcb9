from django.db import models

# Create your models here.


class Banner(models.Model):
    title = models.CharField(max_length=255)
    image = models.ImageField(upload_to="banners")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    expire_date = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    regions = models.ManyToManyField(
        "products.Region", blank=True, related_name="banners"
    )

    def __str__(self):
        return self.title

    class Meta:
        verbose_name = "Banner"
        verbose_name_plural = "Banners"
        ordering = ["-created_at"]
