from ninja import Router
from ninja.orm import create_schema
from .models import Banner
from typing import List
from django.utils import timezone

router = Router(tags=["index"])

BannerSchema = create_schema(Banner)


@router.get("/banners", response=List[BannerSchema])
def get_banners(request, region_id: int):
    banners = Banner.objects.filter(expire_date__gte=timezone.now())
    banners = banners.filter(is_active=True)
    banners = banners.filter(regions__id=region_id).all()
    return [BannerSchema.from_orm(banner) for banner in banners]
