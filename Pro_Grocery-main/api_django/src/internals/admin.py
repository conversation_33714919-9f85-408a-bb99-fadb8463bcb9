from django.contrib import admin
from .models import Banner


class BannerAdmin(admin.ModelAdmin):
    list_display = ("title", "created_at", "updated_at", "expire_date")
    list_filter = ("created_at", "updated_at", "expire_date")
    search_fields = ("title",)
    readonly_fields = ("created_at", "updated_at")
    filter_horizontal = ("regions",)

    fieldsets = (
        (
            None,
            {
                "fields": (
                    "title",
                    "image",
                    "expire_date",
                    "is_active",
                ),
            },
        ),
        (
            "Targeting",
            {
                "fields": ("regions",),
                "classes": ("collapse",),
                "description": "Select regions where this banner should be displayed",
            },
        ),
        (
            "Timestamps",
            {
                "fields": (
                    "created_at",
                    "updated_at",
                ),
                "classes": ("collapse",),
            },
        ),
    )


admin.site.register(Banner, BannerAdmin)
