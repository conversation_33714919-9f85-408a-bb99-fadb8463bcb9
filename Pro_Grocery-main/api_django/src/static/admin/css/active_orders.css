/* Active Orders Admin - Premium Modern UI */

/* CSS Variables for consistent theming */
:root {
    --primary-color: #6366f1;
    --primary-hover: #4f46e5;
    --secondary-color: #f1f5f9;
    --accent-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --dark-bg: #0f172a;
    --card-bg: #1e293b;
    --border-color: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #94a3b8;
    --text-muted: #64748b;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--dark-bg);
    color: var(--text-primary);
    line-height: 1.6;
}

.admin-active-orders {
    min-height: 100vh;
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    padding: 2rem;
}

/* Header Section */
.admin-active-orders h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 3rem 0;
    text-align: center;
    position: relative;
    padding-bottom: 1rem;
}

.admin-active-orders h1::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 2px;
}

/* Messages */
.messages {
    list-style: none;
    padding: 0;
    margin: 0 0 2rem 0;
}

.messages li {
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    border-radius: var(--radius-lg);
    font-weight: 500;
    border-left: 4px solid;
    backdrop-filter: blur(10px);
    animation: slideInDown 0.3s ease-out;
}

.messages .success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--accent-color);
    border-left-color: var(--accent-color);
}

.messages .error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    border-left-color: var(--danger-color);
}

.messages .warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border-left-color: var(--warning-color);
}

/* Summary Stats */
.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: var(--card-bg);
    border-radius: var(--radius-xl);
    padding: 2rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Filters Section */
.filters-section {
    background: var(--card-bg);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    margin-bottom: 3rem;
    overflow: hidden;
}

.filters-section::before {
    content: '';
    display: block;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.filters-row {
    padding: 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.filter-group select,
.filter-group input[type="text"],
.filter-group input[type="date"] {
    padding: 0.875rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    background: var(--dark-bg);
    color: var(--text-primary);
    transition: all 0.3s ease;
    font-family: inherit;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    transform: translateY(-1px);
}

.filter-buttons {
    display: flex;
    gap: 1rem;
    align-items: end;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.875rem 1.5rem;
    border: 2px solid transparent;
    border-radius: var(--radius-lg);
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    min-width: 120px;
    justify-content: center;
}

.filter-btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.filter-btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.filter-btn-secondary {
    background: transparent;
    color: var(--text-secondary);
    border-color: var(--border-color);
}

.filter-btn-secondary:hover {
    background: var(--border-color);
    color: var(--text-primary);
    transform: translateY(-2px);
}

/* Utility Actions */
.utility-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.utility-btn {
    padding: 0.75rem 1.25rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--card-bg);
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.utility-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
}

/* Results Table */
.results {
    background: var(--card-bg);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    margin-bottom: 2rem;
}

.results table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.results th {
    background: var(--dark-bg);
    padding: 1.5rem 1.25rem;
    text-align: left;
    font-weight: 700;
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 2px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 10;
}

.results td {
    padding: 1.5rem 1.25rem;
    border-bottom: 1px solid var(--border-color);
    vertical-align: top;
    color: var(--text-primary);
}

.results tr:hover {
    background: rgba(99, 102, 241, 0.05);
}

.results tr:last-child td {
    border-bottom: none;
}

/* Order ID Styling */
.results td:first-child a {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-weight: 700;
    font-size: 0.875rem;
    display: inline-block;
    transition: all 0.3s ease;
}

.results td:first-child a:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
    color: white;
}

/* Info Sections */
.store-info,
.wholesaler-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.store-info strong,
.wholesaler-info strong {
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
}

.store-info small,
.wholesaler-info small {
    color: var(--text-secondary);
    font-size: 0.875rem;
    background: rgba(99, 102, 241, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
    display: inline-block;
    margin-top: 0.25rem;
}

/* Phone Numbers */
.phone-numbers {
    background: rgba(16, 185, 129, 0.05);
    padding: 1rem;
    border-radius: var(--radius-lg);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.phone-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.phone-item:last-child {
    margin-bottom: 0;
}

.phone-item strong {
    font-size: 0.75rem;
    color: var(--text-secondary);
    min-width: 80px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.phone-number {
    font-family: 'JetBrains Mono', 'Fira Code', monospace;
    background: var(--dark-bg);
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    flex: 1;
    min-width: 120px;
}

.copy-btn {
    background: var(--accent-color);
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.copy-btn:hover {
    background: #059669;
    transform: scale(1.1);
}

.copy-btn:active {
    transform: scale(0.95);
}

/* Order Details */
.order-details {
    background: rgba(99, 102, 241, 0.05);
    padding: 1rem;
    border-radius: var(--radius-lg);
    border: 1px solid rgba(99, 102, 241, 0.2);
}

.order-details>div {
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.order-details>div:last-child {
    margin-bottom: 0;
}

.order-details strong {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.price {
    color: var(--accent-color);
    font-weight: 700;
    font-size: 1rem;
}

/* Status Badges */
.status-badge {
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-lg);
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: inline-block;
}

.status-pending {
    background: rgba(245, 158, 11, 0.2);
    color: var(--warning-color);
    border: 1px solid var(--warning-color);
}

.status-processing {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    border: 1px solid #3b82f6;
}

.status-shipped {
    background: rgba(139, 69, 193, 0.2);
    color: #8b45c1;
    border: 1px solid #8b45c1;
}

/* Actions */
.actions {
    display: flex;
    gap: 0.5rem;
    flex-direction: column;
}

.actions a {
    background: transparent;
    color: var(--primary-color);
    padding: 0.5rem 1rem;
    border: 1px solid var(--primary-color);
    border-radius: var(--radius-md);
    text-decoration: none;
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.actions a:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    margin: 2rem 0;
    background: var(--card-bg);
    padding: 1.5rem;
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
}

.pagination .step-links {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.pagination a,
.pagination .current {
    padding: 0.75rem 1rem;
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    min-width: 44px;
    text-align: center;
}

.pagination a {
    background: var(--dark-bg);
    color: var(--text-secondary);
    border-color: var(--border-color);
}

.pagination a:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.pagination .current {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* No Results */
.no-results {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-secondary);
    background: var(--card-bg);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.no-results p {
    font-size: 1.125rem;
    margin-bottom: 1rem;
}

.no-results a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border: 2px solid var(--primary-color);
    border-radius: var(--radius-lg);
    display: inline-block;
    transition: all 0.3s ease;
}

.no-results a:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

/* Animations */
@keyframes slideInDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.copy-success {
    animation: copySuccess 0.6s ease-in-out;
}

@keyframes copySuccess {
    0% {
        background: var(--accent-color);
        transform: scale(1);
    }

    50% {
        background: #059669;
        transform: scale(1.2);
    }

    100% {
        background: var(--accent-color);
        transform: scale(1);
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .admin-active-orders {
        padding: 1.5rem;
    }

    .filters-row {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .admin-active-orders {
        padding: 1rem;
    }

    .admin-active-orders h1 {
        font-size: 2rem;
        margin-bottom: 2rem;
    }

    .summary-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .filters-row {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1.5rem;
    }

    .filter-buttons {
        justify-content: stretch;
    }

    .filter-btn {
        flex: 1;
    }

    .utility-actions {
        flex-direction: column;
    }

    .utility-btn {
        width: 100%;
        justify-content: center;
    }

    /* Enhanced mobile table scrolling */
    .results {
        border-radius: var(--radius-lg);
        overflow-x: auto;
        overflow-y: visible;
        -webkit-overflow-scrolling: touch;
        /* Add scroll indicator */
        background: var(--card-bg);
        position: relative;
    }

    .results::after {
        content: '← Scroll to view more columns →';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
        color: white;
        text-align: center;
        padding: 0.5rem;
        font-size: 0.75rem;
        font-weight: 500;
        z-index: 5;
        opacity: 0.8;
    }

    .results table {
        min-width: 1000px;
        /* Increased from 800px for better visibility */
        width: max-content;
    }

    .results th,
    .results td {
        padding: 1rem 0.75rem;
        white-space: nowrap;
        min-width: 150px;
        /* Ensure minimum column width */
    }

    /* Specific column widths for better mobile experience */
    .results th:first-child,
    .results td:first-child {
        min-width: 100px;
        position: sticky;
        left: 0;
        background: var(--card-bg);
        z-index: 2;
        box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    }

    .results th:nth-child(2),
    .results td:nth-child(2) {
        min-width: 200px;
    }

    .results th:nth-child(3),
    .results td:nth-child(3) {
        min-width: 200px;
    }

    .results th:nth-child(4),
    .results td:nth-child(4) {
        min-width: 250px;
    }

    .results th:nth-child(5),
    .results td:nth-child(5) {
        min-width: 250px;
    }

    .results th:nth-child(6),
    .results td:nth-child(6) {
        min-width: 150px;
    }

    .phone-item {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .phone-item strong {
        min-width: auto;
    }

    .actions {
        flex-direction: row;
        gap: 0.25rem;
    }

    .actions a {
        flex: 1;
        font-size: 0.6875rem;
        padding: 0.375rem 0.5rem;
        white-space: nowrap;
    }

    /* Improve info sections for mobile */
    .store-info,
    .wholesaler-info {
        max-width: 180px;
    }

    .store-info small,
    .wholesaler-info small {
        font-size: 0.75rem;
        padding: 0.125rem 0.375rem;
    }

    .phone-numbers {
        padding: 0.75rem;
    }

    .order-details {
        padding: 0.75rem;
        max-width: 230px;
    }

    .order-details>div {
        font-size: 0.75rem;
        margin-bottom: 0.375rem;
    }
}

@media (max-width: 480px) {
    .admin-active-orders {
        padding: 0.75rem;
    }

    .admin-active-orders h1 {
        font-size: 1.75rem;
    }

    .stat-card {
        padding: 1.25rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .filters-row {
        padding: 1.25rem;
    }

    .pagination .step-links {
        justify-content: center;
    }

    .pagination a,
    .pagination .current {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
    }

    /* Enhanced mobile table for very small screens */
    .results table {
        min-width: 900px;
        /* Slightly smaller for very small screens */
    }

    .results th,
    .results td {
        padding: 0.75rem 0.5rem;
        font-size: 0.75rem;
    }

    /* Adjust column widths for very small screens */
    .results th:first-child,
    .results td:first-child {
        min-width: 80px;
    }

    .results th:nth-child(2),
    .results td:nth-child(2) {
        min-width: 180px;
    }

    .results th:nth-child(3),
    .results td:nth-child(3) {
        min-width: 180px;
    }

    .results th:nth-child(4),
    .results td:nth-child(4) {
        min-width: 220px;
    }

    .results th:nth-child(5),
    .results td:nth-child(5) {
        min-width: 220px;
    }

    .results th:nth-child(6),
    .results td:nth-child(6) {
        min-width: 120px;
    }

    .store-info,
    .wholesaler-info {
        max-width: 160px;
    }

    .phone-numbers {
        padding: 0.5rem;
    }

    .order-details {
        padding: 0.5rem;
        max-width: 200px;
    }

    .actions a {
        font-size: 0.625rem;
        padding: 0.25rem 0.375rem;
    }
}

/* Dark scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--dark-bg);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Enhanced mobile scrollbar */
@media (max-width: 768px) {
    ::-webkit-scrollbar {
        width: 12px;
        height: 12px;
    }

    ::-webkit-scrollbar-thumb {
        background: var(--primary-color);
        border-radius: 6px;
        border: 2px solid var(--card-bg);
    }

    ::-webkit-scrollbar-thumb:hover {
        background: var(--primary-hover);
    }

    /* For Firefox */
    .results {
        scrollbar-width: auto;
        scrollbar-color: var(--primary-color) var(--card-bg);
    }
}

/* Print styles */
@media print {
    .admin-active-orders {
        background: white;
        color: black;
        padding: 1rem;
    }

    .utility-actions,
    .filters-section,
    .pagination {
        display: none;
    }

    .copy-btn {
        display: none;
    }
}

/* Focus styles for accessibility */
button:focus,
input:focus,
select:focus,
a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}