"""
CRUD API endpoints for CustomUser model.
"""

from ninja import Router, Schema
from ninja.errors import HttpError
from typing import Optional, List
from datetime import datetime
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
import logging

from accounts.models import CustomUser
from api.middleware import AuthMiddleware

logger = logging.getLogger(__name__)

# Create router for CustomUser endpoints
router = Router(tags=["users"])

# ============================================================================
# SCHEMAS
# ============================================================================

class CustomUserIn(Schema):
    """Schema for creating a new user"""
    username: str
    email: Optional[str] = None
    phone: str
    password: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None


class CustomUserUpdate(Schema):
    """Schema for updating user data"""
    username: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    is_active: Optional[bool] = None


class CustomUserOut(Schema):
    """Schema for user output"""
    id: int
    username: str
    email: Optional[str] = None
    phone: str
    phone_verified: bool
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    is_active: bool
    is_staff: bool
    is_superuser: bool
    date_joined: datetime
    last_login: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime


class PaginatedCustomUserResponse(Schema):
    """Paginated response for users"""
    users: List[CustomUserOut]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool


# ============================================================================
# CRUD ENDPOINTS
# ============================================================================

@router.get("/", response=PaginatedCustomUserResponse, auth=AuthMiddleware)
def list_users(
    request,
    page: int = 1,
    page_size: int = 20,
    search: Optional[str] = None,
    is_active: Optional[bool] = None,
    phone_verified: Optional[bool] = None,
) -> PaginatedCustomUserResponse:
    """
    List all users with pagination and filtering.
    Requires authentication.
    """
    try:
        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Build queryset
        queryset = CustomUser.objects.filter(deleted_at__isnull=True)

        # Apply filters
        if search:
            queryset = queryset.filter(
                Q(username__icontains=search) |
                Q(email__icontains=search) |
                Q(phone__icontains=search) |
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search)
            )

        if is_active is not None:
            queryset = queryset.filter(is_active=is_active)

        if phone_verified is not None:
            queryset = queryset.filter(phone_verified=phone_verified)

        # Order by creation date
        queryset = queryset.order_by('-date_joined')

        # Paginate
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Convert to response format
        users = []
        for user in page_obj.object_list:
            users.append(CustomUserOut(
                id=user.id,
                username=user.username,
                email=user.email,
                phone=user.phone,
                phone_verified=user.phone_verified,
                first_name=user.first_name,
                last_name=user.last_name,
                is_active=user.is_active,
                is_staff=user.is_staff,
                is_superuser=user.is_superuser,
                date_joined=user.date_joined,
                last_login=user.last_login,
                created_at=user.created_at,
                updated_at=user.updated_at,
            ))

        return PaginatedCustomUserResponse(
            users=users,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except Exception as e:
        logger.exception(f"Error listing users: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/{user_id}", response=CustomUserOut, auth=AuthMiddleware)
def get_user(request, user_id: int) -> CustomUserOut:
    """
    Get a specific user by ID.
    Requires authentication.
    """
    try:
        user = get_object_or_404(
            CustomUser,
            id=user_id,
            deleted_at__isnull=True
        )

        return CustomUserOut(
            id=user.id,
            username=user.username,
            email=user.email,
            phone=user.phone,
            phone_verified=user.phone_verified,
            first_name=user.first_name,
            last_name=user.last_name,
            is_active=user.is_active,
            is_staff=user.is_staff,
            is_superuser=user.is_superuser,
            date_joined=user.date_joined,
            last_login=user.last_login,
            created_at=user.created_at,
            updated_at=user.updated_at,
        )

    except Exception as e:
        logger.exception(f"Error getting user {user_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.post("/", response=CustomUserOut, auth=AuthMiddleware)
def create_user(request, data: CustomUserIn) -> CustomUserOut:
    """
    Create a new user.
    Requires authentication.
    """
    try:
        # Check if username already exists
        if CustomUser.objects.filter(username=data.username).exists():
            raise HttpError(400, "Username already exists")

        # Check if phone already exists
        if CustomUser.objects.filter(phone=data.phone).exists():
            raise HttpError(400, "Phone number already exists")

        # Check if email already exists (if provided)
        if data.email and CustomUser.objects.filter(email=data.email).exists():
            raise HttpError(400, "Email already exists")

        # Create user
        user = CustomUser.objects.create_user(
            username=data.username,
            email=data.email or "",
            phone=data.phone,
            password=data.password,
            first_name=data.first_name or "",
            last_name=data.last_name or "",
            phone_verified=False,
        )

        return CustomUserOut(
            id=user.id,
            username=user.username,
            email=user.email,
            phone=user.phone,
            phone_verified=user.phone_verified,
            first_name=user.first_name,
            last_name=user.last_name,
            is_active=user.is_active,
            is_staff=user.is_staff,
            is_superuser=user.is_superuser,
            date_joined=user.date_joined,
            last_login=user.last_login,
            created_at=user.created_at,
            updated_at=user.updated_at,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error creating user: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.put("/{user_id}", response=CustomUserOut, auth=AuthMiddleware)
def update_user(request, user_id: int, data: CustomUserUpdate) -> CustomUserOut:
    """
    Update a user.
    Requires authentication.
    """
    try:
        user = get_object_or_404(
            CustomUser,
            id=user_id,
            deleted_at__isnull=True
        )

        # Update fields if provided
        if data.username is not None:
            if CustomUser.objects.filter(username=data.username).exclude(id=user_id).exists():
                raise HttpError(400, "Username already exists")
            user.username = data.username

        if data.email is not None:
            if data.email and CustomUser.objects.filter(email=data.email).exclude(id=user_id).exists():
                raise HttpError(400, "Email already exists")
            user.email = data.email

        if data.phone is not None:
            if CustomUser.objects.filter(phone=data.phone).exclude(id=user_id).exists():
                raise HttpError(400, "Phone number already exists")
            user.phone = data.phone
            user.phone_verified = False  # Reset verification when phone changes

        if data.first_name is not None:
            user.first_name = data.first_name

        if data.last_name is not None:
            user.last_name = data.last_name

        if data.is_active is not None:
            user.is_active = data.is_active

        user.save()

        return CustomUserOut(
            id=user.id,
            username=user.username,
            email=user.email,
            phone=user.phone,
            phone_verified=user.phone_verified,
            first_name=user.first_name,
            last_name=user.last_name,
            is_active=user.is_active,
            is_staff=user.is_staff,
            is_superuser=user.is_superuser,
            date_joined=user.date_joined,
            last_login=user.last_login,
            created_at=user.created_at,
            updated_at=user.updated_at,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error updating user {user_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.delete("/{user_id}", auth=AuthMiddleware)
def delete_user(request, user_id: int):
    """
    Soft delete a user.
    Requires authentication.
    """
    try:
        user = get_object_or_404(
            CustomUser,
            id=user_id,
            deleted_at__isnull=True
        )

        # Perform soft delete
        user.delete()

        return {"message": "User deleted successfully"}

    except Exception as e:
        logger.exception(f"Error deleting user {user_id}: {str(e)}")
        raise HttpError(500, "Internal server error")
