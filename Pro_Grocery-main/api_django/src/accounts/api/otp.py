"""
CRUD API endpoints for OTP model.
"""

from ninja import Router, Schema
from ninja.errors import HttpError
from typing import Optional, List
from datetime import datetime
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
import logging

from accounts.models import OTP, CustomUser
from api.middleware import AuthMiddleware

logger = logging.getLogger(__name__)

# Create router for OTP endpoints
router = Router(tags=["otps"])

# ============================================================================
# SCHEMAS
# ============================================================================

class OTPIn(Schema):
    """Schema for creating a new OTP"""
    user_id: int
    code: str
    expires_at: datetime


class OTPUpdate(Schema):
    """Schema for updating OTP data"""
    code: Optional[str] = None
    is_used: Optional[bool] = None
    expires_at: Optional[datetime] = None


class CustomUserOut(Schema):
    """Simplified user schema for OTP responses"""
    id: int
    username: str
    phone: str


class OTPOut(Schema):
    """Schema for OTP output"""
    id: int
    user: CustomUserOut
    code: str
    is_used: bool
    is_valid: bool
    created_at: datetime
    expires_at: datetime


class PaginatedOTPResponse(Schema):
    """Paginated response for OTPs"""
    otps: List[OTPOut]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool


class OTPGenerateRequest(Schema):
    """Schema for generating OTP for a user"""
    user_id: int


# ============================================================================
# CRUD ENDPOINTS
# ============================================================================

@router.get("/", response=PaginatedOTPResponse, auth=AuthMiddleware)
def list_otps(
    request,
    page: int = 1,
    page_size: int = 20,
    user_id: Optional[int] = None,
    is_used: Optional[bool] = None,
    is_valid: Optional[bool] = None,
) -> PaginatedOTPResponse:
    """
    List all OTPs with pagination and filtering.
    Requires authentication.
    """
    try:
        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Build queryset
        queryset = OTP.objects.select_related('user')

        # Apply filters
        if user_id:
            queryset = queryset.filter(user_id=user_id)

        if is_used is not None:
            queryset = queryset.filter(is_used=is_used)

        if is_valid is not None:
            if is_valid:
                # Valid OTPs are not used and not expired
                queryset = queryset.filter(
                    is_used=False,
                    expires_at__gt=timezone.now()
                )
            else:
                # Invalid OTPs are either used or expired
                queryset = queryset.filter(
                    Q(is_used=True) | Q(expires_at__lte=timezone.now())
                )

        # Order by creation date (newest first)
        queryset = queryset.order_by('-created_at')

        # Paginate
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Convert to response format
        otps = []
        for otp in page_obj.object_list:
            otps.append(OTPOut(
                id=otp.id,
                user=CustomUserOut(
                    id=otp.user.id,
                    username=otp.user.username,
                    phone=otp.user.phone,
                ),
                code=otp.code,
                is_used=otp.is_used,
                is_valid=otp.is_valid(),
                created_at=otp.created_at,
                expires_at=otp.expires_at,
            ))

        return PaginatedOTPResponse(
            otps=otps,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except Exception as e:
        logger.exception(f"Error listing OTPs: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/{otp_id}", response=OTPOut, auth=AuthMiddleware)
def get_otp(request, otp_id: int) -> OTPOut:
    """
    Get a specific OTP by ID.
    Requires authentication.
    """
    try:
        otp = get_object_or_404(
            OTP.objects.select_related('user'),
            id=otp_id
        )

        return OTPOut(
            id=otp.id,
            user=CustomUserOut(
                id=otp.user.id,
                username=otp.user.username,
                phone=otp.user.phone,
            ),
            code=otp.code,
            is_used=otp.is_used,
            is_valid=otp.is_valid(),
            created_at=otp.created_at,
            expires_at=otp.expires_at,
        )

    except Exception as e:
        logger.exception(f"Error getting OTP {otp_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.post("/generate", response=OTPOut, auth=AuthMiddleware)
def generate_otp(request, data: OTPGenerateRequest) -> OTPOut:
    """
    Generate a new OTP for a user.
    Requires authentication.
    """
    try:
        # Check if user exists
        user = get_object_or_404(
            CustomUser,
            id=data.user_id,
            deleted_at__isnull=True
        )

        # Generate OTP using the model's class method
        otp = OTP.generate_otp(user)

        return OTPOut(
            id=otp.id,
            user=CustomUserOut(
                id=otp.user.id,
                username=otp.user.username,
                phone=otp.user.phone,
            ),
            code=otp.code,
            is_used=otp.is_used,
            is_valid=otp.is_valid(),
            created_at=otp.created_at,
            expires_at=otp.expires_at,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error generating OTP: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.post("/", response=OTPOut, auth=AuthMiddleware)
def create_otp(request, data: OTPIn) -> OTPOut:
    """
    Create a new OTP manually.
    Requires authentication.
    """
    try:
        # Check if user exists
        user = get_object_or_404(
            CustomUser,
            id=data.user_id,
            deleted_at__isnull=True
        )

        # Validate expiration time
        if data.expires_at <= timezone.now():
            raise HttpError(400, "Expiration time must be in the future")

        # Create OTP
        otp = OTP.objects.create(
            user=user,
            code=data.code,
            expires_at=data.expires_at,
        )

        return OTPOut(
            id=otp.id,
            user=CustomUserOut(
                id=otp.user.id,
                username=otp.user.username,
                phone=otp.user.phone,
            ),
            code=otp.code,
            is_used=otp.is_used,
            is_valid=otp.is_valid(),
            created_at=otp.created_at,
            expires_at=otp.expires_at,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error creating OTP: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.put("/{otp_id}", response=OTPOut, auth=AuthMiddleware)
def update_otp(request, otp_id: int, data: OTPUpdate) -> OTPOut:
    """
    Update an OTP.
    Requires authentication.
    """
    try:
        otp = get_object_or_404(
            OTP.objects.select_related('user'),
            id=otp_id
        )

        # Update fields if provided
        if data.code is not None:
            otp.code = data.code

        if data.is_used is not None:
            otp.is_used = data.is_used

        if data.expires_at is not None:
            if data.expires_at <= timezone.now():
                raise HttpError(400, "Expiration time must be in the future")
            otp.expires_at = data.expires_at

        otp.save()

        return OTPOut(
            id=otp.id,
            user=CustomUserOut(
                id=otp.user.id,
                username=otp.user.username,
                phone=otp.user.phone,
            ),
            code=otp.code,
            is_used=otp.is_used,
            is_valid=otp.is_valid(),
            created_at=otp.created_at,
            expires_at=otp.expires_at,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error updating OTP {otp_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.post("/{otp_id}/verify", auth=AuthMiddleware)
def verify_otp(request, otp_id: int, code: str):
    """
    Verify an OTP code.
    Requires authentication.
    """
    try:
        otp = get_object_or_404(OTP, id=otp_id)

        # Check if OTP is valid and matches the code
        if not otp.is_valid():
            raise HttpError(400, "OTP is expired or already used")

        if otp.code != code:
            raise HttpError(400, "Invalid OTP code")

        # Mark OTP as used
        otp.is_used = True
        otp.save()

        return {"message": "OTP verified successfully", "valid": True}

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error verifying OTP {otp_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.delete("/{otp_id}", auth=AuthMiddleware)
def delete_otp(request, otp_id: int):
    """
    Delete an OTP.
    Requires authentication.
    """
    try:
        otp = get_object_or_404(OTP, id=otp_id)
        otp.delete()

        return {"message": "OTP deleted successfully"}

    except Exception as e:
        logger.exception(f"Error deleting OTP {otp_id}: {str(e)}")
        raise HttpError(500, "Internal server error")
