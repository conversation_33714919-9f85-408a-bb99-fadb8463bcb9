from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import CustomUser, AccountVerificationId, Wallet


# CustomUser Admin configuration
class CustomUserAdmin(UserAdmin):
    list_display = (
        "username",
        "email",
        "phone",
        "phone_verified",
        "is_staff",
        "is_active",
    )
    list_filter = ("is_staff", "is_active", "phone_verified", "created_at")
    search_fields = ("username", "email", "phone")
    fieldsets = UserAdmin.fieldsets + (
        (
            "Custom User Fields",
            {
                "fields": (
                    "phone",
                    "phone_verified",
                    "fcm_token",
                    "deleted_at",
                )
            },
        ),
    )
    add_fieldsets = UserAdmin.add_fieldsets + (
        ("Custom User Fields", {"fields": ("phone",)}),
    )
    readonly_fields = ("created_at", "updated_at", "fcm_token")


# AccountVerificationId Admin configuration
class AccountVerificationIdAdmin(admin.ModelAdmin):
    list_display = ("user", "verified", "created_at", "updated_at")
    list_filter = ("verified", "created_at")
    search_fields = ("user__username", "user__email", "user__phone")
    raw_id_fields = ("user",)
    readonly_fields = ("created_at", "updated_at")


# Wallet Admin configuration
class WalletAdmin(admin.ModelAdmin):
    list_display = ("user", "balance", "created_at", "updated_at")
    search_fields = ("user__username", "user__email", "user__phone")
    raw_id_fields = ("user",)
    readonly_fields = ("created_at", "updated_at")


# Register all models with admin site
admin.site.register(CustomUser, CustomUserAdmin)
admin.site.register(AccountVerificationId, AccountVerificationIdAdmin)
admin.site.register(Wallet, WalletAdmin)
