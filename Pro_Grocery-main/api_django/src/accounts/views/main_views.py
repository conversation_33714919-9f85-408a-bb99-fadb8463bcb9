import logging
from accounts.models import CustomUser
from accounts.middleware import AuthMiddleware
from core.custom_router import CustomRouter
from pydantic import BaseModel

router = CustomRouter(tags=["accounts"])

logger = logging.getLogger(__name__)


class SuccessResponse(BaseModel):
    message: str


class ErrorResponse(BaseModel):
    error: str
    error_code: str


class UpdateFCMTokenRequest(BaseModel):
    fcm_token: str


@router.post(
    "/fcm-token",
    response={200: SuccessResponse, 500: ErrorResponse},
    auth=AuthMiddleware,
)
async def update_fcm_token(request, data: UpdateFCMTokenRequest):
    try:
        user: CustomUser = request.auth
        user.fcm_token = data.fcm_token
        await user.asave()
        return 200, SuccessResponse(message="FCM token updated successfully")
    except Exception as e:
        logger.exception(f"Unexpected error updating FCM token: {str(e)}")
        return 500, ErrorResponse(
            error="Internal server error", error_code="SERVER_ERROR"
        )
