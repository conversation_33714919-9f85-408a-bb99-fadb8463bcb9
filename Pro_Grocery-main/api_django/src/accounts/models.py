import random
from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils import timezone

from core.utils import RandomFileName


class UserProfile(models.Model):
    phone = models.CharField(max_length=50)
    phone_verified = models.BooleanField(default=False)
    fcm_token = models.CharField(max_length=255, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        abstract = True

    def delete(self, *args, **kwargs):
        self.deleted_at = timezone.now()
        self.save()

    def hard_delete(self, *args, **kwargs):
        super(UserProfile, self).delete(*args, **kwargs)


class CustomUser(AbstractUser, UserProfile):
    class Meta:
        verbose_name = "Custom User"
        verbose_name_plural = "Custom Users"
        indexes = [
            models.Index(fields=["phone"], name="customuser_phone_idx"),
            models.Index(
                fields=["phone_verified"], name="customuser_phone_verified_idx"
            ),
            models.Index(fields=["deleted_at"], name="customuser_deleted_at_idx"),
        ]

    def __str__(self):
        return self.username + " - " + str(self.phone)


class AccountVerificationId(models.Model):
    user = models.ForeignKey(
        CustomUser, on_delete=models.CASCADE, related_name="verification_ids"
    )
    front_image = models.ImageField(upload_to=RandomFileName("verification/front/"))
    back_image = models.ImageField(upload_to=RandomFileName("verification/back/"))
    verified = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = "Account Verification ID"
        verbose_name_plural = "Account Verification IDs"

    def __str__(self):
        return (
            self.user.username
            + " - "
            + ("Verified" if self.verified else "Not Verified")
        )

    def delete(self, *args, **kwargs):
        self.deleted_at = timezone.now()
        self.save()

    def hard_delete(self, *args, **kwargs):
        super(AccountVerificationId, self).delete(*args, **kwargs)


class Wallet(models.Model):
    user = models.OneToOneField(
        CustomUser, on_delete=models.CASCADE, related_name="wallet"
    )
    balance = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True, db_index=True)

    class Meta:
        verbose_name = "Wallet"
        verbose_name_plural = "Wallets"

    def __str__(self):
        return f"{self.user.username}'s Wallet"

    def delete(self, *args, **kwargs):
        self.deleted_at = timezone.now()
        self.save()

    def hard_delete(self, *args, **kwargs):
        super(Wallet, self).delete(*args, **kwargs)


class TransactionActionEnum(models.TextChoices):
    DEPOSIT = "DEPOSIT", "Deposit"
    WITHDRAW = "WITHDRAW", "Withdraw"
    TRANSFER = "TRANSFER", "Transfer"
    REFUND = "REFUND", "Refund"
    PAYMENT = "PAYMENT", "Payment"
    CASHBACK = "CASHBACK", "Cashback"
    FEES = "FEES", "Fees"
    ORDER_REJECT_FEES = "ORDER_REJECT_FEES", "Order Reject Fees"
    ORDER_CANCEL_FEES = "ORDER_CANCEL_FEES", "Order Cancel Fees"
    ORDER_COMPLETE_FEES = "ORDER_COMPLETE_FEES", "Order Complete Fees"
    SETTLEMENT = "SETTLEMENT", "Settlement"


class Transaction(models.Model):
    related_order = models.ForeignKey(
        "stores.Order",
        on_delete=models.CASCADE,
        related_name="transactions",
        null=True,
        blank=True,
    )
    user = models.ForeignKey(
        CustomUser, on_delete=models.CASCADE, related_name="transactions"
    )
    wallet = models.ForeignKey(
        Wallet, on_delete=models.CASCADE, related_name="transactions"
    )
    action = models.CharField(
        max_length=255,
        choices=TransactionActionEnum.choices,
        default=TransactionActionEnum.REFUND,
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True, db_index=True)

    class Meta:
        verbose_name = "Transaction"
        verbose_name_plural = "Transactions"
        indexes = [
            models.Index(fields=["user", "wallet"]),
            models.Index(fields=["related_order"]),
        ]

    def is_cashback(self):
        return self.action == TransactionActionEnum.CASHBACK

    def is_fees(self):
        return self.action == TransactionActionEnum.FEES

    def __str__(self):
        if self.is_cashback():
            return f"Cashback of {self.amount} for order {self.related_order.id} by {self.user.username}"
        if self.is_fees():
            return f"Fees of {self.amount} for order {self.related_order.id} by {self.user.username}"
        return f"{self.action} of {self.amount} by {self.user.username}"

    def delete(self, *args, **kwargs):
        self.deleted_at = timezone.now()
        self.save()

    def hard_delete(self, *args, **kwargs):
        super(Transaction, self).delete(*args, **kwargs)


class OTP(models.Model):
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name="otps")
    code = models.CharField(max_length=6)
    is_used = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()

    class Meta:
        verbose_name = "OTP"
        verbose_name_plural = "OTPs"

    def __str__(self):
        return f"{self.user.username} - {self.code}"

    @classmethod
    def generate_otp(cls, user):
        # Generate a 4-digit OTP
        code = "".join([str(random.randint(0, 9)) for _ in range(4)])

        # Set expiration time to 10 minutes from now
        expiration = timezone.now() + timezone.timedelta(minutes=10)

        # Create and return the OTP
        otp = cls.objects.create(user=user, code=code, expires_at=expiration)
        return otp

    def is_valid(self):
        return not self.is_used and timezone.now() < self.expires_at


class Notification(models.Model):
    user = models.ForeignKey(
        CustomUser, on_delete=models.CASCADE, related_name="notifications"
    )
    title = models.CharField(max_length=255)
    body = models.TextField()
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = "Notification"
        verbose_name_plural = "Notifications"

    def __str__(self):
        return f"{self.user.username} - {self.title}"

    def delete(self, *args, **kwargs):
        self.deleted_at = timezone.now()
        self.save()
