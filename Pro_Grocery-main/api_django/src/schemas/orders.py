from typing import List, Optional

from ninja import Schema


# ----------------------------------------------------------------------------
# Input Schemas
# ----------------------------------------------------------------------------


class OrderItemIn(Schema):
    """Schema for order items input"""

    item_id: int
    quantity: int


class OrderIn(Schema):
    """Schema for creating a new order"""

    wholesaler_id: int
    store_id: int
    items: List[OrderItemIn]
    deliver_at: Optional[str] = None


# ----------------------------------------------------------------------------
# Output Schemas
# ----------------------------------------------------------------------------


class StoreOrderOut(Schema):
    """Simplified store schema for order responses"""

    id: int
    name: str
    address: str


class WholesalerOut(Schema):
    """Simplified wholesaler schema for order responses"""

    id: int
    logo: Optional[str] = None
    title: str


class ProductbOut(Schema):
    """Basic product info for order items"""

    id: int
    name: str
    title: str
    unit: str
    unit_count: float
    image_url: Optional[str] = None


class OrderItemOut2(Schema):
    id: int
    order_id: int
    product_item_id: int
    quantity: int
    price_per_unit: float
    total_price: float
    product: ProductbOut


class OrderOut(Schema):
    id: int
    wholesaler: WholesalerOut
    store: StoreOrderOut
    order_items: List[OrderItemOut2]
    # monetary/totals
    total_price: float
    fees: float
    products_total_price: float
    products_total_quantity: int
    # status info
    status: str
    status_reason: Optional[str] = None
    final_completed_price: Optional[float] = None
    # datetimes (serialized as ISO strings by our builder)
    deliver_at: Optional[str] = None
    completed_at: Optional[str] = None
    created_at: str


class PaginatedOrderResponse(Schema):
    """Paginated response for orders"""

    orders: List[OrderOut]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool
