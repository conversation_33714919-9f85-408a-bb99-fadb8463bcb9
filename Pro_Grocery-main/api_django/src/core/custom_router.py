from ninja import Router


class CustomRouter(Router):
    pass
    # def add_api_operation(self, path, methods, view_func, **kwargs):
    #     # Automatically set the operation_id to the function's name if not explicitly set
    #     # if "operation_id" not in kwargs:
    #     kwargs["operation_id"] = view_func.__name__
    #     return super().add_api_operation(path, methods, view_func, **kwargs)
