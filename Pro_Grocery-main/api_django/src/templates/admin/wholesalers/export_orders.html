{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; <a href="{% url 'admin:wholesalers_wholesaler_changelist' %}">{% trans 'Wholesalers' %}</a>
&rsaquo; <a href="{% url 'admin:wholesalers_wholesaler_change' wholesaler.id %}">{{ wholesaler.title }}</a>
&rsaquo; {% trans 'Export Orders' %}
</div>
{% endblock %}

{% block content %}
<div class="module aligned">
    <h1>{{ title }}</h1>
    
    <div class="form-row">
        <p>Select the date range for exporting orders for <strong>{{ wholesaler.title }}</strong>:</p>
    </div>
    
    <form method="get" action="{% url 'admin:wholesaler_download_orders_csv' wholesaler.id %}">
        <fieldset class="module aligned">
            <div class="form-row">
                <div>
                    <label for="start_date">Start Date:</label>
                    <input type="date" id="start_date" name="start_date" value="{{ start_date }}" required>
                </div>
            </div>
            
            <div class="form-row">
                <div>
                    <label for="end_date">End Date:</label>
                    <input type="date" id="end_date" name="end_date" value="{{ end_date }}" required>
                </div>
            </div>
            
            <div class="form-row">
                <div class="submit-row">
                    <input type="submit" value="Download CSV" class="default" name="_download">
                    <a href="{% url 'admin:wholesalers_wholesaler_change' wholesaler.id %}" class="button cancel-link">Cancel</a>
                </div>
            </div>
        </fieldset>
    </form>
    
    <div class="help" style="background-color: transparent; border: none; padding: 0;">
        <p><strong>Note:</strong> The CSV file will include all orders for this wholesaler within the selected date range, including:</p>
        <ul>
            <li>Order ID and Date</li>
            <li>Store Name and Owner Information</li>
            <li>Store Owner Phone Number</li>
            <li>Order Status</li>
            <li>Products Total Price</li>
            <li>Fees</li>
            <li>Total Price</li>
            <li>Total Quantity</li>
            <li>Delivery Date</li>
            <li>Completion Information</li>
        </ul>
    </div>
</div>

<style>
.form-row {
    margin-bottom: 10px;
}

.form-row div {
    margin-bottom: 8px;
}

.form-row label {
    display: inline-block;
    width: 100px;
    font-weight: bold;
}

.form-row input[type="date"] {
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 13px;
}

.help {
    margin-top: 20px;
    padding: 10px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.help ul {
    margin: 10px 0 0 20px;
}

.cancel-link {
    margin-left: 10px;
    color: #666;
    text-decoration: none;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: #f8f9fa;
}

.cancel-link:hover {
    background-color: #e9ecef;
    text-decoration: none;
}
</style>
{% endblock %}
