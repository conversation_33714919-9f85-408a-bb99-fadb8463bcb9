<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة طلب #{{ order.id }} - {{ order.store.name }}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --brand-green: #28a745;
            --light-gray: #f8f9fa;
            --border-gray: #dee2e6;
            --text-dark: #212529;
            --text-muted: #6c757d;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans Arabic', 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: var(--text-dark);
            direction: rtl;
            text-align: right;
            background: white;
            margin: 5mm;
            font-weight: 400;
        }

        .print-container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.6s ease-out forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Header Section */
        .print-header {
            border-bottom: 3px solid var(--brand-green);
            padding: 8px 0;
            margin-bottom: 12px;
        }

        .header-content {
            /* Simple container for header content */
        }

        .header-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 8px;
        }

        .company-info, .store-info {
            flex: 1;
            background: var(--light-gray);
            border: 1px solid var(--border-gray);
            padding: 8px;
        }

        .company-logo {
            max-width: 70px;
            max-height: 55px;
            object-fit: contain;
            margin-bottom: 8px;
        }

        .company-name {
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 6px;
            color: var(--brand-green);
        }

        .section-title {
            font-size: 13px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--brand-green);
            border-bottom: 2px solid var(--brand-green);
            padding-bottom: 3px;
        }

        .info-line {
            margin-bottom: 3px;
            font-size: 10px;
            color: var(--text-muted);
        }

        .info-line strong {
            color: var(--text-dark);
            font-weight: 600;
        }

        .invoice-title {
            text-align: center;
            padding: 6px;
            margin: 8px 0 0 0;
        }

        .invoice-title h1 {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 5px;
            color: var(--brand-green);
        }

        .invoice-dates {
            font-size: 11px;
            color: var(--text-muted);
            font-weight: 500;
        }

        /* Order Summary */
        .order-summary {
            display: flex;
            justify-content: space-between;
            margin: 12px 8px;
            padding: 8px;
            background: var(--light-gray);
            border: 2px solid var(--brand-green);
        }

        .summary-item {
            text-align: center;
            flex: 1;
            position: relative;
        }

        .summary-item:not(:last-child)::after {
            content: '';
            position: absolute;
            right: -1px;
            top: 10%;
            height: 80%;
            width: 1px;
            background: var(--border-gray);
        }

        .summary-label {
            font-size: 10px;
            color: var(--text-muted);
            margin-bottom: 4px;
            font-weight: 500;
        }

        .summary-value {
            font-size: 12px;
            font-weight: 700;
            color: var(--brand-green);
        }

        /* Items Table */
        .items-table {
            width: calc(100% - 16px);
            margin: 12px 8px;
            border-collapse: collapse;
            font-size: 10px;
            background: white;
        }

        .items-table th,
        .items-table td {
            border: 1px solid var(--border-gray);
            padding: 4px 3px;
            text-align: center;
        }

        .items-table th {
            background: var(--brand-green);
            color: white;
            font-weight: 600;
            font-size: 10px;
        }

        .items-table tbody tr:nth-child(even) {
            background: var(--light-gray);
        }

        .product-name {
            text-align: right;
            max-width: 150px;
            font-weight: 500;
        }

        .product-details {
            font-size: 9px;
            color: var(--text-muted);
            margin-top: 2px;
            font-style: italic;
        }

        /* Totals Section */
        .totals-section {
            margin: 12px 8px;
            display: flex;
            justify-content: flex-end;
        }

        .totals-table {
            border-collapse: collapse;
            min-width: 250px;
            background: white;
        }

        .totals-table td {
            border: 1px solid var(--border-gray);
            padding: 4px 8px;
            font-size: 11px;
        }

        .totals-table .label {
            background: var(--light-gray);
            font-weight: 600;
            text-align: right;
            color: var(--text-dark);
        }

        .totals-table .value {
            text-align: left;
            min-width: 100px;
            font-weight: 500;
            color: var(--text-muted);
        }

        .total-final .label {
            background: var(--brand-green) !important;
            color: white !important;
            font-weight: 700;
        }

        .total-final .value {
            background: var(--light-gray) !important;
            color: var(--brand-green) !important;
            font-weight: 700;
            font-size: 12px;
        }

        /* Footer */
        .print-footer {
            margin: 12px 8px 8px 8px;
            background: var(--light-gray);
            border: 1px solid var(--border-gray);
            padding: 8px;
            display: flex;
            justify-content: space-between;
            font-size: 10px;
        }

        .footer-left, .footer-right {
            flex: 1;
        }

        .footer-left {
            color: var(--text-dark);
            font-weight: 500;
        }

        .footer-right {
            text-align: left;
            color: var(--text-muted);
            font-size: 9px;
        }

        .footer-center {
            text-align: center;
            margin-top: 8px;
            padding: 6px;
            background: var(--brand-green);
            color: white;
            font-weight: 600;
            font-size: 12px;
        }

        /* Status Badge */
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border: 1px solid var(--border-gray);
            font-size: 9px;
            font-weight: 600;
            background: var(--light-gray);
            color: var(--text-dark);
        }

        /* Print optimizations */
                    @media print {
            body {
                margin: 4mm;
                font-size: 9px;
                background: white !important;
            }

            .print-container {
                page-break-inside: avoid;
                animation: none !important;
                opacity: 1 !important;
                transform: none !important;
            }

            .items-table {
                page-break-inside: auto;
            }

            .items-table tr {
                page-break-inside: avoid;
            }

            .header-row {
                gap: 10px;
            }

            .company-info, .store-info {
                padding: 4px;
            }

            .info-line {
                margin-bottom: 1px;
                font-size: 8px;
            }
        }

        /* Compact spacing */
        .compact {
            margin: 2px 0;
        }

        .no-margin {
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="print-container">
        <!-- Header Section -->
        <div class="print-header">
            <div class="header-content">
            <div class="header-row">
                <!-- Wholesaler Information -->
                <div class="company-info">
                    {% if wholesaler.logo %}
                    <img src="{{ wholesaler.logo.url }}" alt="{{ wholesaler.title }}" class="company-logo">
                    {% endif %}
                    <div class="company-name">{{ wholesaler.title }}</div>
                    <div class="info-line"><strong>المستخدم:</strong> {{ wholesaler.username }}</div>
                    <div class="info-line"><strong>الفئة:</strong> 
                        {% if wholesaler.category == 'GROCERY' %}بقالة
                        {% elif wholesaler.category == 'PHARMACEUTICAL' %}صيدلية
                        {% elif wholesaler.category == 'ELECTRONICS' %}إلكترونيات
                        {% else %}{{ wholesaler.category }}{% endif %}
                    </div>
                    {% if wholesaler.user.phone %}
                    <div class="info-line"><strong>الهاتف:</strong> {{ wholesaler.user.phone }}</div>
                    {% endif %}
                    {% if wholesaler.user.email %}
                    <div class="info-line"><strong>البريد:</strong> {{ wholesaler.user.email }}</div>
                    {% endif %}
                </div>

                <!-- Store Information -->
                <div class="store-info">
                    <div class="section-title">معلومات المتجر</div>
                    <div class="info-line"><strong>المتجر:</strong> {{ order.store.name }}</div>
                    <div class="info-line"><strong>المالك:</strong> {{ order.store.owner.username }}</div>
                    {% if order.store.owner.phone %}
                    <div class="info-line"><strong>الهاتف:</strong> {{ order.store.owner.phone }}</div>
                    {% endif %}
                    <div class="info-line"><strong>العنوان:</strong> {{ order.store.address }}</div>
                    {% if order.store.city %}
                    <div class="info-line"><strong>المدينة:</strong> {{ order.store.city.name }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- Invoice Title -->
            <div class="invoice-title">
                <h1>فاتورة طلب #{{ order.id }}</h1>
                <div class="invoice-dates">
                    تاريخ الطلب: {{ order.created_at|date:"Y/m/d H:i" }} |
                    تاريخ الطباعة: <span id="printDate"></span>
                </div>
            </div>
            </div>
        </div>

        <!-- Order Summary -->
        <div class="order-summary">
            <div class="summary-item">
                <div class="summary-label">رقم الطلب</div>
                <div class="summary-value">#{{ order.id }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">الحالة</div>
                <div class="summary-value">
                    <span class="status-badge">
                        {% if order.status == 'pending' %}في الانتظار
                        {% elif order.status == 'processing' %}قيد المعالجة
                        {% elif order.status == 'shipped' %}تم الشحن
                        {% elif order.status == 'delivered' %}تم التسليم
                        {% elif order.status == 'cancelled' %}ملغي
                        {% endif %}
                    </span>
                </div>
            </div>
            <div class="summary-item">
                <div class="summary-label">عدد المنتجات</div>
                <div class="summary-value">{{ order.order_items.count }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">المبلغ الإجمالي</div>
                <div class="summary-value">{{ order.total_price }} ج.م</div>
            </div>
        </div>

        <!-- Order Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%;">#</th>
                    <th style="width: 35%;">المنتج</th>
                    <th style="width: 15%;">الشركة</th>
                    <th style="width: 10%;">السعر</th>
                    <th style="width: 10%;">الكمية</th>
                    <th style="width: 15%;">الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                {% for item in order_items %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td class="product-name">
                        <div>{{ item.product_item.product.name }}</div>
                    </td>
                    <td>
                        {% if item.product_item.product.company %}
                        {{ item.product_item.product.company.name }}
                        {% else %}
                        غير محدد
                        {% endif %}
                    </td>
                    <td>{{ item.price_per_unit }} ج.م</td>
                    <td>{{ item.quantity }}</td>
                    <td>{{ item.total_price }} ج.م</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Totals Section -->
        <div class="totals-section">
            <table class="totals-table">
                {% comment %} <tr>
                    <td class="label">المجموع الفرعي:</td>
                    <td class="value">{{ order.products_total_price }} ج.م</td>
                </tr> {% endcomment %}
                {% comment %} <tr>
                    <td class="label">الرسوم:</td>
                    <td class="value">{{ order.fees }} ج.م</td>
                </tr> {% endcomment %}
                <tr class="total-final">
                    <td class="label">المجموع الإجمالي:</td>
                    <td class="value">{{ order.products_total_price }} ج.م</td>
                </tr>
            </table>
        </div>

        <!-- Footer -->
        <div class="print-footer">
            <div class="footer-left">
                <div><strong>{{ wholesaler.title }}</strong></div>
                {% if wholesaler.user.phone %}
                <div>الهاتف: {{ wholesaler.user.phone }}</div>
                {% endif %}
            </div>
            <div class="footer-right">
                <div>تم إنشاء الفاتورة إلكترونياً</div>
                <div>{{ order.created_at|date:"Y/m/d H:i" }}</div>
            </div>
        </div>
        
        <div class="footer-center">
            شكراً لتعاملكم معنا
        </div>
    </div>

    <script>
        // Set print date with enhanced formatting
        document.getElementById('printDate').textContent = new Date().toLocaleDateString('ar-EG', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            timeZone: 'Africa/Cairo'
        });

        // Enhanced auto-print and return functionality
        let printDialogHandled = false;

        function handlePrintCompletion() {
            if (printDialogHandled) return;
            printDialogHandled = true;

            // Add a small delay to ensure print dialog has closed
            setTimeout(() => {
                try {
                    // Check if we have a parent window to return to
                    if (window.opener && !window.opener.closed) {
                        // Focus back to the parent window
                        window.opener.focus();
                        // Close this print window
                        window.close();
                    } else {
                        // If no parent window, try to go back in history
                        if (window.history.length > 1) {
                            window.history.back();
                        } else {
                            // Fallback: close the window
                            window.close();
                        }
                    }
                } catch (error) {
                    console.log('Error returning to parent window:', error);
                    // Fallback: try to close the window
                    window.close();
                }
            }, 500);
        }

        // Auto-print when page loads
        window.onload = function() {
            // Small delay to ensure page is fully rendered
            setTimeout(() => {
                window.print();
            }, 100);
        };

        // Handle print dialog events
        window.addEventListener('beforeprint', function() {
            console.log('Print dialog opened');
        });

        window.addEventListener('afterprint', function() {
            console.log('Print dialog closed');
            handlePrintCompletion();
        });

        // Fallback for browsers that don't support afterprint
        // Monitor for focus return which often indicates print dialog closed
        let focusTimeout;
        window.addEventListener('focus', function() {
            clearTimeout(focusTimeout);
            focusTimeout = setTimeout(() => {
                if (!printDialogHandled) {
                    handlePrintCompletion();
                }
            }, 1000);
        });

        // Handle escape key to close window
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                handlePrintCompletion();
            }
        });

        // Fallback timeout - auto-close after 30 seconds if no interaction
        setTimeout(() => {
            if (!printDialogHandled) {
                console.log('Auto-closing print window after timeout');
                handlePrintCompletion();
            }
        }, 30000);

        // Additional fallback: handle window unload to ensure cleanup
        window.addEventListener('beforeunload', function() {
            printDialogHandled = true;
        });
    </script>
</body>
</html>
