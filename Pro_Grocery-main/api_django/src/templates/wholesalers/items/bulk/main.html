{% extends 'wholesalers/base.html' %}

{% block title %}إضافة مجمعة للمنتجات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h2 mb-1">
                    <i class="fas fa-upload me-2 text-success"></i>
                    إضافة مجمعة للمنتجات
                </h1>
                <p class="text-muted mb-0">اختر عدة منتجات لإضافتها إلى مخزونك بقيم افتراضية</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{% url 'items_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للمخزون
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Selection Summary -->
<div class="card mb-4" id="selectionSummary" style="display: none;">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="d-flex align-items-center">
                    <div class="bg-success rounded-circle d-flex align-items-center justify-content-center me-3" 
                         style="width: 50px; height: 50px;">
                        <i class="fas fa-check text-white"></i>
                    </div>
                    <div>
                        <h5 class="mb-1">تم اختيار <span id="selectedCount">0</span> منتج</h5>
                        <p class="text-muted mb-0">سيتم إنشاء المنتجات بقيم افتراضية يمكنك تعديلها لاحقاً</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <button type="button" class="btn btn-outline-secondary" onclick="clearSelection()">
                        <i class="fas fa-times me-2"></i>
                        مسح الاختيار
                    </button>
                    <button type="button" class="btn btn-success" onclick="showConfirmationModal()">
                        <i class="fas fa-plus me-2"></i>
                        إضافة المنتجات المحددة
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-search me-2"></i>
            البحث والفلترة
        </h5>
    </div>
    <div class="card-body">
        <form method="get" id="searchForm" hx-get="{% url 'bulk_add_items' %}" hx-target="#productsContainer" hx-trigger="input delay:500ms, submit">
            <div class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" 
                           class="form-control" 
                           id="search" 
                           name="search" 
                           placeholder="اسم المنتج أو الباركود..."
                           value="{{ search_query }}"
                           autocomplete="off">
                </div>
                
                <div class="col-md-3">
                    <label for="company" class="form-label">الشركة</label>
                    <select class="form-select" id="company" name="company">
                        <option value="">جميع الشركات</option>
                        {% for company in companies %}
                        <option value="{{ company.id }}" {% if company_filter == company.id|stringformat:"s" %}selected{% endif %}>
                            {{ company.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="category" class="form-label">الفئة</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">جميع الفئات</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if category_filter == category.id|stringformat:"s" %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-2 d-flex align-items-end">
                    <button type="button" class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                        <i class="fas fa-times me-2"></i>
                        مسح
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Products List -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-boxes me-2"></i>
            المنتجات المتاحة
        </h5>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAllVisible()">
                <i class="fas fa-check-square me-1"></i>
                تحديد الكل
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="deselectAllVisible()">
                <i class="fas fa-square me-1"></i>
                إلغاء التحديد
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <div id="productsContainer">
            {% include 'wholesalers/items/bulk/partials/products_list.html' %}
        </div>
    </div>
</div>

<!-- Confirmation Modal -->
<div class="modal fade" id="confirmationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الإضافة المجمعة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> سيتم إنشاء المنتجات بالقيم الافتراضية التالية:
                </div>
                
                <div class="row g-3 mb-4">
                    <div class="col-md-6">
                        <div class="border rounded p-3">
                            <h6 class="text-success mb-2">
                                <i class="fas fa-tag me-1"></i>
                                السعر الأساسي
                            </h6>
                            <p class="mb-0 text-muted">0.00 ج.م</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="border rounded p-3">
                            <h6 class="text-success mb-2">
                                <i class="fas fa-warehouse me-1"></i>
                                المخزون
                            </h6>
                            <p class="mb-0 text-muted">0 قطعة</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="border rounded p-3">
                            <h6 class="text-success mb-2">
                                <i class="fas fa-arrow-down me-1"></i>
                                الحد الأدنى للطلب
                            </h6>
                            <p class="mb-0 text-muted">1 قطعة</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="border rounded p-3">
                            <h6 class="text-success mb-2">
                                <i class="fas fa-arrow-up me-1"></i>
                                الحد الأقصى للطلب
                            </h6>
                            <p class="mb-0 text-muted">غير محدد</p>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    يمكنك تعديل هذه القيم لاحقاً من صفحة إدارة المخزون أو استخدام التعديل المجمع.
                </div>
                
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="redirectToBulkEdit">
                    <label class="form-check-label" for="redirectToBulkEdit">
                        الانتقال مباشرة لتعديل المنتجات المضافة بعد الإنشاء
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="processBulkAdd()">
                    <i class="fas fa-plus me-2"></i>
                    تأكيد الإضافة
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.product-card {
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
}

.product-card:hover {
    border-color: var(--primary-green);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.15);
}

.product-card.selected {
    border-color: var(--primary-green);
    background-color: var(--light-green);
}

.product-checkbox {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
}

.product-image {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 0.5rem;
}

.product-placeholder {
    width: 100%;
    height: 150px;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
}

.selection-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 10;
}

@media (max-width: 768px) {
    .product-image,
    .product-placeholder {
        height: 120px;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let selectedProducts = new Set();

function updateSelectionSummary() {
    const count = selectedProducts.size;
    document.getElementById('selectedCount').textContent = count;
    
    const summaryCard = document.getElementById('selectionSummary');
    if (count > 0) {
        summaryCard.style.display = 'block';
    } else {
        summaryCard.style.display = 'none';
    }
}

function toggleProduct(productId) {
    const checkbox = document.querySelector(`input[data-product-id="${productId}"]`);
    const card = checkbox.closest('.product-card');
    
    if (checkbox.checked) {
        selectedProducts.add(productId);
        card.classList.add('selected');
    } else {
        selectedProducts.delete(productId);
        card.classList.remove('selected');
    }
    
    updateSelectionSummary();
}

function selectAllVisible() {
    const checkboxes = document.querySelectorAll('input[data-product-id]');
    checkboxes.forEach(checkbox => {
        if (!checkbox.checked) {
            checkbox.checked = true;
            const productId = checkbox.getAttribute('data-product-id');
            selectedProducts.add(productId);
            checkbox.closest('.product-card').classList.add('selected');
        }
    });
    updateSelectionSummary();
}

function deselectAllVisible() {
    const checkboxes = document.querySelectorAll('input[data-product-id]');
    checkboxes.forEach(checkbox => {
        if (checkbox.checked) {
            checkbox.checked = false;
            const productId = checkbox.getAttribute('data-product-id');
            selectedProducts.delete(productId);
            checkbox.closest('.product-card').classList.remove('selected');
        }
    });
    updateSelectionSummary();
}

function clearSelection() {
    selectedProducts.clear();
    const checkboxes = document.querySelectorAll('input[data-product-id]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
        checkbox.closest('.product-card').classList.remove('selected');
    });
    updateSelectionSummary();
}

function clearFilters() {
    document.getElementById('search').value = '';
    document.getElementById('company').value = '';
    document.getElementById('category').value = '';
    htmx.trigger('#searchForm', 'submit');
}

function showConfirmationModal() {
    if (selectedProducts.size === 0) {
        alert('يرجى اختيار منتج واحد على الأقل');
        return;
    }
    
    const modal = new bootstrap.Modal(document.getElementById('confirmationModal'));
    modal.show();
}

function processBulkAdd() {
    if (selectedProducts.size === 0) {
        alert('يرجى اختيار منتج واحد على الأقل');
        return;
    }
    
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{% url "bulk_add_process" %}';
    
    // Add CSRF token
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrfmiddlewaretoken';
    csrfInput.value = '{{ csrf_token }}';
    form.appendChild(csrfInput);
    
    // Add selected products
    selectedProducts.forEach(productId => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'selected_products';
        input.value = productId;
        form.appendChild(input);
    });
    
    // Add redirect preference
    const redirectInput = document.createElement('input');
    redirectInput.type = 'hidden';
    redirectInput.name = 'redirect_to';
    redirectInput.value = document.getElementById('redirectToBulkEdit').checked ? 'bulk_edit' : 'items_list';
    form.appendChild(redirectInput);
    
    document.body.appendChild(form);
    form.submit();
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateSelectionSummary();
    
    // Auto-focus on search input
    document.getElementById('search').focus();
});

// Handle HTMX after request to maintain selection state
document.body.addEventListener('htmx:afterRequest', function(event) {
    // Restore selection state after HTMX updates
    selectedProducts.forEach(productId => {
        const checkbox = document.querySelector(`input[data-product-id="${productId}"]`);
        if (checkbox) {
            checkbox.checked = true;
            checkbox.closest('.product-card').classList.add('selected');
        }
    });
    updateSelectionSummary();
});
</script>
{% endblock %}
