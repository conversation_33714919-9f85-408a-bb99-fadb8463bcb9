{% extends 'wholesalers/base.html' %}

{% block title %}إضافة منتج جديد - تفاصيل المنتج{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Progress Steps -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="step-item completed">
                            <div class="step-number">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="step-title">اختيار الشركة</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="step-item completed">
                            <div class="step-number">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="step-title">اختيار المنتج</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="step-item active">
                            <div class="step-number">3</div>
                            <div class="step-title">تفاصيل المنتج</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Selected Product Info -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-box me-2"></i>
                    المنتج المحدد
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        {% if product.image %}
                        <img src="{{ product.image.url }}" 
                             alt="{{ product.name }}" 
                             class="img-fluid rounded">
                        {% else %}
                        <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                             style="height: 150px;">
                            <i class="fas fa-image fa-3x text-muted"></i>
                        </div>
                        {% endif %}
                    </div>
                    <div class="col-md-9">
                        <h5>{{ product.name }}</h5>
                        <p class="text-muted mb-2">{{ product.title }}</p>
                        
                        <div class="row">
                            <div class="col-sm-6">
                                {% if product.company %}
                                <p class="mb-1">
                                    <strong>الشركة:</strong> {{ product.company.name }}
                                </p>
                                {% endif %}
                                {% if product.category %}
                                <p class="mb-1">
                                    <strong>الفئة:</strong> {{ product.category.name }}
                                </p>
                                {% endif %}
                            </div>
                            <div class="col-sm-6">
                                {% if product.barcode %}
                                <p class="mb-1">
                                    <strong>الباركود:</strong> {{ product.barcode }}
                                </p>
                                {% endif %}
                            </div>
                        </div>
                        
                        {% if product.description %}
                        <div class="mt-2">
                            <strong>الوصف:</strong>
                            <p class="text-muted">{{ product.description|truncatechars:200 }}</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Item Details Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    تفاصيل المنتج في المخزون
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="itemForm">
                    {% csrf_token %}
                    
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="base_price" class="form-label">
                                <i class="fas fa-tag me-1"></i>
                                السعر الأساسي *
                            </label>
                            <div class="input-group">
                                <input type="number" 
                                       class="form-control" 
                                       id="base_price" 
                                       name="base_price" 
                                       step="0.01" 
                                       min="0"
                                       required
                                       placeholder="0.00">
                                <span class="input-group-text">ج.م</span>
                            </div>
                            <div class="form-text">السعر الذي ستبيع به هذا المنتج</div>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="inventory_count" class="form-label">
                                <i class="fas fa-warehouse me-1"></i>
                                الكمية المتوفرة
                            </label>
                            <input type="number" 
                                   class="form-control" 
                                   id="inventory_count" 
                                   name="inventory_count" 
                                   min="0"
                                   value="0"
                                   placeholder="0">
                            <div class="form-text">الكمية الحالية في المخزون</div>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="minimum_order_quantity" class="form-label">
                                <i class="fas fa-arrow-down me-1"></i>
                                الحد الأدنى للطلب *
                            </label>
                            <input type="number" 
                                   class="form-control" 
                                   id="minimum_order_quantity" 
                                   name="minimum_order_quantity" 
                                   min="1"
                                   value="1"
                                   required
                                   placeholder="1">
                            <div class="form-text">أقل كمية يمكن طلبها</div>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="maximum_order_quantity" class="form-label">
                                <i class="fas fa-arrow-up me-1"></i>
                                الحد الأقصى للطلب
                            </label>
                            <input type="number" 
                                   class="form-control" 
                                   id="maximum_order_quantity" 
                                   name="maximum_order_quantity" 
                                   min="1"
                                   placeholder="غير محدد">
                            <div class="form-text">أكبر كمية يمكن طلبها (اختياري)</div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-4">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> يمكنك تعديل هذه المعلومات لاحقاً من صفحة إدارة المخزون.
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{% url 'item_create_step2' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-2"></i>
                            السابق
                        </a>
                        <div class="d-flex gap-2">
                            <a href="{% url 'items_list' %}" class="btn btn-outline-danger">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-success" id="submitBtn">
                                <i class="fas fa-check me-2"></i>
                                إضافة المنتج
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.step-item {
    position: relative;
    padding: 1rem 0;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin: 0 auto 0.5rem;
    transition: all 0.3s ease;
}

.step-item.active .step-number {
    background-color: var(--primary-green);
    color: white;
}

.step-item.completed .step-number {
    background-color: var(--secondary-green);
    color: white;
}

.step-title {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.step-item.active .step-title {
    color: var(--primary-green);
    font-weight: 600;
}

.step-item.completed .step-title {
    color: var(--secondary-green);
    font-weight: 600;
}

/* Connect steps with lines */
.step-item:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 20px;
    right: -50%;
    width: 100%;
    height: 2px;
    background-color: #e9ecef;
    z-index: -1;
}

.step-item.completed:not(:last-child)::after {
    background-color: var(--secondary-green);
}

.form-control:focus {
    border-color: var(--primary-green);
    box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25);
}

.btn-success {
    background-color: var(--primary-green);
    border-color: var(--primary-green);
}

.btn-success:hover {
    background-color: var(--primary-green-dark);
    border-color: var(--primary-green-dark);
}

@media (max-width: 768px) {
    .step-item:not(:last-child)::after {
        display: none;
    }
    
    .step-number {
        width: 32px;
        height: 32px;
        font-size: 0.9rem;
    }
    
    .step-title {
        font-size: 0.8rem;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on price input
    document.getElementById('base_price').focus();
    
    // Form validation
    const form = document.getElementById('itemForm');
    const submitBtn = document.getElementById('submitBtn');
    
    form.addEventListener('submit', function(e) {
        const basePrice = document.getElementById('base_price').value;
        const minOrder = document.getElementById('minimum_order_quantity').value;
        const maxOrder = document.getElementById('maximum_order_quantity').value;
        
        // Validate price
        if (!basePrice || parseFloat(basePrice) <= 0) {
            e.preventDefault();
            alert('يرجى إدخال سعر صحيح');
            document.getElementById('base_price').focus();
            return;
        }
        
        // Validate order quantities
        if (maxOrder && parseInt(maxOrder) < parseInt(minOrder)) {
            e.preventDefault();
            alert('الحد الأقصى للطلب يجب أن يكون أكبر من أو يساوي الحد الأدنى');
            document.getElementById('maximum_order_quantity').focus();
            return;
        }
        
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإضافة...';
    });
    
    // Auto-calculate suggestions
    const basePriceInput = document.getElementById('base_price');
    basePriceInput.addEventListener('input', function() {
        const price = parseFloat(this.value);
        if (price > 0) {
            // You could add price suggestions or validation here
        }
    });
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && e.ctrlKey) {
            form.submit();
        }
        if (e.key === 'Escape') {
            if (confirm('هل تريد إلغاء إضافة المنتج؟')) {
                window.location.href = '{% url "items_list" %}';
            }
        }
    });
});
</script>
{% endblock %}
