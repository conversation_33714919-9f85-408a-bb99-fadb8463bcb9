{% extends "base.html" %}

{% block title %}TagerPlus - {{ category.name }}{% endblock %}

{% block content %}
<div class="mb-4">
    <div class="d-flex justify-content-between align-items-center">
        <h1>{{ category.name }}</h1>
        <a href="{% url 'category_list' %}" class="btn btn-outline-secondary">Back to Categories</a>
    </div>
    <p class="lead">{{ category.title }}</p>
    <p class="text-muted">Slug: {{ category.slug }}</p>
</div>

<h2 class="mb-3">Products in this Category</h2>

<div class="row">
    {% if products %}
    {% for product in products %}
    <div class="col-md-6 col-lg-4 mb-3">
        <div class="card h-100">
            {% if product.image %}
            <img src="{{ product.image.url }}" class="card-img-top" alt="{{ product.name }}"
                style="height: 200px; object-fit: cover;">
            {% else %}
            <div class="bg-light text-center py-5">
                <span class="text-muted">No Image</span>
            </div>
            {% endif %}
            <div class="card-body">
                <h5 class="card-title">{{ product.name }}</h5>
                <h6 class="card-subtitle mb-2 text-muted">{{ product.title }}</h6>

                {% if product.company %}
                <p class="mb-1">
                    <span class="badge bg-primary">{{ product.company.name }}</span>
                </p>
                {% endif %}

                <p class="card-text small text-muted mt-2">Barcode: {{ product.barcode }}</p>

                <div class="d-flex justify-content-between mt-3">
                    <a href="{% url 'product_detail' product.id %}" class="btn btn-sm btn-outline-primary">View
                        Details</a>
                    <a href="{% url 'product_update' product.id %}" class="btn btn-sm btn-outline-secondary">Edit</a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
    {% else %}
    <div class="col-12">
        <div class="alert alert-info text-center">
            No products found in this category.
            <a href="{% url 'product_create' %}" class="alert-link">Create a product</a> and assign it to this category.
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}