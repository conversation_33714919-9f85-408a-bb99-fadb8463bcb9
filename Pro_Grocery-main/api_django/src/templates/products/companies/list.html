{% extends "base.html" %}

{% block title %}TagerPlus - Companies{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Companies</h1>
    <button class="btn btn-primary" hx-get="{% url 'company_create' %}" hx-target="#companyFormContainer"
        hx-swap="innerHTML">
        Add Company
    </button>
</div>

<!-- Company form container -->
<div id="companyFormContainer" class="mb-4"></div>

<!-- Company list -->
<div id="companyList" hx-get="{% url 'company_list' %}" hx-trigger="companyListChanged from:body">
    {% include "products/companies/partials/company_list.html" %}
</div>
{% endblock %}