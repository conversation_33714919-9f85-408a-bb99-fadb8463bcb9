{% extends "base.html" %}

{% block title %}TagerPlus - Update {{ product.name }}{% endblock %}

{% block content %}
<div class="mb-4">
    <div class="d-flex justify-content-between align-items-center">
        <h1>Update Product</h1>
        <a href="{% url 'product_detail' product.id %}" class="btn btn-outline-secondary">Cancel</a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}

            <div class="row g-3">
                <div class="col-md-6">
                    <label for="{{ form.name.id_for_label }}" class="form-label">Product Name</label>
                    {{ form.name }}
                    {% if form.name.errors %}
                    <div class="invalid-feedback d-block">{{ form.name.errors }}</div>
                    {% endif %}
                </div>

                <div class="col-md-6">
                    <label for="{{ form.title.id_for_label }}" class="form-label">Product Title</label>
                    {{ form.title }}
                    {% if form.title.errors %}
                    <div class="invalid-feedback d-block">{{ form.title.errors }}</div>
                    {% endif %}
                </div>

                <div class="col-md-6">
                    <label for="{{ form.barcode.id_for_label }}" class="form-label">Barcode</label>
                    {{ form.barcode }}
                    {% if form.barcode.errors %}
                    <div class="invalid-feedback d-block">{{ form.barcode.errors }}</div>
                    {% endif %}
                </div>

                <div class="col-md-6">
                    <label for="{{ form.slug.id_for_label }}" class="form-label">Slug</label>
                    {{ form.slug }}
                    {% if form.slug.errors %}
                    <div class="invalid-feedback d-block">{{ form.slug.errors }}</div>
                    {% endif %}
                </div>

                <div class="col-md-6">
                    <label for="{{ form.company.id_for_label }}" class="form-label">Company</label>
                    {{ form.company }}
                    {% if form.company.errors %}
                    <div class="invalid-feedback d-block">{{ form.company.errors }}</div>
                    {% endif %}
                </div>

                <div class="col-md-6">
                    <label for="{{ form.category.id_for_label }}" class="form-label">Category</label>
                    {{ form.category }}
                    {% if form.category.errors %}
                    <div class="invalid-feedback d-block">{{ form.category.errors }}</div>
                    {% endif %}
                </div>

                <div class="col-12">
                    <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                    {{ form.description }}
                    {% if form.description.errors %}
                    <div class="invalid-feedback d-block">{{ form.description.errors }}</div>
                    {% endif %}
                </div>

                <div class="col-12">
                    <label for="{{ form.image.id_for_label }}" class="form-label">Product Image</label>
                    {{ form.image }}
                    {% if form.image.errors %}
                    <div class="invalid-feedback d-block">{{ form.image.errors }}</div>
                    {% endif %}
                    {% if product.image %}
                    <div class="mt-2">
                        <p class="small text-muted">Current image:</p>
                        <img src="{{ product.image.url }}" alt="{{ product.name }}"
                            style="max-height: 100px; max-width: 100%;" class="img-thumbnail">
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="mt-4">
                <button type="submit" class="btn btn-primary">Update Product</button>
                <a href="{% url 'product_detail' product.id %}" class="btn btn-outline-secondary ms-2">Cancel</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}