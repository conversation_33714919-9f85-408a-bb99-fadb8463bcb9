<div class="row">
    {% if products %}
    {% for product in products %}
    <div class="col-md-6 col-lg-4 mb-3">
        <div class="card h-100">
            {% if product.image %}
            <img src="{{ product.image.url }}" class="card-img-top" alt="{{ product.name }}"
                style="height: 200px; object-fit: cover;">
            {% else %}
            <div class="bg-light text-center py-5">
                <span class="text-muted">No Image</span>
            </div>
            {% endif %}
            <div class="card-body">
                <h5 class="card-title">{{ product.name }}</h5>
                <h6 class="card-subtitle mb-2 text-muted">{{ product.title }}</h6>

                {% if product.company %}
                <p class="mb-1">
                    <span class="badge bg-primary">{{ product.company.name }}</span>
                </p>
                {% endif %}

                {% if product.category %}
                <p class="mb-1">
                    <span class="badge bg-secondary">{{ product.category.name }}</span>
                </p>
                {% endif %}

                <p class="card-text small text-muted mt-2">Barcode: {{ product.barcode }}</p>

                <div class="d-flex justify-content-between mt-3">
                    <a href="{% url 'product_detail' product.id %}" class="btn btn-sm btn-outline-primary">View
                        Details</a>
                    <a href="{% url 'product_update' product.id %}" class="btn btn-sm btn-outline-secondary">Edit</a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
    {% else %}
    <div class="col-12">
        <div class="alert alert-info text-center">
            No products found. {% if selected_company or selected_category %}Try changing your filters or{% endif %} add
            your first product now!
        </div>
    </div>
    {% endif %}
</div>