{% extends "base.html" %}

{% block title %}TagerPlus - {{ product.name }}{% endblock %}

{% block content %}
<div class="mb-4">
    <div class="d-flex justify-content-between align-items-center">
        <h1>{{ product.name }}</h1>
        <div>
            <a href="{% url 'product_update' product.id %}" class="btn btn-primary">Edit Product</a>
            <a href="{% url 'product_list' %}" class="btn btn-outline-secondary ms-2">Back to Products</a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-5 mb-4">
        {% if product.image %}
        <img src="{{ product.image.url }}" alt="{{ product.name }}" class="img-fluid rounded shadow"
            style="width: 100%; object-fit: cover;">
        {% else %}
        <div class="bg-light text-center py-5 rounded shadow">
            <span class="text-muted">No Image Available</span>
        </div>
        {% endif %}
    </div>

    <div class="col-md-7">
        <div class="card mb-4">
            <div class="card-body">
                <h2 class="card-title">{{ product.title }}</h2>

                <div class="mb-3">
                    {% if product.company %}
                    <span class="badge bg-primary me-2">{{ product.company.name }}</span>
                    {% endif %}

                    {% if product.category %}
                    <span class="badge bg-secondary">{{ product.category.name }}</span>
                    {% endif %}
                </div>

                <p class="card-text">{{ product.description|linebreaks }}</p>

                <div class="row mt-4">
                    <div class="col-md-6 mb-3">
                        <h6 class="text-muted">Barcode</h6>
                        <p>{{ product.barcode }}</p>
                    </div>

                    <div class="col-md-6 mb-3">
                        <h6 class="text-muted">Slug</h6>
                        <p>{{ product.slug }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}