"""
CRUD API endpoints for Region model.
"""

from ninja import Router
from ninja.errors import HttpError
from typing import Optional, List
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
import logging

from core.schemas import PaginatedResponse
from products.models import Region, RegionType
from api.middleware import AuthMiddleware
from products.schemas import RegionSchema

logger = logging.getLogger(__name__)

# Create router for Region endpoints
router = Router(tags=["regions"])
# ============================================================================
# CRUD ENDPOINTS
# ============================================================================


@router.get("/", response=PaginatedResponse[RegionSchema], auth=AuthMiddleware)
def list_regions(
    request,
    page: int = 1,
    page_size: int = 20,
    search: Optional[str] = None,
    type: Optional[str] = None,
    parent_id: Optional[int] = None,
    is_active: Optional[bool] = None,
) -> PaginatedResponse[RegionSchema]:
    """
    List all regions with pagination and filtering.
    Requires authentication.
    """
    try:
        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Build queryset
        queryset = Region.objects.filter(deleted_at__isnull=True).select_related(
            "parent"
        )

        # Apply filters
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search)
                | Q(code__icontains=search)
                | Q(slug__icontains=search)
            )

        if type:
            queryset = queryset.filter(type=type)

        if parent_id is not None:
            if parent_id == 0:
                queryset = queryset.filter(parent__isnull=True)
            else:
                queryset = queryset.filter(parent_id=parent_id)

        if is_active is not None:
            queryset = queryset.filter(is_active=is_active)

        # Order by type and name
        queryset = queryset.order_by("type", "name")

        # Paginate
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Convert to response format
        regions = []
        for region in page_obj.object_list:
            regions.append(RegionSchema.from_orm(region))

        return PaginatedResponse[RegionSchema](
            regions=regions,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except Exception as e:
        logger.exception(f"Error listing regions: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/countries", response=List[RegionSchema], auth=AuthMiddleware)
def get_countries(request) -> List[RegionSchema]:
    """
    List all countries.
    Requires authentication.
    """
    try:
        regions = Region.objects.filter(
            deleted_at__isnull=True, parent__isnull=True, type=RegionType.COUNTRY
        )

        result = []
        for region in regions:
            result.append(RegionSchema.from_orm(region))

        return result

    except Exception as e:
        logger.exception(f"Error getting countries: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/states", response=List[RegionSchema], auth=AuthMiddleware)
def get_states(request, country_id: Optional[int] = None) -> List[RegionSchema]:
    """
    List all states/provinces, optionally filtered by country.
    Requires authentication.
    """
    try:
        queryset = Region.objects.filter(
            deleted_at__isnull=True, type=RegionType.STATE
        ).select_related("parent")

        if country_id:
            queryset = queryset.filter(parent_id=country_id)

        result = []
        for region in queryset:
            result.append(RegionSchema.from_orm(region))

        return result

    except Exception as e:
        logger.exception(f"Error getting states: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/cities", response=List[RegionSchema], auth=AuthMiddleware)
def get_cities(request, state_id: Optional[int] = None) -> List[RegionSchema]:
    """
    List all cities, optionally filtered by state.
    Requires authentication.
    """
    try:
        queryset = Region.objects.filter(
            deleted_at__isnull=True, type=RegionType.DISTRICT
        ).select_related("parent")

        if state_id:
            queryset = queryset.filter(parent_id=state_id)

        result = []
        for region in queryset:
            result.append(RegionSchema.from_orm(region))

        return result

    except Exception as e:
        logger.exception(f"Error getting cities: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/{region_id}", response=RegionSchema, auth=AuthMiddleware)
def get_region(request, region_id: int) -> RegionSchema:
    """
    Get a specific region by ID.
    Requires authentication.
    """
    try:
        region = get_object_or_404(
            Region.objects.select_related("parent"),
            id=region_id,
            deleted_at__isnull=True,
        )

        return RegionSchema.from_orm(region)

    except Exception as e:
        logger.exception(f"Error getting region {region_id}: {str(e)}")
        raise HttpError(500, "Internal server error")
