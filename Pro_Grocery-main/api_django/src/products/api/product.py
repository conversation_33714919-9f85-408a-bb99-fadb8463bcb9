"""
CRUD API endpoints for Product model.
"""

from ninja import Router, Schema
from ninja.errors import HttpError
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
from django.contrib.postgres.search import SearchQuery
import logging

from products.models import Product, Company, Category, MeasurementUnit, Region
from products.utils import search_products_trigram
from wholesalers.models import Item, RegionMinCharge, Wholesaler
from api.middleware import AuthMiddleware
from products.schemas import ProductSchema

logger = logging.getLogger(__name__)

# Create router for Product endpoints
router = Router(tags=["products"])

# ============================================================================
# SCHEMAS
# ============================================================================


class ProductIn(Schema):
    """Schema for creating a new product"""

    name: str
    title: str
    barcode: str
    slug: str
    description: str = ""
    company_id: Optional[int] = None
    category_id: Optional[int] = None
    unit: str = MeasurementUnit.PIECE
    unit_count: Decimal = Decimal("1.0")


class ProductUpdate(Schema):
    """Schema for updating product data"""

    name: Optional[str] = None
    title: Optional[str] = None
    barcode: Optional[str] = None
    slug: Optional[str] = None
    description: Optional[str] = None
    company_id: Optional[int] = None
    category_id: Optional[int] = None
    unit: Optional[str] = None
    unit_count: Optional[Decimal] = None


class CompanyOut(Schema):
    """Simplified company schema for product responses"""

    id: int
    name: str
    title: str
    slug: str


class CategoryOut(Schema):
    """Simplified category schema for product responses"""

    id: int
    name: str
    title: str
    slug: str


class PaginatedProductResponse(Schema):
    """Paginated response for products"""

    products: List[ProductSchema]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool


# ============================================================================
# SCHEMAS FOR PRODUCT WITH WHOLESALER ITEMS ENDPOINT
# ============================================================================


class RegionFilterIn(Schema):
    """Schema for region filter input"""

    region_id: int


class WholesalerItemOut(Schema):
    """Schema for wholesaler item output"""

    id: int
    base_price: Decimal
    inventory_count: int
    minimum_order_quantity: int
    maximum_order_quantity: Optional[int] = None
    price_expiry: datetime
    expires_at: datetime
    created_at: datetime
    updated_at: datetime


class WholesalerDetailOut(Schema):
    """Schema for detailed wholesaler information"""

    id: int
    category: str
    title: str
    username: str
    logo_url: Optional[str] = None
    background_image_url: Optional[str] = None
    created_at: datetime
    updated_at: datetime


class RegionMinChargeOut(Schema):
    """Schema for regional minimum charge information"""

    id: int
    min_charge: Decimal
    min_items: int
    region_id: int
    region_name: str


class WholesalerItemWithDetailsOut(Schema):
    """Schema combining wholesaler item with wholesaler details and regional pricing"""

    item: WholesalerItemOut
    wholesaler: WholesalerDetailOut
    regional_pricing: Optional[RegionMinChargeOut] = None


class ProductWithWholesalerItemsOut(Schema):
    """Schema for product with all associated wholesaler items"""

    id: int
    name: str
    title: str
    barcode: str
    slug: str
    description: str
    image_url: Optional[str] = None
    company: Optional[CompanyOut] = None
    category: Optional[CategoryOut] = None
    unit: str
    unit_count: float
    items_count: int
    created_at: datetime
    updated_at: datetime
    wholesaler_items: List[WholesalerItemWithDetailsOut]


# ============================================================================
# CRUD ENDPOINTS
# ============================================================================


@router.get("/", response=PaginatedProductResponse, auth=AuthMiddleware)
def list_products(
    request,
    page: int = 1,
    page_size: int = 20,
    search: Optional[str] = None,
    company_id: Optional[int] = None,
    category_id: Optional[int] = None,
    unit: Optional[str] = None,
    region_id: Optional[int] = None,
) -> PaginatedProductResponse:
    """
    List all products with pagination and filtering.
    Requires authentication.
    """
    try:
        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Build queryset
        queryset = Product.objects.filter(
            deleted_at__isnull=True, items_count__gt=0
        ).select_related("company", "category")

        # Apply filters
        if company_id:
            queryset = queryset.filter(company_id=company_id)

        if category_id:
            queryset = queryset.filter(category_id=category_id)

        if unit:
            queryset = queryset.filter(unit=unit)

        # Apply search
        if search:
            queryset = search_products_trigram(queryset, search)

        # Order by creation date (newest first)
        queryset = queryset.order_by("-items_count")

        # Paginate
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Convert to response format
        products: List[ProductSchema] = []
        for product in page_obj.object_list:
            items = Item.objects.filter(
                deleted_at__isnull=True,
                product=product,
                base_price__gt=0,
                wholesaler__region_min_charge__region_id=region_id,
                wholesaler__region_min_charge__deleted_at__isnull=True,
            )
            products.append(ProductSchema.from_orm(product, items=items))

        # filter products with no items
        products = [
            product for product in products if product.items and len(product.items) > 0
        ]

        return PaginatedProductResponse(
            products=products,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except Exception as e:
        logger.exception(f"Error listing products: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/{product_id}", response=ProductSchema, auth=AuthMiddleware)
def get_product(request, product_id: int) -> ProductSchema:
    """
    Get a specific product by ID.
    Requires authentication.
    """
    try:
        product = get_object_or_404(
            Product.objects.select_related("company", "category"),
            id=product_id,
            deleted_at__isnull=True,
        )

        return ProductSchema.from_orm(product)

    except Exception as e:
        logger.exception(f"Error getting product {product_id}: {str(e)}")
        raise HttpError(500, "Internal server error")
