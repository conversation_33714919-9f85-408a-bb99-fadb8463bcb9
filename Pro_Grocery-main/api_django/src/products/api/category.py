"""
CRUD API endpoints for Category model.
"""

from ninja import Router, Schema
from ninja.errors import HttpError
from typing import Optional, List
from datetime import datetime
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
import logging

from products.models import Category
from api.middleware import AuthMiddleware

logger = logging.getLogger(__name__)

# Create router for Category endpoints
router = Router(tags=["categories"])

# ============================================================================
# SCHEMAS
# ============================================================================

class CategoryIn(Schema):
    """Schema for creating a new category"""
    name: str
    title: str
    slug: str


class CategoryUpdate(Schema):
    """Schema for updating category data"""
    name: Optional[str] = None
    title: Optional[str] = None
    slug: Optional[str] = None


class CategoryOut(Schema):
    """Schema for category output"""
    id: int
    name: str
    title: str
    slug: str
    created_at: datetime
    updated_at: datetime


class PaginatedCategoryResponse(Schema):
    """Paginated response for categories"""
    categories: List[CategoryOut]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool


# ============================================================================
# CRUD ENDPOINTS
# ============================================================================

@router.get("/", response=PaginatedCategoryResponse, auth=AuthMiddleware)
def list_categories(
    request,
    page: int = 1,
    page_size: int = 20,
    search: Optional[str] = None,
) -> PaginatedCategoryResponse:
    """
    List all categories with pagination and filtering.
    Requires authentication.
    """
    try:
        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Build queryset
        queryset = Category.objects.filter(deleted_at__isnull=True)

        # Apply search
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(title__icontains=search) |
                Q(slug__icontains=search)
            )

        # Order by name
        queryset = queryset.order_by('name')

        # Paginate
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Convert to response format
        categories = []
        for category in page_obj.object_list:
            categories.append(CategoryOut(
                id=category.id,
                name=category.name,
                title=category.title,
                slug=category.slug,
                created_at=category.created_at,
                updated_at=category.updated_at,
            ))

        return PaginatedCategoryResponse(
            categories=categories,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except Exception as e:
        logger.exception(f"Error listing categories: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/{category_id}", response=CategoryOut, auth=AuthMiddleware)
def get_category(request, category_id: int) -> CategoryOut:
    """
    Get a specific category by ID.
    Requires authentication.
    """
    try:
        category = get_object_or_404(
            Category,
            id=category_id,
            deleted_at__isnull=True
        )

        return CategoryOut(
            id=category.id,
            name=category.name,
            title=category.title,
            slug=category.slug,
            created_at=category.created_at,
            updated_at=category.updated_at,
        )

    except Exception as e:
        logger.exception(f"Error getting category {category_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.post("/", response=CategoryOut, auth=AuthMiddleware)
def create_category(request, data: CategoryIn) -> CategoryOut:
    """
    Create a new category.
    Requires authentication.
    """
    try:
        # Check if slug already exists
        if Category.objects.filter(slug=data.slug).exists():
            raise HttpError(400, "Category with this slug already exists")

        # Create category
        category = Category.objects.create(
            name=data.name,
            title=data.title,
            slug=data.slug,
        )

        return CategoryOut(
            id=category.id,
            name=category.name,
            title=category.title,
            slug=category.slug,
            created_at=category.created_at,
            updated_at=category.updated_at,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error creating category: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.put("/{category_id}", response=CategoryOut, auth=AuthMiddleware)
def update_category(request, category_id: int, data: CategoryUpdate) -> CategoryOut:
    """
    Update a category.
    Requires authentication.
    """
    try:
        category = get_object_or_404(
            Category,
            id=category_id,
            deleted_at__isnull=True
        )

        # Update fields if provided
        if data.name is not None:
            category.name = data.name

        if data.title is not None:
            category.title = data.title

        if data.slug is not None:
            if Category.objects.filter(slug=data.slug).exclude(id=category_id).exists():
                raise HttpError(400, "Category with this slug already exists")
            category.slug = data.slug

        category.save()

        return CategoryOut(
            id=category.id,
            name=category.name,
            title=category.title,
            slug=category.slug,
            created_at=category.created_at,
            updated_at=category.updated_at,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error updating category {category_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.delete("/{category_id}", auth=AuthMiddleware)
def delete_category(request, category_id: int):
    """
    Soft delete a category.
    Requires authentication.
    """
    try:
        category = get_object_or_404(
            Category,
            id=category_id,
            deleted_at__isnull=True
        )

        # Check if category has products
        if category.products.filter(deleted_at__isnull=True).exists():
            raise HttpError(400, "Cannot delete category that has products")

        # Perform soft delete
        category.delete()

        return {"message": "Category deleted successfully"}

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error deleting category {category_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/{category_id}/products", auth=AuthMiddleware)
def get_category_products(request, category_id: int):
    """
    Get all products in a category.
    Requires authentication.
    """
    try:
        category = get_object_or_404(
            Category,
            id=category_id,
            deleted_at__isnull=True
        )

        products = category.products.filter(deleted_at__isnull=True).select_related('company')
        
        products_data = []
        for product in products:
            products_data.append({
                "id": product.id,
                "name": product.name,
                "title": product.title,
                "barcode": product.barcode,
                "slug": product.slug,
                "image_url": product.image.url if product.image else None,
                "company": {
                    "id": product.company.id,
                    "name": product.company.name,
                    "title": product.company.title,
                    "slug": product.company.slug,
                } if product.company else None,
                "unit": product.unit,
                "unit_count": product.unit_count,
            })

        return {
            "category": CategoryOut(
                id=category.id,
                name=category.name,
                title=category.title,
                slug=category.slug,
                created_at=category.created_at,
                updated_at=category.updated_at,
            ),
            "products": products_data,
            "products_count": len(products_data)
        }

    except Exception as e:
        logger.exception(f"Error getting category products {category_id}: {str(e)}")
        raise HttpError(500, "Internal server error")
