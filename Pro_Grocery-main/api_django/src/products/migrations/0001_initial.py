# Generated by Django 5.1.7 on 2025-03-26 09:03

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Category name, e.g. Electronics', max_length=255)),
                ('title', models.Char<PERSON>ield(help_text='Category title, e.g. Electronics (System name)', max_length=255)),
                ('slug', models.SlugField(unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Category',
                'verbose_name_plural': 'Categories',
            },
        ),
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Company name, e.g. Apple Inc.', max_length=255)),
                ('title', models.CharField(help_text='Company title, e.g. Apple Inc. (System name)', max_length=255)),
                ('slug', models.SlugField(unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Company',
                'verbose_name_plural': 'Companies',
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('title', models.CharField(max_length=255)),
                ('barcode', models.CharField(max_length=100, unique=True)),
                ('slug', models.SlugField(unique=True)),
                ('description', models.TextField(default='')),
                ('image', models.ImageField(blank=True, null=True, upload_to='products/images/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='products', to='products.category')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='products', to='products.company')),
            ],
            options={
                'verbose_name': 'Product',
                'verbose_name_plural': 'Products',
            },
        ),
        migrations.CreateModel(
            name='Region',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Region name, e.g. USA, California, San Francisco', max_length=255)),
                ('type', models.CharField(choices=[('COUNTRY', 'Country'), ('STATE', 'State/Province'), ('CITY', 'City'), ('DISTRICT', 'District'), ('OTHER', 'Other')], default='OTHER', help_text='Type of region', max_length=20)),
                ('code', models.CharField(blank=True, help_text='Optional region code (e.g. ISO country code, state abbreviation)', max_length=50)),
                ('slug', models.SlugField(unique=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('parent', models.ForeignKey(blank=True, help_text='Parent region. E.g. USA is parent of California, California is parent of San Francisco', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='products.region')),
            ],
            options={
                'verbose_name': 'Region',
                'verbose_name_plural': 'Regions',
                'indexes': [models.Index(fields=['parent', 'type'], name='products_re_parent__bd77e3_idx'), models.Index(fields=['type', 'is_active'], name='products_re_type_fb86e3_idx'), models.Index(fields=['code'], name='products_re_code_9dc382_idx')],
                'constraints': [models.CheckConstraint(condition=models.Q(('id', models.F('parent')), _negated=True), name='prevent_self_parent')],
            },
        ),
    ]
