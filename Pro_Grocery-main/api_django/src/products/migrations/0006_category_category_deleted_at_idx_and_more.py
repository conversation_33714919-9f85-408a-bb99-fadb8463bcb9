# Generated by Django 5.2.1 on 2025-07-25 12:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0005_product_items_count'),
    ]

    operations = [
        migrations.AddIndex(
            model_name='category',
            index=models.Index(fields=['deleted_at'], name='category_deleted_at_idx'),
        ),
        migrations.AddIndex(
            model_name='category',
            index=models.Index(fields=['name'], name='category_name_idx'),
        ),
        migrations.AddIndex(
            model_name='company',
            index=models.Index(fields=['deleted_at'], name='company_deleted_at_idx'),
        ),
        migrations.AddIndex(
            model_name='company',
            index=models.Index(fields=['name'], name='company_name_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['deleted_at'], name='product_deleted_at_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['company', 'deleted_at'], name='product_company_deleted_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['category', 'deleted_at'], name='product_category_deleted_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['created_at'], name='product_created_at_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['name'], name='product_name_idx'),
        ),
    ]
