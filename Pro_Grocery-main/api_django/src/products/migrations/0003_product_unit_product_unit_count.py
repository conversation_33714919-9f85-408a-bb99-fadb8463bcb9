# Generated by Django 5.2 on 2025-04-26 03:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0002_alter_product_image'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='unit',
            field=models.CharField(choices=[('PIECE', 'Piece'), ('KILOGRAM', 'Kilogram (kg)'), ('GRAM', 'Gram (g)'), ('LITER', 'Liter (L)'), ('MILLILITER', 'Milliliter (mL)'), ('METER', 'Meter (m)'), ('CENTIMETER', 'Centimeter (cm)'), ('BOX', 'Box'), ('CARTON', 'Carton'), ('PACK', 'Pack'), ('BOTTLE', 'Bottle'), ('CAN', 'Can'), ('DOZEN', 'Dozen'), ('PAIR', 'Pair'), ('OTHER', 'Other')], default='PIECE', help_text='Unit of measurement for this product', max_length=50),
        ),
        migrations.AddField(
            model_name='product',
            name='unit_count',
            field=models.DecimalField(decimal_places=2, default=1.0, help_text='Number of units in this product', max_digits=10),
        ),
    ]
