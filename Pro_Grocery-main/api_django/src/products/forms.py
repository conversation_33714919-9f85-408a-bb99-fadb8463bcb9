from django import forms
from .models import Company, Category, Product


class CompanyForm(forms.ModelForm):
    class Meta:
        model = Company
        fields = ["name", "title", "slug"]
        widgets = {
            "name": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Company Name"}
            ),
            "title": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Company Title"}
            ),
            "slug": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "company-slug"}
            ),
        }


class CategoryForm(forms.ModelForm):
    class Meta:
        model = Category
        fields = ["name", "title", "slug"]
        widgets = {
            "name": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Category Name"}
            ),
            "title": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Category Title"}
            ),
            "slug": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "category-slug"}
            ),
        }


class ProductForm(forms.ModelForm):
    class Meta:
        model = Product
        fields = [
            "name",
            "title",
            "barcode",
            "slug",
            "description",
            "company",
            "category",
            "image",
        ]
        widgets = {
            "name": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Product Name"}
            ),
            "title": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Product Title"}
            ),
            "barcode": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "Barcode"}
            ),
            "slug": forms.TextInput(
                attrs={"class": "form-control", "placeholder": "product-slug"}
            ),
            "description": forms.Textarea(
                attrs={
                    "class": "form-control",
                    "rows": 3,
                    "placeholder": "Product Description",
                }
            ),
            "company": forms.Select(attrs={"class": "form-select"}),
            "category": forms.Select(attrs={"class": "form-select"}),
            "image": forms.FileInput(attrs={"class": "form-control"}),
        }
