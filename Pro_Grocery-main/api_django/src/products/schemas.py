from typing import List, Optional

from wholesalers.schemas import ItemSchema
from .models import Region
from ninja.orm import ModelSchema
from ninja import Schema


class CompanySchema(Schema):
    id: int
    name: str
    title: str
    slug: str


class CategorySchema(Schema):
    id: int
    name: str
    title: str
    slug: str


class RegionSchema(ModelSchema):
    class Meta:
        model = Region
        fields = "__all__"
        depth = 1


class ProductSchema(Schema):
    id: int
    name: str
    title: str
    barcode: str
    slug: str
    description: str
    image_url: Optional[str] = None
    company_id: Optional[int] = None
    category_id: Optional[int] = None
    company: Optional[CompanySchema] = None
    category: Optional[CategorySchema] = None
    unit: str
    unit_count: float
    items: Optional[List["ItemSchema"]] = None  # Forward reference

    @classmethod
    def from_orm(cls, product, items=None):
        c = cls(
            id=product.id,
            name=product.name,
            title=product.title,
            barcode=product.barcode,
            slug=product.slug,
            description=product.description,
            image_url=product.image.url if product.image else None,
            company_id=product.company.id if product.company else None,
            category_id=product.category.id if product.category else None,
            company=CompanySchema.from_orm(product.company)
            if product.company
            else None,
            category=CategorySchema.from_orm(product.category)
            if product.category
            else None,
            unit=product.unit,
            unit_count=product.unit_count,
        )
        if items:
            c.items = [ItemSchema.from_orm(item) for item in items]
        return c
