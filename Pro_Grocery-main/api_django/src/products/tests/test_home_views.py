from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from django.test import TestCase
from django.utils import timezone
from ninja.testing import TestClient
from ..models import Product, Company, Category
from ..schemas import ProductWithPricingOut
from wholesalers.models import Wholesaler, Item

# Use the same router from home_views
from ..home_views import router


class HomeViewsTestCase(TestCase):
    def setUp(self):
        self.client = TestClient(router)
        
        # Create test data
        self.company = Company.objects.create(
            name="Test Company",
            title="Test Company Title",
            slug="test-company"
        )
        
        self.category = Category.objects.create(
            name="Test Category",
            title="Test Category Title",
            slug="test-category"
        )
        
        # Create products
        self.product1 = Product.objects.create(
            name="Test Product 1",
            title="Test Product 1 Title",
            barcode="1234567890123",
            slug="test-product-1",
            description="Test Description 1",
            company=self.company,
            category=self.category,
            unit="PIECE",
            unit_count=Decimal("1.0")
        )
        
        self.product2 = Product.objects.create(
            name="Test Product 2",
            title="Test Product 2 Title",
            barcode="1234567890124",
            slug="test-product-2",
            description="Test Description 2",
            company=self.company,
            category=self.category,
            unit="PIECE",
            unit_count=Decimal("1.0")
        )
        
        # Create wholesalers
        self.wholesaler1 = Wholesaler.objects.create(
            title="Wholesaler 1",
            username="wholesaler1",
            category="GROCERY"
        )
        
        self.wholesaler2 = Wholesaler.objects.create(
            title="Wholesaler 2",
            username="wholesaler2",
            category="GROCERY"
        )
        
        # Create items (product-wholesaler relationships)
        self.item1 = Item.objects.create(
            wholesaler=self.wholesaler1,
            product=self.product1,
            base_price=Decimal("10.00"),
            inventory_count=5,
            price_expiry=timezone.now() + timedelta(days=30)
        )
        
        self.item2 = Item.objects.create(
            wholesaler=self.wholesaler2,
            product=self.product1,  # Same product, different wholesaler
            base_price=Decimal("8.50"),
            inventory_count=3,
            price_expiry=timezone.now() + timedelta(days=30)
        )
        
        # Product 2 only available from one wholesaler
        self.item3 = Item.objects.create(
            wholesaler=self.wholesaler1,
            product=self.product2,
            base_price=Decimal("15.00"),
            inventory_count=10,
            price_expiry=timezone.now() + timedelta(days=30)
        )
    
    def test_get_popular_products(self):
        # Test the popular products endpoint
        response = self.client.get("/home/<USER>")
        self.assertEqual(response.status_code, 200)
        
        # Check that we got a list of products
        self.assertIsInstance(response.json(), list)
        
        # We should have 2 products (both have inventory)
        self.assertEqual(len(response.json()), 2)
        
        # Check product 1 (has multiple prices)
        product1 = next(p for p in response.json() if p["id"] == self.product1.id)
        self.assertEqual(product1["name"], "Test Product 1")
        
        # Should have the lowest price (8.50 from wholesaler2)
        self.assertEqual(Decimal(product1["lowest_price"]), Decimal("8.50"))
        
        # Should have one other price (10.00 from wholesaler1)
        self.assertEqual(len(product1["other_prices"]), 1)
        self.assertEqual(Decimal(product1["other_prices"][0]["price"]), Decimal("10.00"))
        
        # Check price range
        self.assertEqual(Decimal(product1["price_range"]["min"]), Decimal("8.50"))
        self.assertEqual(Decimal(product1["price_range"]["max"]), Decimal("10.00"))
        
        # Check product 2 (has only one price)
        product2 = next(p for p in response.json() if p["id"] == self.product2.id)
        self.assertEqual(product2["name"], "Test Product 2")
        
        # Should have the only price as lowest price
        self.assertEqual(Decimal(product2["lowest_price"]), Decimal("15.00"))
        
        # No other prices
        self.assertEqual(len(product2["other_prices"]), 0)
        
        # Price range should have same min and max
        self.assertEqual(Decimal(product2["price_range"]["min"]), Decimal("15.00"))
        self.assertEqual(Decimal(product2["price_range"]["max"]), Decimal("15.00"))
    
    def test_product_without_inventory_not_shown(self):
        # Create a product with no inventory
        product3 = Product.objects.create(
            name="Test Product 3",
            title="Test Product 3 Title",
            barcode="1234567890125",
            slug="test-product-3",
            description="Test Description 3",
            company=self.company,
            category=self.category,
            unit="PIECE",
            unit_count=Decimal("1.0")
        )
        
        # Create an item with zero inventory
        Item.objects.create(
            wholesaler=self.wholesaler1,
            product=product3,
            base_price=Decimal("20.00"),
            inventory_count=0,  # No inventory
            price_expiry=timezone.now() + timedelta(days=30)
        )
        
        response = self.client.get("/home/<USER>")
        self.assertEqual(response.status_code, 200)
        
        # Should still only have 2 products (the ones with inventory)
        self.assertEqual(len(response.json()), 2)
        
        # Product 3 should not be in the results
        product_ids = [p["id"] for p in response.json()]
        self.assertNotIn(product3.id, product_ids)
    
    def test_products_sorted_by_lowest_price(self):
        # The products should be sorted by lowest price (ascending)
        response = self.client.get("/home/<USER>")
        self.assertEqual(response.status_code, 200)
        
        # First product should have the lower lowest price (8.50)
        self.assertEqual(Decimal(response.json()[0]["lowest_price"]), Decimal("8.50"))
        
        # Second product should have the higher lowest price (15.00)
        self.assertEqual(Decimal(response.json()[1]["lowest_price"]), Decimal("15.00"))
