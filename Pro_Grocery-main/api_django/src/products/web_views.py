from django.shortcuts import render, redirect, get_object_or_404
from django.http import HttpResponse, JsonResponse
from django.contrib import messages
from django.views.decorators.http import require_http_methods
from django.template.loader import render_to_string
from django.views.decorators.csrf import ensure_csrf_cookie

from .models import Company, Category, Product
from .forms import CompanyForm, CategoryForm, ProductForm


def index_view(request):
    """Main landing page with quick stats and navigation"""
    company_count = Company.objects.filter(deleted_at__isnull=True).count()
    category_count = Category.objects.filter(deleted_at__isnull=True).count()
    product_count = Product.objects.filter(deleted_at__isnull=True).count()

    context = {
        "company_count": company_count,
        "category_count": category_count,
        "product_count": product_count,
    }
    return render(request, "products/index.html", context)


# Company views
@ensure_csrf_cookie
def company_list_view(request):
    """Display list of companies with HTMX integration"""
    companies = Company.objects.filter(deleted_at__isnull=True)

    # For HTMX requests, return only the company list portion
    if request.headers.get("HX-Request"):
        return render(
            request,
            "products/companies/partials/company_list.html",
            {"companies": companies},
        )

    # For regular requests, return the full page
    return render(request, "products/companies/list.html", {"companies": companies})


@require_http_methods(["GET", "POST"])
@ensure_csrf_cookie
def company_create_view(request):
    """Create a new company with HTMX form handling"""
    if request.method == "POST":
        form = CompanyForm(request.POST)
        if form.is_valid():
            company = form.save()

            # For HTMX requests, return a partial with the new company
            if request.headers.get("HX-Request"):
                companies = Company.objects.filter(deleted_at__isnull=True)
                html = render_to_string(
                    "products/companies/partials/company_list.html",
                    {"companies": companies},
                )
                return HttpResponse(html)

            messages.success(request, "Company created successfully!")
            return redirect("company_list")
    else:
        form = CompanyForm()

    # For HTMX requests, return only the form
    if request.headers.get("HX-Request"):
        return render(
            request, "products/companies/partials/company_form.html", {"form": form}
        )

    return render(request, "products/companies/create.html", {"form": form})


# Category views
@ensure_csrf_cookie
def category_list_view(request):
    """Display list of categories with HTMX integration"""
    categories = Category.objects.filter(deleted_at__isnull=True)

    # For HTMX requests, return only the category list portion
    if request.headers.get("HX-Request"):
        return render(
            request,
            "products/categories/partials/category_list.html",
            {"categories": categories},
        )

    return render(request, "products/categories/list.html", {"categories": categories})


@require_http_methods(["GET", "POST"])
@ensure_csrf_cookie
def category_create_view(request):
    """Create a new category with HTMX form handling"""
    if request.method == "POST":
        form = CategoryForm(request.POST)
        if form.is_valid():
            category = form.save()

            # For HTMX requests, return a partial with updated category list
            if request.headers.get("HX-Request"):
                categories = Category.objects.filter(deleted_at__isnull=True)
                html = render_to_string(
                    "products/categories/partials/category_list.html",
                    {"categories": categories},
                )
                return HttpResponse(html)

            messages.success(request, "Category created successfully!")
            return redirect("category_list")
    else:
        form = CategoryForm()

    # For HTMX requests, return only the form
    if request.headers.get("HX-Request"):
        return render(
            request, "products/categories/partials/category_form.html", {"form": form}
        )

    return render(request, "products/categories/create.html", {"form": form})


def category_detail_view(request, category_id):
    """View a single category's details and its products"""
    category = get_object_or_404(Category, id=category_id, deleted_at__isnull=True)
    products = Product.objects.filter(category=category, deleted_at__isnull=True)

    context = {
        "category": category,
        "products": products,
    }
    return render(request, "products/categories/detail.html", context)


# Product views
def product_list_view(request):
    """Display list of products with filtering options"""
    queryset = Product.objects.filter(deleted_at__isnull=True)

    # Handle filtering
    company_id = request.GET.get("company_id")
    category_id = request.GET.get("category_id")

    # Convert string IDs to integers for proper comparison in template
    selected_company = int(company_id) if company_id and company_id.isdigit() else None
    selected_category = (
        int(category_id) if category_id and category_id.isdigit() else None
    )

    if company_id:
        queryset = queryset.filter(company_id=company_id)

    if category_id:
        queryset = queryset.filter(category_id=category_id)

    companies = Company.objects.filter(deleted_at__isnull=True)
    categories = Category.objects.filter(deleted_at__isnull=True)

    context = {
        "products": queryset,
        "companies": companies,
        "categories": categories,
        "selected_company": selected_company,
        "selected_category": selected_category,
    }

    # For HTMX requests, return only the product list portion
    if request.headers.get("HX-Request"):
        return render(request, "products/products/partials/product_list.html", context)

    return render(request, "products/products/list.html", context)


@require_http_methods(["GET", "POST"])
def product_create_view(request):
    """Create a new product with file upload handling"""
    if request.method == "POST":
        form = ProductForm(request.POST, request.FILES)
        if form.is_valid():
            product = form.save()
            messages.success(request, "Product created successfully!")
            return redirect("product_list")
    else:
        form = ProductForm()

    return render(request, "products/products/create.html", {"form": form})


def product_detail_view(request, product_id):
    """View a single product's details"""
    product = get_object_or_404(Product, id=product_id, deleted_at__isnull=True)
    return render(request, "products/products/detail.html", {"product": product})


@require_http_methods(["GET", "POST"])
def product_update_view(request, product_id):
    """Update an existing product"""
    product = get_object_or_404(Product, id=product_id, deleted_at__isnull=True)

    if request.method == "POST":
        form = ProductForm(request.POST, request.FILES, instance=product)
        if form.is_valid():
            product = form.save()
            messages.success(request, "Product updated successfully!")
            return redirect("product_detail", product_id=product.id)
    else:
        form = ProductForm(instance=product)

    return render(
        request, "products/products/update.html", {"form": form, "product": product}
    )
