import logging
import time
import random
from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from products.models import Product
from products.utils import generate_embeddings_bulk

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Generate embeddings for all products"

    def add_arguments(self, parser):
        parser.add_argument(
            "--limit",
            type=int,
            default=-1,
            help="Limit the number of products to process (default: 0)",
        )
        parser.add_argument(
            "--batch-size",
            type=int,
            default=100,
            help="Number of products to process in each batch (default: 100)",
        )
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be processed without making changes",
        )
        parser.add_argument(
            "--force",
            action="store_true",
            help="Regenerate embeddings for all products, even if they already exist",
        )
        parser.add_argument(
            "--batch-delay",
            type=float,
            default=1.0,
            help="Delay in seconds between batches to avoid rate limits (default: 1.0)",
        )
        parser.add_argument(
            "--max-retries",
            type=int,
            default=3,
            help="Maximum number of retries for failed API calls (default: 3)",
        )

    def handle(self, *args, **options):
        batch_size = options["batch_size"]
        dry_run = options["dry_run"]
        force = options["force"]
        limit = options["limit"]
        batch_delay = options["batch_delay"]
        max_retries = options["max_retries"]

        # Get products that need embeddings
        products_query = Product.objects.filter(deleted_at=None, items_count__gt=0)

        if not force:
            products_query = products_query.filter(semantic_vector=None)

        if limit > 0:
            products_query = products_query[:limit]

        products_query = products_query.select_related("company", "category")

        total_products = products_query.count()

        if total_products == 0:
            self.stdout.write(
                self.style.SUCCESS("No products found that need embeddings.")
            )
            return

        self.stdout.write(
            f"Found {total_products} products to process "
            f"(batch size: {batch_size}, dry run: {dry_run}, batch delay: {batch_delay}s)"
        )

        if dry_run:
            self.stdout.write(
                self.style.WARNING("DRY RUN MODE - No changes will be made")
            )

        processed = 0
        failed = 0
        start_time = timezone.now()

        try:
            for i in range(0, total_products, batch_size):
                batch_products = list(products_query[i : i + batch_size])

                if not batch_products:
                    break

                self.stdout.write(
                    f"Processing batch {i // batch_size + 1}/{(total_products + batch_size - 1) // batch_size} "
                    f"({len(batch_products)} products)"
                )

                # Generate embeddings with rate limiting
                product_names = [
                    (
                        "اسم المنتج: "
                        + p.name
                        + " اسم المنتج: "
                        + p.title
                        + " اوصاف المنتج: "
                        + p.description
                        + " اسم الشركة: "
                        + p.company.name
                        + " اسم الفئة: "
                        + p.category.name
                    )
                    for p in batch_products
                ]

                embeddings_response = generate_embeddings_bulk(
                    product_names, max_retries=max_retries, base_delay=batch_delay
                )

                if embeddings_response is None:
                    self.stdout.write(
                        self.style.ERROR(
                            f"Failed to generate embeddings for batch starting at index {i}"
                        )
                    )
                    failed += len(batch_products)
                    continue

                # Update products with embeddings and search vectors
                if not dry_run:
                    try:
                        with transaction.atomic():
                            # First update semantic vectors using bulk_update
                            for j, product in enumerate(batch_products):
                                if j < len(embeddings_response):
                                    product.semantic_vector = embeddings_response[
                                        j
                                    ].embedding

                            Product.objects.bulk_update(
                                batch_products, ["semantic_vector"]
                            )

                            # Then update search vectors using raw SQL to avoid JOIN issues
                            from django.conf import settings
                            from django.db import connection

                            is_postgres = (
                                settings.DATABASES["default"]["ENGINE"]
                                == "django.db.backends.postgresql"
                            )

                            if is_postgres:
                                # PostgreSQL: Use native full-text search with subqueries
                                with connection.cursor() as cursor:
                                    for product in batch_products:
                                        cursor.execute(
                                            """
                                            UPDATE products_product
                                            SET search_vector =
                                                setweight(to_tsvector('arabic', COALESCE(name, '')), 'A') ||
                                                setweight(to_tsvector('arabic', COALESCE(title, '')), 'A') ||
                                                setweight(to_tsvector('arabic', COALESCE(description, '')), 'B') ||
                                                setweight(to_tsvector('arabic', COALESCE(
                                                    (SELECT name FROM products_company WHERE id = products_product.company_id), '')), 'C') ||
                                                setweight(to_tsvector('arabic', COALESCE(
                                                    (SELECT name FROM products_category WHERE id = products_product.category_id), '')), 'C')
                                            WHERE id = %s
                                            """,
                                            [product.id],
                                        )
                            else:
                                # SQLite: Store a JSON representation for basic search
                                import json

                                with connection.cursor() as cursor:
                                    for product in batch_products:
                                        # Get product data
                                        product_name = product.name
                                        product_title = product.title
                                        product_description = product.description
                                        company_name = (
                                            product.company.name
                                            if product.company
                                            else "Unknown Company"
                                        )
                                        category_name = (
                                            product.category.name
                                            if product.category
                                            else "Uncategorized"
                                        )

                                        # Create search vector as JSON
                                        search_vector = {
                                            "name": product_name.lower(),
                                            "title": product_title.lower(),
                                            "description": product_description.lower()
                                            if product_description
                                            else "",
                                            "company": company_name.lower(),
                                            "category": category_name.lower(),
                                            "tokens": list(
                                                set(
                                                    [
                                                        token.lower()
                                                        for token in product_name.split()
                                                    ]
                                                    + [
                                                        token.lower()
                                                        for token in product_title.split()
                                                    ]
                                                )
                                            ),
                                        }

                                        # Convert to JSON string for SQLite
                                        search_vector_json = json.dumps(search_vector)

                                        cursor.execute(
                                            "UPDATE products_product SET search_vector = %s WHERE id = %s",
                                            [search_vector_json, product.id],
                                        )

                    except Exception as e:
                        logger.error(
                            f"Error updating batch {i // batch_size + 1}: {str(e)}"
                        )
                        self.stdout.write(
                            self.style.ERROR(f"Failed to update batch: {str(e)}")
                        )
                        failed += len(batch_products)
                        continue

                processed += len(batch_products)

                # Progress update
                progress = (processed / total_products) * 100
                self.stdout.write(
                    f"Progress: {processed}/{total_products} ({progress:.1f}%) - "
                    f"Failed: {failed}"
                )

                # Rate limiting: Add delay between batches to avoid hitting rate limits
                if i + batch_size < total_products and not dry_run:
                    delay = batch_delay + random.uniform(0, 0.5)  # Add some jitter
                    self.stdout.write(
                        f"Waiting {delay:.2f} seconds before next batch..."
                    )
                    time.sleep(delay)

        except KeyboardInterrupt:
            self.stdout.write(self.style.WARNING("\nProcess interrupted by user"))
        except Exception as e:
            logger.error(f"Unexpected error in generate_embeddings command: {str(e)}")
            self.stdout.write(self.style.ERROR(f"Unexpected error: {str(e)}"))

        # Final summary
        end_time = timezone.now()
        duration = end_time - start_time

        self.stdout.write("\n" + "=" * 50)
        self.stdout.write("PROCESSING SUMMARY")
        self.stdout.write("=" * 50)
        self.stdout.write(f"Total products found: {total_products}")
        self.stdout.write(f"Successfully processed: {processed}")
        self.stdout.write(f"Failed: {failed}")
        self.stdout.write(f"Duration: {duration}")

        if failed > 0:
            self.stdout.write(
                self.style.ERROR(f"⚠️  {failed} products failed to process")
            )

        if processed > 0:
            self.stdout.write(
                self.style.SUCCESS(f"✅ Successfully processed {processed} products")
            )
