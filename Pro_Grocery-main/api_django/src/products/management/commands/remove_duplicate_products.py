import logging
from collections import defaultdict
from django.core.management.base import BaseCommand
from django.db import transaction
from products.models import Product

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Removes duplicate products keeping only the oldest record for each product name"

    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Perform a dry run without actually deleting products",
        )

    def handle(self, *args, **options):
        dry_run = options.get("dry_run", False)

        self.stdout.write(self.style.NOTICE("Scanning for duplicate products..."))

        # Group products by name
        name_groups = defaultdict(list)
        for product in Product.objects.all().order_by("created_at"):
            name_groups[product.name].append(product)

        # Identify duplicates (keeping the oldest one)
        duplicates_to_delete = []
        for name, products in name_groups.items():
            if len(products) > 1:
                # Keep the first (oldest) product, mark others for deletion
                duplicates_to_delete.extend(products[1:])

        # Report findings
        if duplicates_to_delete:
            self.stdout.write(
                self.style.WARNING(
                    f"Found {len(duplicates_to_delete)} duplicate products across {len([g for g in name_groups.values() if len(g) > 1])} names"
                )
            )

            # Show details of what will be deleted
            for product in duplicates_to_delete[:10]:  # Show first 10 as example
                self.stdout.write(
                    f"  - {product.name} (ID: {product.id}, Created: {product.created_at})"
                )

            if len(duplicates_to_delete) > 10:
                self.stdout.write(f"  - ... and {len(duplicates_to_delete) - 10} more")

            # Perform deletion if not dry run
            if not dry_run:
                with transaction.atomic():
                    # Use hard_delete to bypass soft deletion
                    for product in duplicates_to_delete:
                        product.hard_delete()

                    self.stdout.write(
                        self.style.SUCCESS(
                            f"Successfully deleted {len(duplicates_to_delete)} duplicate products"
                        )
                    )
            else:
                self.stdout.write(
                    self.style.NOTICE("Dry run completed. No products were deleted.")
                )
        else:
            self.stdout.write(self.style.SUCCESS("No duplicate products found"))
