from django.core.management.base import BaseCommand
from wholesalers.models import Item


class Command(BaseCommand):
    help = "Update all items with default values: inventory_count=50, minimum_order_quantity=1, maximum_order_quantity=5"

    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Show what would be updated without making changes",
        )
        parser.add_argument(
            "--batch-size",
            type=int,
            default=1000,
            help="Number of items to update in each batch (default: 1000)",
        )

    def handle(self, *args, **options):
        dry_run = options["dry_run"]
        batch_size = options["batch_size"]

        # Get all items that are not deleted
        items = Item.objects.filter(deleted_at__isnull=True)
        total_items = items.count()

        if total_items == 0:
            self.stdout.write(
                self.style.WARNING("No active items found in the database")
            )
            return

        self.stdout.write(
            self.style.SUCCESS(f"Found {total_items} active items to update")
        )

        if dry_run:
            self.stdout.write(
                self.style.WARNING("DRY RUN MODE - No changes will be made")
            )
            self.stdout.write("Would update the following fields for all items:")
            self.stdout.write("  - inventory_count: 50")
            self.stdout.write("  - minimum_order_quantity: 1")
            self.stdout.write("  - maximum_order_quantity: 5")
            return

        # Update items in batches using IDs
        updated_count = 0

        for i in items:
            i.inventory_count = 50
            i.minimum_order_quantity = 1
            i.maximum_order_quantity = 5
            i.save()
            updated_count += 1
            self.stdout.write(
                self.style.SUCCESS(f"Updated {updated_count}/{total_items} items")
            )

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully updated {updated_count} items with default values"
            )
        )
