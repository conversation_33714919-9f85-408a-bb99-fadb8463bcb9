from ninja import Schema
from psycopg2.extensions import Int
from wholesalers.models import RegionMinCharge, Item
from typing import Optional


class RegionMinChargeSchema(Schema):
    wholesaler_id: int
    region_id: int
    min_charge: float
    min_items: int

    @classmethod
    def from_orm(cls, region_min_charge: RegionMinCharge):
        return cls(
            wholesaler_id=region_min_charge.wholesaler_id,
            region_id=region_min_charge.region_id,
            min_charge=region_min_charge.min_charge,
            min_items=region_min_charge.min_items,
        )


class WholesalerSchema(Schema):
    id: int
    title: str
    username: str
    background_image: Optional[str]
    logo: Optional[str]


class ItemProductSchema(Schema):
    id: int
    title: str
    image: Optional[str] = None
    company_id: Optional[int] = None
    category_id: Optional[int] = None
    unit: str
    unit_count: float


class ItemSchema(Schema):
    id: int
    base_price: float
    inventory_count: int
    minimum_order_quantity: int
    maximum_order_quantity: Optional[int]
    product: Optional[ItemProductSchema] = None
    wholesaler_id: int
    wholesaler: Optional[WholesalerSchema] = None

    @classmethod
    def from_orm(
        cls, item: Item, include_product: bool = False, include_wholesaler: bool = False
    ):
        c = cls(
            id=item.id,
            base_price=item.base_price,
            inventory_count=item.inventory_count,
            minimum_order_quantity=item.minimum_order_quantity,
            maximum_order_quantity=item.maximum_order_quantity,
            wholesaler_id=item.wholesaler_id,
        )
        if include_product:
            c.product = ItemProductSchema.from_orm(item.product)
        if include_wholesaler:
            c.wholesaler = WholesalerSchema.from_orm(item.wholesaler)
        return c
