from typing import List
from django.shortcuts import get_object_or_404
from django.utils import timezone
from ninja import UploadedFile
from ninja.errors import HttpError

from products.models import Product, Region
from accounts.middleware import AuthMiddleware
from core.custom_router import CustomRouter
from .models import (
    Wholesaler,
    RegionMinCharge,
    Item,
    InventoryTransaction,
    InventoryTransactionType,
)
from .schemas import (
    WholesalerIn,
    WholesalerUpdate,
    WholesalerOut,
    RegionMinChargeIn,
    RegionMinChargeOut,
    ItemIn,
    ItemUpdate,
    ItemOut,
    InventoryTransactionIn,
    InventoryTransactionOut,
    InventoryUpdateResponse,
    RegionOut,
    ProductOut,
)

# Initialize router
router = CustomRouter(tags=["wholesalers"])


# Wholesaler Endpoints
@router.post("/wholesalers", response=WholesalerOut, auth=AuthMiddleware)
def create_wholesaler(request, data: WholesalerIn):
    """Create a new wholesaler store. Required when user first signs in as a wholesaler."""
    user = request.user

    # Check if user already has a wholesaler
    existing_wholesaler = Wholesaler.objects.filter(
        user=user, deleted_at__isnull=True
    ).first()

    if existing_wholesaler:
        raise HttpError(400, "User already has a wholesaler store")

    # Create new wholesaler
    wholesaler = Wholesaler.objects.create(
        user=user, category=data.category, title=data.title, username=data.username
    )

    return WholesalerOut(
        id=wholesaler.id,
        category=wholesaler.category,
        title=wholesaler.title,
        username=wholesaler.username,
        logo_url=wholesaler.logo.url if wholesaler.logo else None,
        background_image_url=wholesaler.background_image.url
        if wholesaler.background_image
        else None,
        created_at=wholesaler.created_at,
    )


@router.get("/wholesalers/me", response=WholesalerOut, auth=AuthMiddleware)
def get_my_wholesaler(request):
    """Get the current user's wholesaler store"""
    wholesaler = get_object_or_404(
        Wholesaler, user=request.user, deleted_at__isnull=True
    )
    return WholesalerOut(
        id=wholesaler.id,
        category=wholesaler.category,
        title=wholesaler.title,
        username=wholesaler.username,
        logo_url=wholesaler.logo.url if wholesaler.logo else None,
        background_image_url=wholesaler.background_image.url
        if wholesaler.background_image
        else None,
        created_at=wholesaler.created_at,
    )


@router.put("/wholesalers/me", response=WholesalerOut, auth=AuthMiddleware)
def update_my_wholesaler(request, data: WholesalerUpdate):
    """Update the current user's wholesaler store"""
    wholesaler = get_object_or_404(
        Wholesaler, user=request.user, deleted_at__isnull=True
    )

    # Update fields if provided
    update_data = data.dict(exclude_unset=True)

    for field, value in update_data.items():
        setattr(wholesaler, field, value)

    wholesaler.save()
    return WholesalerOut(
        id=wholesaler.id,
        category=wholesaler.category,
        title=wholesaler.title,
        username=wholesaler.username,
        logo_url=wholesaler.logo.url if wholesaler.logo else None,
        background_image_url=wholesaler.background_image.url
        if wholesaler.background_image
        else None,
        created_at=wholesaler.created_at,
    )


@router.post("/wholesalers/me/logo", response=WholesalerOut, auth=AuthMiddleware)
def upload_logo(request, file: UploadedFile):
    """Upload a logo for the wholesaler"""
    wholesaler = get_object_or_404(
        Wholesaler, user=request.user, deleted_at__isnull=True
    )

    wholesaler.logo = file
    wholesaler.save()

    return WholesalerOut(
        id=wholesaler.id,
        category=wholesaler.category,
        title=wholesaler.title,
        username=wholesaler.username,
        logo_url=wholesaler.logo.url if wholesaler.logo else None,
        background_image_url=wholesaler.background_image.url
        if wholesaler.background_image
        else None,
        created_at=wholesaler.created_at,
    )


@router.post("/wholesalers/me/background", response=WholesalerOut, auth=AuthMiddleware)
def upload_background(request, file: UploadedFile):
    """Upload a background image for the wholesaler"""
    wholesaler = get_object_or_404(
        Wholesaler, user=request.user, deleted_at__isnull=True
    )

    wholesaler.background_image = file
    wholesaler.save()

    return WholesalerOut(
        id=wholesaler.id,
        category=wholesaler.category,
        title=wholesaler.title,
        username=wholesaler.username,
        logo_url=wholesaler.logo.url if wholesaler.logo else None,
        background_image_url=wholesaler.background_image.url
        if wholesaler.background_image
        else None,
        created_at=wholesaler.created_at,
    )


# Region Min Charge Endpoints
@router.post(
    "/wholesalers/me/min-charges", response=RegionMinChargeOut, auth=AuthMiddleware
)
def create_min_charge(request, data: RegionMinChargeIn):
    """Set minimum charge for a specific region"""
    wholesaler = get_object_or_404(
        Wholesaler, user=request.user, deleted_at__isnull=True
    )

    region = get_object_or_404(Region, id=data.region_id, deleted_at__isnull=True)

    # Check if there's an existing min charge for this region
    existing = RegionMinCharge.objects.filter(
        wholesaler=wholesaler, region=region, deleted_at__isnull=True
    ).first()

    if existing:
        # Update existing
        existing.min_charge = data.min_charge
        existing.min_items = data.min_items
        existing.save()
        return RegionMinChargeOut(
            id=existing.id,
            region=RegionOut(
                id=region.id,
                name=region.name,
                type=region.type,
                code=region.code,
            ),
            min_charge=existing.min_charge,
            min_items=existing.min_items,
        )

    # Create new
    min_charge = RegionMinCharge.objects.create(
        wholesaler=wholesaler,
        region=region,
        min_charge=data.min_charge,
        min_items=data.min_items,
    )

    return RegionMinChargeOut(
        id=min_charge.id,
        region=RegionOut(
            id=region.id,
            name=region.name,
            type=region.type,
            code=region.code,
        ),
        min_charge=min_charge.min_charge,
        min_items=min_charge.min_items,
    )


@router.get(
    "/wholesalers/me/min-charges",
    response=List[RegionMinChargeOut],
    auth=AuthMiddleware,
)
def list_min_charges(request):
    """List all minimum charges for the current wholesaler"""
    wholesaler = get_object_or_404(
        Wholesaler, user=request.user, deleted_at__isnull=True
    )

    min_charges = RegionMinCharge.objects.filter(
        wholesaler=wholesaler, deleted_at__isnull=True
    )

    return [
        RegionMinChargeOut(
            id=charge.id,
            region=RegionOut(
                id=charge.region.id,
                name=charge.region.name,
                type=charge.region.type,
                code=charge.region.code,
            ),
            min_charge=charge.min_charge,
            min_items=charge.min_items,
        )
        for charge in min_charges
    ]


# Item (Product Pricing) Endpoints
@router.post("/wholesalers/me/items", response=ItemOut, auth=AuthMiddleware)
def create_item(request, data: ItemIn):
    """Add a product with base pricing to the wholesaler's inventory"""
    wholesaler = get_object_or_404(
        Wholesaler, user=request.user, deleted_at__isnull=True
    )

    product = get_object_or_404(Product, id=data.product_id, deleted_at__isnull=True)

    # Check if product already exists for this wholesaler
    existing = Item.objects.filter(
        wholesaler=wholesaler, product=product, deleted_at__isnull=True
    ).first()

    if existing:
        raise HttpError(400, "This product is already in your inventory")

    # Convert data to dict and exclude product_id
    item_data = data.dict(exclude={"product_id", "price_expiry"})

    # Create new item
    item = Item.objects.create(wholesaler=wholesaler, product=product, **item_data)

    # Set custom price expiry if provided
    if data.price_expiry:
        item.price_expiry = data.price_expiry
        item.expires_at = data.price_expiry
        item.save()

    # Create an inventory transaction if initial inventory count is greater than zero
    if item.inventory_count > 0:
        InventoryTransaction.objects.create(
            item=item,
            transaction_type=InventoryTransactionType.ADDITION,
            quantity=item.inventory_count,
            notes="Initial inventory on item creation",
        )

    return ItemOut(
        id=item.id,
        product=ProductOut(
            id=product.id,
            name=product.name,
            barcode=product.barcode,
            image_url=product.image.url
            if product.image and product.image.name
            else None,
        ),
        base_price=item.base_price,
        inventory_count=item.inventory_count,
        minimum_order_quantity=item.minimum_order_quantity,
        maximum_order_quantity=item.maximum_order_quantity,
        price_expiry=item.price_expiry,
        created_at=item.created_at,
    )


@router.get("/wholesalers/me/items", response=List[ItemOut], auth=AuthMiddleware)
def list_items(request):
    """List all products in the wholesaler's inventory"""
    wholesaler = get_object_or_404(
        Wholesaler, user=request.user, deleted_at__isnull=True
    )

    items = Item.objects.filter(wholesaler=wholesaler, deleted_at__isnull=True)

    return [
        ItemOut(
            id=item.id,
            product=ProductOut(
                id=item.product.id,
                name=item.product.name,
                barcode=item.product.barcode,
                image_url=item.product.image.url
                if item.product.image and item.product.image.name
                else None,
            ),
            base_price=item.base_price,
            inventory_count=item.inventory_count,
            minimum_order_quantity=item.minimum_order_quantity,
            maximum_order_quantity=item.maximum_order_quantity,
            price_expiry=item.price_expiry,
            created_at=item.created_at,
        )
        for item in items
    ]


@router.get("/wholesalers/me/items/{item_id}", response=ItemOut, auth=AuthMiddleware)
def get_item(request, item_id: int):
    """Get a specific product from the wholesaler's inventory"""
    wholesaler = get_object_or_404(
        Wholesaler, user=request.user, deleted_at__isnull=True
    )

    item = get_object_or_404(
        Item, id=item_id, wholesaler=wholesaler, deleted_at__isnull=True
    )

    return ItemOut(
        id=item.id,
        product=ProductOut(
            id=item.product.id,
            name=item.product.name,
            barcode=item.product.barcode,
            image_url=item.product.image.url
            if item.product.image and item.product.image.name
            else None,
        ),
        base_price=item.base_price,
        inventory_count=item.inventory_count,
        minimum_order_quantity=item.minimum_order_quantity,
        maximum_order_quantity=item.maximum_order_quantity,
        price_expiry=item.price_expiry,
        created_at=item.created_at,
    )


@router.put("/wholesalers/me/items/{item_id}", response=ItemOut, auth=AuthMiddleware)
def update_item(request, item_id: int, data: ItemUpdate):
    """Update a product's base pricing and inventory in the wholesaler's inventory"""
    wholesaler = get_object_or_404(
        Wholesaler, user=request.user, deleted_at__isnull=True
    )

    item = get_object_or_404(
        Item, id=item_id, wholesaler=wholesaler, deleted_at__isnull=True
    )

    # Store original inventory count to detect changes
    original_inventory_count = item.inventory_count

    # Update fields if provided
    update_data = data.dict(exclude_unset=True)

    # Handle price expiry separately to update both fields
    if "price_expiry" in update_data:
        price_expiry = update_data.pop("price_expiry")
        item.price_expiry = price_expiry
        item.expires_at = price_expiry

    # Handle inventory count separately to create a transaction
    if "inventory_count" in update_data:
        new_inventory_count = update_data.pop("inventory_count")
        inventory_difference = new_inventory_count - original_inventory_count

        # Only create a transaction if there's an actual change
        if inventory_difference != 0:
            # Determine transaction type based on whether inventory increased or decreased
            if inventory_difference > 0:
                transaction_type = InventoryTransactionType.ADDITION
                quantity = inventory_difference
            else:
                transaction_type = InventoryTransactionType.SUBTRACTION
                quantity = abs(inventory_difference)

            # Create transaction record
            InventoryTransaction.objects.create(
                item=item,
                transaction_type=transaction_type,
                quantity=quantity,
                notes="Inventory adjusted via item update",
            )

            # Update the inventory count
            item.inventory_count = new_inventory_count

    # Update remaining fields
    for field, value in update_data.items():
        setattr(item, field, value)

    item.save()
    return ItemOut(
        id=item.id,
        product=ProductOut(
            id=item.product.id,
            name=item.product.name,
            barcode=item.product.barcode,
            image_url=item.product.image.url
            if item.product.image and item.product.image.name
            else None,
        ),
        base_price=item.base_price,
        inventory_count=item.inventory_count,
        minimum_order_quantity=item.minimum_order_quantity,
        maximum_order_quantity=item.maximum_order_quantity,
        price_expiry=item.price_expiry,
        created_at=item.created_at,
    )


@router.delete("/wholesalers/me/items/{item_id}", auth=AuthMiddleware)
def delete_item(request, item_id: int):
    """Remove a product from the wholesaler's inventory (soft delete)"""
    wholesaler = get_object_or_404(
        Wholesaler, user=request.user, deleted_at__isnull=True
    )

    item = get_object_or_404(
        Item, id=item_id, wholesaler=wholesaler, deleted_at__isnull=True
    )

    item.delete()  # This is a soft delete as defined in the model

    return {"success": True, "message": "Item removed from inventory"}


@router.put("/wholesalers/me/items/{item_id}/expire", auth=AuthMiddleware)
def expire_item(request, item_id: int):
    """Immediately expire a product's pricing"""
    wholesaler = get_object_or_404(
        Wholesaler, user=request.user, deleted_at__isnull=True
    )

    item = get_object_or_404(
        Item, id=item_id, wholesaler=wholesaler, deleted_at__isnull=True
    )

    # Set expiry to now
    item.price_expiry = timezone.now()
    item.expires_at = timezone.now()
    item.save()

    return {"success": True, "message": "Item pricing expired"}


# Inventory Transaction Endpoints
@router.post(
    "/wholesalers/me/items/{item_id}/inventory",
    response=InventoryUpdateResponse,
    auth=AuthMiddleware,
)
def update_inventory(request, item_id: int, data: InventoryTransactionIn):
    """Add or remove inventory from an item"""
    wholesaler = get_object_or_404(
        Wholesaler, user=request.user, deleted_at__isnull=True
    )

    item = get_object_or_404(
        Item, id=item_id, wholesaler=wholesaler, deleted_at__isnull=True
    )

    # Validate transaction type
    if data.transaction_type not in [
        InventoryTransactionType.ADDITION,
        InventoryTransactionType.SUBTRACTION,
    ]:
        raise HttpError(400, "Invalid transaction type")

    # For subtraction, check if there's enough inventory
    if (
        data.transaction_type == InventoryTransactionType.SUBTRACTION
        and item.inventory_count < data.quantity
    ):
        raise HttpError(400, "Not enough inventory available")

    # Create transaction record
    InventoryTransaction.objects.create(
        item=item,
        transaction_type=data.transaction_type,
        quantity=data.quantity,
        notes=data.notes,
    )

    # Update inventory count
    if data.transaction_type == InventoryTransactionType.ADDITION:
        item.inventory_count += data.quantity
        message = f"Added {data.quantity} items to inventory"
    else:
        item.inventory_count -= data.quantity
        message = f"Removed {data.quantity} items from inventory"

    item.save()

    return InventoryUpdateResponse(
        success=True,
        message=message,
        new_inventory_count=item.inventory_count,
    )


@router.get(
    "/wholesalers/me/items/{item_id}/inventory",
    response=List[InventoryTransactionOut],
    auth=AuthMiddleware,
)
def list_inventory_transactions(request, item_id: int):
    """List all inventory transactions for an item"""
    wholesaler = get_object_or_404(
        Wholesaler, user=request.user, deleted_at__isnull=True
    )

    item = get_object_or_404(
        Item, id=item_id, wholesaler=wholesaler, deleted_at__isnull=True
    )

    transactions = InventoryTransaction.objects.filter(
        item=item, deleted_at__isnull=True
    ).order_by("-created_at")

    return [
        InventoryTransactionOut(
            id=transaction.id,
            transaction_type=transaction.transaction_type,
            quantity=transaction.quantity,
            notes=transaction.notes,
            created_at=transaction.created_at,
        )
        for transaction in transactions
    ]
