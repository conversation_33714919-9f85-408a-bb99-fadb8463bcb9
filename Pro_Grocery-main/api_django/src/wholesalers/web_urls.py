"""
URL configuration for wholesaler web dashboard
"""

from django.urls import path
from . import web_views

urlpatterns = [
    # Authentication
    path("login/", web_views.wholesaler_login, name="wholesaler_login"),
    path("logout/", web_views.wholesaler_logout, name="wholesaler_logout"),
    # Dashboard
    path("", web_views.wholesaler_dashboard, name="wholesaler_dashboard"),
    # Orders Management
    path("orders/", web_views.orders_list, name="orders_list"),
    path("orders/<int:order_id>/", web_views.order_detail, name="order_detail"),
    path("orders/<int:order_id>/print/", web_views.order_print, name="order_print"),
    path(
        "orders/<int:order_id>/update-status/",
        web_views.update_order_status,
        name="update_order_status",
    ),
    path(
        "orders/<int:order_id>/remove-item/<int:item_id>/",
        web_views.remove_order_item,
        name="remove_order_item",
    ),
    path(
        "orders/<int:order_id>/decrease-quantity/<int:item_id>/",
        web_views.decrease_order_item_quantity,
        name="decrease_order_item_quantity",
    ),
    # Items/Inventory Management
    path("items/", web_views.items_list, name="items_list"),
    path("items/<int:item_id>/", web_views.item_detail, name="item_detail"),
    path("items/<int:item_id>/edit/", web_views.item_edit, name="item_edit"),
    path("items/<int:item_id>/delete/", web_views.item_delete, name="item_delete"),
    # Multi-stage item creation workflow
    path("items/create/step1/", web_views.item_create_step1, name="item_create_step1"),
    path("items/create/step2/", web_views.item_create_step2, name="item_create_step2"),
    path("items/create/step3/", web_views.item_create_step3, name="item_create_step3"),
    # Bulk item adding workflow
    path("items/bulk-add/", web_views.bulk_add_items, name="bulk_add_items"),
    path(
        "items/bulk-add/process/", web_views.bulk_add_process, name="bulk_add_process"
    ),
    path("items/bulk-edit/", web_views.bulk_edit_items, name="bulk_edit_items"),
    # Analytics and reporting (to be implemented)
    # path('analytics/', web_views.analytics_dashboard, name='analytics_dashboard'),
    # path('reports/', web_views.reports_list, name='reports_list'),
    # path('reports/sales/', web_views.sales_report, name='sales_report'),
    # path('reports/inventory/', web_views.inventory_report, name='inventory_report'),
]
