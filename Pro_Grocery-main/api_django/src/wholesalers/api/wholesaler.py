"""
CRUD API endpoints for Wholesaler model.
"""

from ninja import Router, Schema
from ninja.errors import HttpError
from typing import Optional, List
from datetime import datetime
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
import logging

from core.schemas import PaginatedResponse
from wholesalers.models import Wholesaler
from wholesalers.schemas import WholesalerSchema

from accounts.models import CustomUser
from api.middleware import AuthMiddleware

logger = logging.getLogger(__name__)

# Create router for Wholesaler endpoints
router = Router(tags=["wholesalers"])

# ============================================================================
# CRUD ENDPOINTS
# ============================================================================


@router.get("/", response=PaginatedResponse[WholesalerSchema], auth=AuthMiddleware)
def list_wholesalers(
    request,
    page: int = 1,
    page_size: int = 20,
    search: Optional[str] = None,
    region_id: Optional[int] = None,
) -> PaginatedResponse:
    """
    List all wholesalers with pagination and filtering.
    Requires authentication.
    """
    try:
        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Build queryset
        queryset = Wholesaler.objects.filter(deleted_at__isnull=True)

        # Apply filters
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search)
                | Q(description__icontains=search)
                | Q(phone__icontains=search)
                | Q(address__icontains=search)
            )

        if region_id:
            queryset = queryset.filter(
                region_min_charge__region_id=region_id,
                region_min_charge__deleted_at__isnull=True,
            )

        # Order by creation date (newest first)
        queryset = queryset.order_by("-created_at")

        # Paginate
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Convert to response format
        wholesalers = [
            WholesalerSchema.from_orm(wholesaler) for wholesaler in page_obj.object_list
        ]

        return PaginatedResponse[WholesalerSchema](
            data=wholesalers,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except Exception as e:
        logger.exception(f"Error listing wholesalers: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/{wholesaler_id}", response=WholesalerSchema, auth=AuthMiddleware)
def get_wholesaler(request, wholesaler_id: int) -> WholesalerSchema:
    """
    Get a specific wholesaler by ID.
    Requires authentication.
    """
    try:
        wholesaler = get_object_or_404(
            Wholesaler.objects.select_related("user"),
            id=wholesaler_id,
            deleted_at__isnull=True,
        )

        return WholesalerSchema.from_orm(wholesaler)

    except Exception as e:
        logger.exception(f"Error getting wholesaler {wholesaler_id}: {str(e)}")
        raise HttpError(500, "Internal server error")
