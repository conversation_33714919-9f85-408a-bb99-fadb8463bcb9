"""
Gomla API endpoints for wholesaler dashboard functionality.
This module provides REST API endpoints that replicate the functionality
from the web views for the Flutter mobile app.
"""

from ninja import Router, Schema
from ninja.errors import HttpError
from typing import Optional, List
from datetime import datetime, timedelta
from decimal import Decimal
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q, Sum
from django.db import transaction
from django.utils import timezone
import logging

from api.middleware import AuthMiddleware
from wholesalers.models import (
    Wholesaler,
    Item,
    InventoryTransaction,
    InventoryTransactionType,
)
from stores.models import Order, OrderItem, OrderStatus
from products.models import Product, Company, Category
from products.utils import search_products_trigram
from core.settings import FEES_PERCENTAGE

logger = logging.getLogger(__name__)

# Create router for Gomla endpoints
router = Router(tags=["gomla"])

# ============================================================================
# SCHEMAS
# ============================================================================


class DashboardStatsOut(Schema):
    """Dashboard statistics schema"""

    total_orders: int
    pending_orders: int
    total_items: int
    low_stock_items: int


class DailySalesOut(Schema):
    """Daily sales data schema"""

    date: str
    sales: float


class StoreOut(Schema):
    """Store information schema"""

    id: int
    name: str
    address: str
    owner_username: str


class RecentOrderOut(Schema):
    """Recent order schema for dashboard"""

    id: int
    store: StoreOut
    status: str
    total_price: float
    created_at: datetime


class DashboardOut(Schema):
    """Complete dashboard response schema"""

    stats: DashboardStatsOut
    recent_orders: List[RecentOrderOut]
    daily_sales: List[DailySalesOut]


class OrderFilterParams(Schema):
    """Order filtering parameters"""

    status: Optional[str] = None
    search: Optional[str] = None
    date_from: Optional[str] = None
    date_to: Optional[str] = None
    page: int = 1
    page_size: int = 20


class OrderStatsOut(Schema):
    """Order statistics schema"""

    total_orders: int
    pending_orders: int
    completed_orders: int


class ProductItemOut(Schema):
    """Product information for order items"""

    id: int
    name: str
    title: str
    barcode: str
    company_name: Optional[str] = None
    category_name: Optional[str] = None


class OrderItemOut(Schema):
    """Order item schema"""

    id: int
    product: ProductItemOut
    quantity: int
    price_per_unit: float
    total_price: float


class OrderDetailOut(Schema):
    """Detailed order information schema"""

    id: int
    store: StoreOut
    status: str
    total_price: float
    fees: float
    products_total_price: float
    products_total_quantity: int
    created_at: datetime
    updated_at: datetime
    order_items: List[OrderItemOut]


class PaginatedOrdersOut(Schema):
    """Paginated orders response schema"""

    orders: List[OrderDetailOut]
    stats: OrderStatsOut
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool


class UpdateOrderStatusIn(Schema):
    """Update order status request schema"""

    status: str


class RemoveOrderItemIn(Schema):
    """Remove order item request schema"""

    reason: Optional[str] = "المنتج غير متوفر في المخزون"


class DecreaseOrderItemQuantityIn(Schema):
    """Decrease order item quantity request schema"""

    new_quantity: int
    reason: Optional[str] = "تقليل الكمية - توفر جزئي"


# ============================================================================
# HELPER FUNCTIONS
# ============================================================================


def get_wholesaler_from_request(request) -> Wholesaler:
    """Get wholesaler from authenticated user"""
    try:
        wholesaler = Wholesaler.objects.get(user=request.user, deleted_at__isnull=True)
        return wholesaler
    except Wholesaler.DoesNotExist:
        raise HttpError(403, "ليس لديك صلاحية للوصول إلى لوحة التحكم")


def build_order_response(order: Order) -> OrderDetailOut:
    """Build order response with all related data"""
    order_items = OrderItem.objects.filter(order=order).select_related(
        "product_item__product__company", "product_item__product__category"
    )

    items_data = []
    for item in order_items:
        product = item.product_item.product
        items_data.append(
            OrderItemOut(
                id=item.id,
                product=ProductItemOut(
                    id=product.id,
                    name=product.name,
                    title=product.title,
                    barcode=product.barcode,
                    company_name=product.company.name if product.company else None,
                    category_name=product.category.name if product.category else None,
                ),
                quantity=item.quantity,
                price_per_unit=float(item.price_per_unit),
                total_price=float(item.total_price),
            )
        )

    return OrderDetailOut(
        id=order.id,
        store=StoreOut(
            id=order.store.id,
            name=order.store.name,
            address=order.store.address,
            owner_username=order.store.owner.username,
        ),
        status=order.status,
        total_price=float(order.total_price),
        fees=float(order.fees),
        products_total_price=float(order.products_total_price),
        products_total_quantity=order.products_total_quantity,
        created_at=order.created_at,
        updated_at=order.updated_at,
        order_items=items_data,
    )


# ============================================================================
# DASHBOARD ENDPOINTS
# ============================================================================


@router.get("/dashboard", response=DashboardOut, auth=AuthMiddleware)
def get_dashboard(request) -> DashboardOut:
    """
    Get wholesaler dashboard data including statistics, recent orders, and sales data.
    Requires wholesaler authentication.
    """
    try:
        wholesaler = get_wholesaler_from_request(request)

        # Get dashboard statistics
        total_orders = Order.objects.filter(
            wholesaler=wholesaler, deleted_at__isnull=True
        ).count()
        pending_orders = Order.objects.filter(
            wholesaler=wholesaler, status="pending", deleted_at__isnull=True
        ).count()
        total_items = Item.objects.filter(
            wholesaler=wholesaler, deleted_at__isnull=True
        ).count()
        low_stock_items = Item.objects.filter(
            wholesaler=wholesaler, inventory_count__lt=10, deleted_at__isnull=True
        ).count()

        # Recent orders
        recent_orders_qs = (
            Order.objects.filter(wholesaler=wholesaler, deleted_at__isnull=True)
            .select_related("store", "store__owner")
            .order_by("-created_at")[:5]
        )

        recent_orders = []
        for order in recent_orders_qs:
            recent_orders.append(
                RecentOrderOut(
                    id=order.id,
                    store=StoreOut(
                        id=order.store.id,
                        name=order.store.name,
                        address=order.store.address,
                        owner_username=order.store.owner.username,
                    ),
                    status=order.status,
                    total_price=float(order.total_price),
                    created_at=order.created_at,
                )
            )

        # Sales data for the last 7 days
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=6)

        daily_sales = []
        for i in range(7):
            date = start_date + timedelta(days=i)
            sales = (
                Order.objects.filter(
                    wholesaler=wholesaler,
                    created_at__date=date,
                    deleted_at__isnull=True,
                ).aggregate(total=Sum("total_price"))["total"]
                or 0
            )
            daily_sales.append(
                DailySalesOut(date=date.strftime("%Y-%m-%d"), sales=float(sales))
            )

        return DashboardOut(
            stats=DashboardStatsOut(
                total_orders=total_orders,
                pending_orders=pending_orders,
                total_items=total_items,
                low_stock_items=low_stock_items,
            ),
            recent_orders=recent_orders,
            daily_sales=daily_sales,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error getting dashboard data: {str(e)}")
        raise HttpError(500, "Internal server error")


# ============================================================================
# ORDERS MANAGEMENT ENDPOINTS
# ============================================================================


@router.get("/orders", response=PaginatedOrdersOut, auth=AuthMiddleware)
def list_orders(
    request,
    page: int = 1,
    page_size: int = 20,
    status: Optional[str] = None,
    search: Optional[str] = None,
    date_from: Optional[str] = None,
    date_to: Optional[str] = None,
) -> PaginatedOrdersOut:
    """
    List all orders for the wholesaler with filtering and pagination.
    Requires wholesaler authentication.
    """
    try:
        wholesaler = get_wholesaler_from_request(request)

        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Base queryset
        orders = Order.objects.filter(
            wholesaler=wholesaler, deleted_at__isnull=True
        ).select_related("store", "store__owner")

        # Apply filters
        if status:
            orders = orders.filter(status=status)

        if search:
            orders = orders.filter(
                Q(store__name__icontains=search)
                | Q(store__owner__username__icontains=search)
                | Q(id__icontains=search)
            )

        if date_from:
            try:
                from_date = datetime.strptime(date_from, "%Y-%m-%d").date()
                orders = orders.filter(created_at__date__gte=from_date)
            except ValueError:
                pass

        if date_to:
            try:
                to_date = datetime.strptime(date_to, "%Y-%m-%d").date()
                orders = orders.filter(created_at__date__lte=to_date)
            except ValueError:
                pass

        # Order by creation date (newest first)
        orders = orders.order_by("-created_at")

        # Pagination
        paginator = Paginator(orders, page_size)
        page_obj = paginator.get_page(page)

        # Build order responses
        orders_data = []
        for order in page_obj.object_list:
            orders_data.append(build_order_response(order))

        # Get order statistics
        total_orders = Order.objects.filter(
            wholesaler=wholesaler, deleted_at__isnull=True
        ).count()
        pending_orders = Order.objects.filter(
            wholesaler=wholesaler, status="pending", deleted_at__isnull=True
        ).count()
        completed_orders = Order.objects.filter(
            wholesaler=wholesaler, status="delivered", deleted_at__isnull=True
        ).count()

        return PaginatedOrdersOut(
            orders=orders_data,
            stats=OrderStatsOut(
                total_orders=total_orders,
                pending_orders=pending_orders,
                completed_orders=completed_orders,
            ),
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error listing orders: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/orders/{order_id}", response=OrderDetailOut, auth=AuthMiddleware)
def get_order_detail(request, order_id: int) -> OrderDetailOut:
    """
    Get detailed information about a specific order.
    Requires wholesaler authentication.
    """
    try:
        wholesaler = get_wholesaler_from_request(request)
        order = get_object_or_404(
            Order, id=order_id, wholesaler=wholesaler, deleted_at__isnull=True
        )

        return build_order_response(order)

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error getting order detail {order_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.put("/orders/{order_id}/status", auth=AuthMiddleware)
def update_order_status(request, order_id: int, data: UpdateOrderStatusIn):
    """
    Update the status of an order.
    Requires wholesaler authentication.
    """
    try:
        wholesaler = get_wholesaler_from_request(request)
        order = get_object_or_404(
            Order, id=order_id, wholesaler=wholesaler, deleted_at__isnull=True
        )

        valid_statuses = ["pending", "processing", "shipped", "delivered", "cancelled"]

        if data.status not in valid_statuses:
            raise HttpError(400, "حالة الطلب غير صحيحة")

        old_status = order.status
        order.status = data.status
        order.save()

        # Create status message for logging
        status_names = {
            "pending": "في الانتظار",
            "processing": "قيد المعالجة",
            "shipped": "تم الشحن",
            "delivered": "تم التسليم",
            "cancelled": "ملغي",
        }

        logger.info(
            f'Order #{order.id} status updated from "{status_names.get(old_status, old_status)}" '
            f'to "{status_names.get(data.status, data.status)}" by wholesaler {wholesaler.id}'
        )

        return {
            "message": f"تم تحديث حالة الطلب #{order.id} بنجاح",
            "old_status": old_status,
            "new_status": data.status,
        }

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error updating order status {order_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.delete("/orders/{order_id}/items/{item_id}", auth=AuthMiddleware)
def remove_order_item(request, order_id: int, item_id: int, data: RemoveOrderItemIn):
    """
    Remove an item from an order and restore inventory.
    Requires wholesaler authentication.
    """
    try:
        wholesaler = get_wholesaler_from_request(request)

        # Get the order and verify ownership
        order = get_object_or_404(
            Order, id=order_id, wholesaler=wholesaler, deleted_at__isnull=True
        )

        # Get the order item
        order_item = get_object_or_404(OrderItem, id=item_id, order=order)

        with transaction.atomic():
            # Store item details for inventory restoration
            item = order_item.product_item
            quantity_to_restore = order_item.quantity
            item_total_price = order_item.total_price

            # Restore inventory
            item.inventory_count += quantity_to_restore
            item.save()

            # Create inventory transaction record
            InventoryTransaction.objects.create(
                item=item,
                transaction_type=InventoryTransactionType.ADDITION,
                quantity=quantity_to_restore,
                notes=f"Inventory restored from order #{order.id} item removal: {data.reason}",
            )

            # Delete the order item first
            order_item.delete()

            # Update order totals
            order.products_total_price -= item_total_price
            order.products_total_quantity -= quantity_to_restore

            # Ensure totals don't go below zero
            if order.products_total_price < 0:
                order.products_total_price = Decimal("0.00")
            if order.products_total_quantity < 0:
                order.products_total_quantity = 0

            # Recalculate fees based on new total
            fees_decimal = Decimal(str(FEES_PERCENTAGE)) / Decimal("100")
            order.fees = order.products_total_price * fees_decimal
            order.total_price = order.products_total_price + order.fees

            # Save order changes
            order.save()

            return {
                "message": f'تم حذف المنتج "{item.product.name}" من الطلب #{order.id} بنجاح. تم استرداد {quantity_to_restore} قطعة إلى المخزون.',
                "restored_quantity": quantity_to_restore,
                "updated_order": build_order_response(order),
            }

    except HttpError:
        raise
    except Exception as e:
        logger.exception(
            f"Error removing order item {item_id} from order {order_id}: {str(e)}"
        )
        raise HttpError(500, f"حدث خطأ أثناء حذف المنتج: {str(e)}")


@router.put("/orders/{order_id}/items/{item_id}/quantity", auth=AuthMiddleware)
def decrease_order_item_quantity(
    request, order_id: int, item_id: int, data: DecreaseOrderItemQuantityIn
):
    """
    Decrease the quantity of an item in an order and restore the difference to inventory.
    Requires wholesaler authentication.
    """
    try:
        wholesaler = get_wholesaler_from_request(request)

        # Get the order and verify ownership
        order = get_object_or_404(
            Order, id=order_id, wholesaler=wholesaler, deleted_at__isnull=True
        )

        # Get the order item
        order_item = get_object_or_404(OrderItem, id=item_id, order=order)

        current_quantity = order_item.quantity

        # Validation
        if data.new_quantity <= 0:
            raise HttpError(
                400,
                "الكمية الجديدة يجب أن تكون أكبر من صفر. استخدم خيار الحذف لإزالة المنتج كاملاً",
            )

        if data.new_quantity >= current_quantity:
            raise HttpError(400, "الكمية الجديدة يجب أن تكون أقل من الكمية الحالية")

        with transaction.atomic():
            # Calculate quantities and prices
            quantity_difference = current_quantity - data.new_quantity
            item = order_item.product_item
            price_per_unit = order_item.price_per_unit

            # Calculate old and new total prices for this item
            old_item_total = order_item.total_price
            new_item_total = price_per_unit * data.new_quantity
            price_difference = old_item_total - new_item_total

            # Update the order item
            order_item.quantity = data.new_quantity
            order_item.total_price = new_item_total
            order_item.save()

            # Restore inventory
            item.inventory_count += quantity_difference
            item.save()

            # Create inventory transaction record
            InventoryTransaction.objects.create(
                item=item,
                transaction_type=InventoryTransactionType.ADDITION,
                quantity=quantity_difference,
                notes=f"Inventory restored from order #{order.id} quantity reduction: {data.reason}. Reduced from {current_quantity} to {data.new_quantity}",
            )

            # Update order totals
            order.products_total_price -= price_difference
            order.products_total_quantity -= quantity_difference

            # Ensure totals don't go below zero
            if order.products_total_price < 0:
                order.products_total_price = Decimal("0.00")
            if order.products_total_quantity < 0:
                order.products_total_quantity = 0

            # Recalculate fees based on new total
            fees_decimal = Decimal(str(FEES_PERCENTAGE)) / Decimal("100")
            order.fees = order.products_total_price * fees_decimal
            order.total_price = order.products_total_price + order.fees

            # Save order changes
            order.save()

            return {
                "message": f'تم تقليل كمية "{item.product.name}" من {current_quantity} إلى {data.new_quantity}. تم استرداد {quantity_difference} قطعة إلى المخزون.',
                "old_quantity": current_quantity,
                "new_quantity": data.new_quantity,
                "restored_quantity": quantity_difference,
                "updated_order": build_order_response(order),
            }

    except HttpError:
        raise
    except Exception as e:
        logger.exception(
            f"Error decreasing order item quantity {item_id} in order {order_id}: {str(e)}"
        )
        raise HttpError(500, f"حدث خطأ أثناء تقليل الكمية: {str(e)}")


# ============================================================================
# ITEMS MANAGEMENT ENDPOINTS
# ============================================================================


class ItemFilterParams(Schema):
    """Item filtering parameters"""

    search: Optional[str] = None
    company: Optional[int] = None
    category: Optional[int] = None
    stock: Optional[str] = None  # "low", "out", "available"
    sort: Optional[str] = "-created_at"
    page: int = 1
    page_size: int = 20


class CompanyOut(Schema):
    """Company information schema"""

    id: int
    name: str
    title: str


class CategoryOut(Schema):
    """Category information schema"""

    id: int
    name: str
    title: str


class ItemProductOut(Schema):
    """Product information for items"""

    id: int
    name: str
    title: str
    barcode: str
    company: Optional[CompanyOut] = None
    category: Optional[CategoryOut] = None


class ItemOut(Schema):
    """Item information schema"""

    id: int
    product: ItemProductOut
    base_price: float
    inventory_count: int
    minimum_order_quantity: int
    maximum_order_quantity: Optional[int] = None
    created_at: datetime
    updated_at: datetime


class ItemStatsOut(Schema):
    """Item statistics schema"""

    total_items: int
    low_stock_items: int
    out_of_stock_items: int


class PaginatedItemsOut(Schema):
    """Paginated items response schema"""

    items: List[ItemOut]
    stats: ItemStatsOut
    companies: List[CompanyOut]
    categories: List[CategoryOut]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool


class ItemUpdateIn(Schema):
    """Update item request schema"""

    base_price: Optional[float] = None
    inventory_count: Optional[int] = None
    minimum_order_quantity: Optional[int] = None
    maximum_order_quantity: Optional[int] = None


def build_item_response(item: Item) -> ItemOut:
    """Build item response with all related data"""
    product = item.product
    return ItemOut(
        id=item.id,
        product=ItemProductOut(
            id=product.id,
            name=product.name,
            title=product.title,
            barcode=product.barcode,
            company=CompanyOut(
                id=product.company.id,
                name=product.company.name,
                title=product.company.title,
            )
            if product.company
            else None,
            category=CategoryOut(
                id=product.category.id,
                name=product.category.name,
                title=product.category.title,
            )
            if product.category
            else None,
        ),
        base_price=float(item.base_price),
        inventory_count=item.inventory_count,
        minimum_order_quantity=item.minimum_order_quantity,
        maximum_order_quantity=item.maximum_order_quantity,
        created_at=item.created_at,
        updated_at=item.updated_at,
    )


@router.get("/items", response=PaginatedItemsOut, auth=AuthMiddleware)
def list_items(
    request,
    page: int = 1,
    page_size: int = 20,
    search: Optional[str] = None,
    company: Optional[int] = None,
    category: Optional[int] = None,
    stock: Optional[str] = None,
    sort: Optional[str] = "-created_at",
) -> PaginatedItemsOut:
    """
    List all items/inventory for the wholesaler with filtering and pagination.
    Requires wholesaler authentication.
    """
    try:
        wholesaler = get_wholesaler_from_request(request)

        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Base queryset
        items = Item.objects.filter(
            wholesaler=wholesaler, deleted_at__isnull=True
        ).select_related("product__company", "product__category")

        # Apply filters
        if search:
            items = items.filter(
                Q(product__name__icontains=search)
                | Q(product__title__icontains=search)
                | Q(product__barcode__icontains=search)
            )

        if company:
            items = items.filter(product__company_id=company)

        if category:
            items = items.filter(product__category_id=category)

        if stock == "low":
            items = items.filter(inventory_count__lt=10)
        elif stock == "out":
            items = items.filter(inventory_count=0)
        elif stock == "available":
            items = items.filter(inventory_count__gt=0)

        # Apply sorting
        valid_sort_fields = [
            "-created_at",
            "created_at",
            "product__name",
            "-product__name",
            "inventory_count",
            "-inventory_count",
            "base_price",
            "-base_price",
        ]

        if sort in valid_sort_fields:
            items = items.order_by(sort)
        else:
            items = items.order_by("-created_at")

        # Pagination
        paginator = Paginator(items, page_size)
        page_obj = paginator.get_page(page)

        # Build item responses
        items_data = []
        for item in page_obj.object_list:
            items_data.append(build_item_response(item))

        # Get filter options
        companies = Company.objects.filter(deleted_at__isnull=True).order_by("name")
        categories = Category.objects.filter(deleted_at__isnull=True).order_by("name")

        companies_data = [
            CompanyOut(id=c.id, name=c.name, title=c.title) for c in companies
        ]
        categories_data = [
            CategoryOut(id=c.id, name=c.name, title=c.title) for c in categories
        ]

        # Get statistics
        total_items = Item.objects.filter(
            wholesaler=wholesaler, deleted_at__isnull=True
        ).count()
        low_stock_items = Item.objects.filter(
            wholesaler=wholesaler, inventory_count__lt=10, deleted_at__isnull=True
        ).count()
        out_of_stock_items = Item.objects.filter(
            wholesaler=wholesaler, inventory_count=0, deleted_at__isnull=True
        ).count()

        return PaginatedItemsOut(
            items=items_data,
            stats=ItemStatsOut(
                total_items=total_items,
                low_stock_items=low_stock_items,
                out_of_stock_items=out_of_stock_items,
            ),
            companies=companies_data,
            categories=categories_data,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error listing items: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/items/{item_id}", response=ItemOut, auth=AuthMiddleware)
def get_item_detail(request, item_id: int) -> ItemOut:
    """
    Get detailed information about a specific item.
    Requires wholesaler authentication.
    """
    try:
        wholesaler = get_wholesaler_from_request(request)
        item = get_object_or_404(
            Item, id=item_id, wholesaler=wholesaler, deleted_at__isnull=True
        )

        return build_item_response(item)

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error getting item detail {item_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.put("/items/{item_id}", response=ItemOut, auth=AuthMiddleware)
def update_item(request, item_id: int, data: ItemUpdateIn) -> ItemOut:
    """
    Update an existing item.
    Requires wholesaler authentication.
    """
    try:
        wholesaler = get_wholesaler_from_request(request)
        item = get_object_or_404(
            Item, id=item_id, wholesaler=wholesaler, deleted_at__isnull=True
        )

        with transaction.atomic():
            # Update item fields
            if data.base_price is not None:
                item.base_price = Decimal(str(data.base_price))

            if data.inventory_count is not None:
                # Create inventory transaction if inventory count changed
                if data.inventory_count != item.inventory_count:
                    if data.inventory_count > item.inventory_count:
                        transaction_type = InventoryTransactionType.ADDITION
                        quantity = data.inventory_count - item.inventory_count
                    else:
                        transaction_type = InventoryTransactionType.SUBTRACTION
                        quantity = item.inventory_count - data.inventory_count

                    # Create inventory transaction
                    InventoryTransaction.objects.create(
                        item=item,
                        quantity=quantity,
                        transaction_type=transaction_type,
                        notes="تم تحديث المخزون بواسطة التطبيق المحمول",
                    )

                    item.inventory_count = data.inventory_count

            if data.minimum_order_quantity is not None:
                item.minimum_order_quantity = data.minimum_order_quantity

            if data.maximum_order_quantity is not None:
                item.maximum_order_quantity = data.maximum_order_quantity

            item.save()

            return build_item_response(item)

    except HttpError:
        raise
    except (ValueError, TypeError) as e:
        logger.exception(f"Invalid data for item update {item_id}: {str(e)}")
        raise HttpError(400, "يرجى التأكد من صحة البيانات المدخلة")
    except Exception as e:
        logger.exception(f"Error updating item {item_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.delete("/items/{item_id}", auth=AuthMiddleware)
def delete_item(request, item_id: int):
    """
    Delete an item (soft delete).
    Requires wholesaler authentication.
    """
    try:
        wholesaler = get_wholesaler_from_request(request)
        item = get_object_or_404(
            Item, id=item_id, wholesaler=wholesaler, deleted_at__isnull=True
        )

        item.delete()  # This performs soft delete

        return {
            "message": f'تم حذف المنتج "{item.product.name}" بنجاح',
            "deleted_item_id": item_id,
        }

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error deleting item {item_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


# ============================================================================
# BULK OPERATIONS ENDPOINTS
# ============================================================================


class ProductForBulkOut(Schema):
    """Product information for bulk operations"""

    id: int
    name: str
    title: str
    barcode: str
    company: Optional[CompanyOut] = None
    category: Optional[CategoryOut] = None


class PaginatedProductsForBulkOut(Schema):
    """Paginated products response for bulk operations"""

    products: List[ProductForBulkOut]
    companies: List[CompanyOut]
    categories: List[CategoryOut]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool


class BulkAddItemsIn(Schema):
    """Bulk add items request schema"""

    product_ids: List[int]


class BulkAddResultOut(Schema):
    """Bulk add operation result"""

    created_items: List[ItemOut]
    failed_items: List[dict]
    success_count: int
    failure_count: int


class BulkUpdateItemsIn(Schema):
    """Bulk update items request schema"""

    item_ids: List[int]
    base_price: Optional[float] = None
    inventory_count: Optional[int] = None
    minimum_order_quantity: Optional[int] = None
    maximum_order_quantity: Optional[int] = None


class BulkUpdateResultOut(Schema):
    """Bulk update operation result"""

    updated_items: List[ItemOut]
    success_count: int
    failure_count: int


@router.get(
    "/products/available", response=PaginatedProductsForBulkOut, auth=AuthMiddleware
)
def list_available_products(
    request,
    page: int = 1,
    page_size: int = 20,
    search: Optional[str] = None,
    company: Optional[int] = None,
    category: Optional[int] = None,
) -> PaginatedProductsForBulkOut:
    """
    List products available for adding to inventory (not already in wholesaler's inventory).
    Requires wholesaler authentication.
    """
    try:
        wholesaler = get_wholesaler_from_request(request)

        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Get products excluding those already in wholesaler's inventory
        products = Product.objects.filter(deleted_at__isnull=True).select_related(
            "company", "category"
        )

        if search:
            products = search_products_trigram(products, search)

        if company:
            products = products.filter(company_id=company)

        if category:
            products = products.filter(category_id=category)

        # Exclude products already in wholesaler's inventory
        existing_product_ids = Item.objects.filter(
            wholesaler=wholesaler, deleted_at__isnull=True
        ).values_list("product_id", flat=True)

        products = products.exclude(id__in=existing_product_ids)

        # Pagination
        paginator = Paginator(products, page_size)
        page_obj = paginator.get_page(page)

        # Build product responses
        products_data = []
        for product in page_obj.object_list:
            products_data.append(
                ProductForBulkOut(
                    id=product.id,
                    name=product.name,
                    title=product.title,
                    barcode=product.barcode,
                    company=CompanyOut(
                        id=product.company.id,
                        name=product.company.name,
                        title=product.company.title,
                    )
                    if product.company
                    else None,
                    category=CategoryOut(
                        id=product.category.id,
                        name=product.category.name,
                        title=product.category.title,
                    )
                    if product.category
                    else None,
                )
            )

        # Get filter options
        companies = Company.objects.filter(deleted_at__isnull=True).order_by("name")
        categories = Category.objects.filter(deleted_at__isnull=True).order_by("name")

        companies_data = [
            CompanyOut(id=c.id, name=c.name, title=c.title) for c in companies
        ]
        categories_data = [
            CategoryOut(id=c.id, name=c.name, title=c.title) for c in categories
        ]

        return PaginatedProductsForBulkOut(
            products=products_data,
            companies=companies_data,
            categories=categories_data,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error listing available products: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.post("/items/bulk-add", response=BulkAddResultOut, auth=AuthMiddleware)
def bulk_add_items(request, data: BulkAddItemsIn) -> BulkAddResultOut:
    """
    Bulk add items with default values.
    Requires wholesaler authentication.
    """
    try:
        wholesaler = get_wholesaler_from_request(request)

        if not data.product_ids:
            raise HttpError(400, "يرجى اختيار منتج واحد على الأقل")

        created_items = []
        failed_items = []

        for product_id in data.product_ids:
            try:
                product = Product.objects.get(id=product_id, deleted_at__isnull=True)

                # Check if item already exists
                existing_item = Item.objects.filter(
                    wholesaler=wholesaler, product=product, deleted_at__isnull=True
                ).first()

                if existing_item:
                    failed_items.append(
                        {
                            "product_id": product_id,
                            "product_name": product.name,
                            "error": "المنتج موجود بالفعل في المخزون",
                        }
                    )
                    continue

                # Create item with default values
                item = Item.objects.create(
                    wholesaler=wholesaler,
                    product=product,
                    base_price=Decimal("0.00"),  # Default price to be updated later
                    inventory_count=0,  # Default inventory
                    minimum_order_quantity=1,  # Default minimum
                    maximum_order_quantity=None,  # No maximum limit
                )

                created_items.append(build_item_response(item))

            except Product.DoesNotExist:
                failed_items.append(
                    {"product_id": product_id, "error": "المنتج غير موجود"}
                )
            except Exception as e:
                failed_items.append(
                    {"product_id": product_id, "error": f"خطأ في الإنشاء: {str(e)}"}
                )

        return BulkAddResultOut(
            created_items=created_items,
            failed_items=failed_items,
            success_count=len(created_items),
            failure_count=len(failed_items),
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error bulk adding items: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.put("/items/bulk-update", response=BulkUpdateResultOut, auth=AuthMiddleware)
def bulk_update_items(request, data: BulkUpdateItemsIn) -> BulkUpdateResultOut:
    """
    Bulk update multiple items.
    Requires wholesaler authentication.
    """
    try:
        wholesaler = get_wholesaler_from_request(request)

        if not data.item_ids:
            raise HttpError(400, "يرجى اختيار منتج واحد على الأقل")

        updated_items = []
        success_count = 0
        failure_count = 0

        for item_id in data.item_ids:
            try:
                item = Item.objects.get(
                    id=item_id, wholesaler=wholesaler, deleted_at__isnull=True
                )

                # Update fields if provided
                if data.base_price is not None:
                    item.base_price = Decimal(str(data.base_price))

                if data.inventory_count is not None:
                    item.inventory_count = data.inventory_count

                if data.minimum_order_quantity is not None:
                    item.minimum_order_quantity = data.minimum_order_quantity

                if data.maximum_order_quantity is not None:
                    item.maximum_order_quantity = data.maximum_order_quantity

                item.save()
                updated_items.append(build_item_response(item))
                success_count += 1

            except (Item.DoesNotExist, ValueError, TypeError):
                failure_count += 1

        return BulkUpdateResultOut(
            updated_items=updated_items,
            success_count=success_count,
            failure_count=failure_count,
        )

    except HttpError:
        raise
    except Exception as e:
        logger.exception(f"Error bulk updating items: {str(e)}")
        raise HttpError(500, "Internal server error")
