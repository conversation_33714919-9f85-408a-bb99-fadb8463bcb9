import logging

from ninja.errors import HttpError
from typing import List
from django.core.paginator import Paginator

from wholesalers.models import RegionMinCharge
from api.middleware import AuthMiddleware
from wholesalers.schemas import RegionMinChargeSchema
from ninja import Router

logger = logging.getLogger(__name__)

# Create router for Wholesaler endpoints
router = Router(tags=["wholesalers"])


@router.get("/", response=List[RegionMinChargeSchema], auth=AuthMiddleware)
def list_min_charges(
    request, region_id: int, wholesaler_id: int
) -> List[RegionMinChargeSchema]:
    """
    List all min charges.
    Requires authentication.
    """
    try:
        # Build queryset
        queryset = RegionMinCharge.objects.filter(
            deleted_at__isnull=True, region_id=region_id, wholesaler_id=wholesaler_id
        )

        # Get page number and page size from request
        page = request.GET.get("page", 1)
        page_size = request.GET.get("page_size", 100)

        # Paginate queryset
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Serialize queryset
        min_charges = [
            RegionMinChargeSchema.from_orm(min_charge)
            for min_charge in page_obj.object_list
        ]

        # Return response
        return min_charges
    except Exception as e:
        logger.exception(f"Error listing min charges: {str(e)}")
        raise HttpError(500, "Internal server error")
