from errno import EILSEQ
from ninja import Router
from ninja.errors import HttpError
from typing import Optional, List
import logging

from django.core.paginator import Paginator
from django.db.models import Q

from api.middleware import AuthMiddleware
from wholesalers.models import Item
from wholesalers.schemas import ItemSchema
from core.schemas import PaginatedResponse

logger = logging.getLogger(__name__)

router = Router(tags=["items"])

# =============================================================================
# Endpoints
# =============================================================================


@router.get(
    "/",
    response=PaginatedResponse[ItemSchema],
    auth=AuthMiddleware,
)
def list_items(
    request,
    wholesaler_id: Optional[int] = None,
    page: int = 1,
    page_size: int = 100,
    search: Optional[str] = None,
    category_id: Optional[int] = None,
    product_id: Optional[int] = None,
    region_id: Optional[int] = None,
) -> PaginatedResponse[ItemSchema]:
    """
    List items for a specific wholesaler including product and category data.
    Excludes items with zero inventory or zero price.
    """
    try:
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        queryset = (
            Item.objects.filter(
                deleted_at__isnull=True,
                inventory_count__gt=0,
                base_price__gt=0,
            )
            .select_related("product", "product__category", "wholesaler")
            .order_by("product__name")
        )

        if wholesaler_id:
            queryset = queryset.filter(wholesaler_id=wholesaler_id)

        if search:
            queryset = queryset.filter(
                Q(product__name__icontains=search)
                | Q(product__title__icontains=search)
                | Q(product__barcode__icontains=search)
                | Q(product__description__icontains=search)
            )

        if region_id:
            queryset = queryset.filter(
                wholesaler__region_min_charge__region_id=region_id,
                wholesaler__region_min_charge__deleted_at__isnull=True,
            )

        if category_id:
            queryset = queryset.filter(product__category_id=category_id)

        if product_id:
            queryset = queryset.filter(product_id=product_id)

        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        items: List[ItemSchema] = []
        for item in page_obj.object_list:
            if product_id:
                items.append(ItemSchema.from_orm(item, include_wholesaler=True))
            else:
                items.append(ItemSchema.from_orm(item, include_product=True))

        return PaginatedResponse[ItemSchema](
            data=items,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except Exception as e:
        logger.exception(
            f"Error listing items for wholesaler {wholesaler_id}: {str(e)}"
        )
        raise HttpError(500, "Internal server error")


@router.get(
    "/by_id",
    response=List[ItemSchema],
    auth=AuthMiddleware,
)
def get_items_by_ids(
    request,
    ids: str,
    region_id: int,
):
    """
    Get a list of items by their IDs.
    IDs should be provided as a comma-separated string.
    e.g., /api/items/by_ids?ids=1,2,3
    """
    ids = ids.split(",")
    ids = [int(id) for id in ids]
    try:
        if not ids:
            raise HttpError(400, "No valid item IDs provided.")

        # Fetch items from the database where the ID is in the provided list
        # and the item is not marked as deleted.
        queryset = Item.objects.filter(
            id__in=ids,
            base_price__gt=0,
            deleted_at__isnull=True,
        ).select_related("product", "product__category", "wholesaler")

        # make sure all items in the same region
        if region_id:
            queryset = queryset.filter(
                wholesaler__region_min_charge__region_id=region_id,
                wholesaler__region_min_charge__deleted_at__isnull=True,
            )

        # Convert the queryset to a list of ItemSchema objects.
        # We include the full product and wholesaler data in the response.
        items_data = [
            ItemSchema.from_orm(item, include_product=True, include_wholesaler=True)
            for item in queryset
        ]

        return items_data

    except ValueError:
        # This handles cases where conversion to int might fail, though the list comprehension above is quite safe.
        raise HttpError(400, "Invalid ID format. IDs must be integers.")
    except Exception as e:
        logger.exception(f"Error retrieving items by IDs: {str(e)}")
        raise HttpError(500, "Internal server error")
