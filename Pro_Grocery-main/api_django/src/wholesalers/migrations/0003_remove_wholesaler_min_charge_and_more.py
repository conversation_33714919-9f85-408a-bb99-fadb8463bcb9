# Generated by Django 5.1.7 on 2025-04-07 20:12

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0002_alter_product_image'),
        ('wholesalers', '0002_alter_wholesaler_background_image_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='wholesaler',
            name='min_charge',
        ),
        migrations.RemoveField(
            model_name='wholesaler',
            name='min_items',
        ),
        migrations.CreateModel(
            name='RegionMinCharge',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('min_charge', models.DecimalField(decimal_places=2, max_digits=10)),
                ('min_items', models.IntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('region', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='wholesaler_min_charge', to='products.region')),
                ('wholesaler', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='region_min_charge', to='wholesalers.wholesaler')),
            ],
            options={
                'verbose_name': 'Region Min Charge',
                'verbose_name_plural': 'Region Min Charges',
                'indexes': [models.Index(fields=['wholesaler', 'region'], name='wholesalers_wholesa_092d33_idx'), models.Index(fields=['region', 'min_charge'], name='wholesalers_region__d345a5_idx')],
                'unique_together': {('wholesaler', 'region')},
            },
        ),
    ]
