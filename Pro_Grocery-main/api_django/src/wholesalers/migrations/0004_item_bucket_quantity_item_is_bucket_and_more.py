# Generated by Django 5.2 on 2025-04-15 14:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('wholesalers', '0003_remove_wholesaler_min_charge_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='item',
            name='bucket_quantity',
            field=models.IntegerField(blank=True, help_text='Number of pieces in a bucket', null=True),
        ),
        migrations.AddField(
            model_name='item',
            name='is_bucket',
            field=models.BooleanField(default=False, help_text='Whether this item is sold in buckets'),
        ),
        migrations.AddField(
            model_name='item',
            name='measurement_unit',
            field=models.CharField(choices=[('PIECE', 'Piece'), ('KILOGRAM', 'Kilogram (kg)'), ('GRAM', 'Gram (g)'), ('LITER', 'Liter (L)'), ('MILLILITER', 'Milliliter (mL)'), ('METER', 'Meter (m)'), ('CENTIMETER', 'Centimeter (cm)'), ('BOX', 'Box'), ('CARTON', 'Carton'), ('PACK', 'Pack'), ('BOTTLE', 'Bottle'), ('CAN', 'Can'), ('DOZEN', 'Dozen'), ('PAIR', 'Pair'), ('OTHER', 'Other')], default='PIECE', help_text='Unit of measurement for this item', max_length=50),
        ),
        migrations.AddField(
            model_name='item',
            name='pricing_type',
            field=models.CharField(choices=[('PER_PIECE', 'Per Piece'), ('OVERALL', 'Overall')], default='PER_PIECE', help_text='Whether the price is per piece or for the overall item', max_length=20),
        ),
    ]
