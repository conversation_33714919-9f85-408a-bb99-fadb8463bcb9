# Generated by Django 5.2 on 2025-07-08 12:03

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('wholesalers', '0009_alter_wholesaler_background_image_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='item',
            name='maximum_order_quantity',
            field=models.IntegerField(blank=True, help_text='Maximum quantity that can be ordered for this item (optional)', null=True, validators=[django.core.validators.MinValueValidator(1)]),
        ),
        migrations.AddField(
            model_name='item',
            name='minimum_order_quantity',
            field=models.IntegerField(default=1, help_text='Minimum quantity that can be ordered for this item', validators=[django.core.validators.MinValueValidator(1)]),
        ),
    ]
