# add signal to increase or descrease the irems count in Product model when i tem is added or deleted

from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from django.db import transaction

from .models import Item
from products.models import Product


@receiver(post_save, sender=Item)
def update_product_items_count_on_item_save(sender, instance, created, **kwargs):
    """
    Update the Product's items_count when an Item is created.
    """
    with transaction.atomic():
        # Use update() to avoid triggering signals
        Product.objects.filter(pk=instance.product.pk).update(
            items_count=Item.objects.filter(
                product=instance.product, deleted_at__isnull=True, base_price__gt=0
            ).count()
        )


@receiver(post_delete, sender=Item)
def update_product_items_count_on_item_delete(sender, instance, **kwargs):
    """
    Update the Product's items_count when an Item is hard-deleted.
    """
    with transaction.atomic():
        # Ensure items_count doesn't go below 0
        # Use update() to avoid triggering signals
        Product.objects.filter(pk=instance.product.pk).update(
            items_count=Item.objects.filter(
                product=instance.product, deleted_at__isnull=True
            ).count()
        )


@receiver(pre_save, sender=Item)
def update_product_items_count_on_soft_delete(sender, instance, **kwargs):
    """
    Update the Product's items_count when an Item is soft-deleted (deleted_at is set).
    This handles the case where the custom delete() method is called.
    """
    if instance.pk:  # Only for existing instances
        try:
            old_instance = Item.objects.get(pk=instance.pk)
            # Check if deleted_at changed from None to a timestamp (soft delete)
            if old_instance.deleted_at is None and instance.deleted_at is not None:
                with transaction.atomic():
                    # Ensure items_count doesn't go below 0
                    # Use update() to avoid triggering signals
                    Product.objects.filter(pk=instance.product.pk).update(
                        items_count=Item.objects.filter(
                            product=instance.product, deleted_at__isnull=True
                        ).count()
                    )
        except Item.DoesNotExist:
            # Item doesn't exist yet, this is a new instance
            pass
