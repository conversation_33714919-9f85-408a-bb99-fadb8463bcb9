"""
CRUD API endpoints for Order model.
"""

from ninja import Router
from ninja.errors import HttpError
from typing import Optional
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db import transaction
import logging


from stores.models import Order, Store, OrderItem
from wholesalers.models import (
    Wholesaler,
    Item,
    InventoryTransaction,
    InventoryTransactionType,
)
from accounts.utils import send_notification
from api.middleware import AuthMiddleware
from django.conf import settings
from schemas.orders import OrderIn, OrderOut, PaginatedOrderResponse

logger = logging.getLogger(__name__)

# Create router for Order endpoints
router = Router(tags=["orders"])

# ============================================================================
# SCHEMAS
# Consolidated in `src/schemas/orders.py`
# ============================================================================


# ============================================================================
# HELPER FUNCTIONS
# ============================================================================


def _build_order_response(order) -> OrderOut:
    """Helper function to build OrderOut response with explicit float fields."""
    # Build items with embedded product info
    order_items = []
    for oi in order.order_items.all():
        product = getattr(oi, "product", None)
        product_data = None
        if product is not None:
            product_data = {
                "id": product.id,
                "name": product.name,
                "title": product.title,
                "barcode": product.barcode,
                "slug": product.slug,
                "description": product.description,
                "image_url": str(product.image.url)
                if getattr(product, "image", None)
                else None,
                "unit": product.unit,
                "unit_count": float(product.unit_count)
                if product.unit_count is not None
                else 0.0,
            }

        order_items.append(
            {
                "id": oi.id,
                "order_id": oi.order_id,
                "product_item_id": oi.product_item_id,
                "quantity": oi.quantity,
                "price_per_unit": float(oi.price_per_unit),
                "total_price": float(oi.total_price),
                "product": product_data,
            }
        )

    wholesaler_data = {
        "id": order.wholesaler_id,
        "logo": str(order.wholesaler.logo.url) if order.wholesaler.logo else None,
        "title": order.wholesaler.title,
    }

    store_data = {
        "id": order.store_id,
        "name": order.store.name,
        "address": order.store.address,
    }

    return OrderOut(
        id=order.id,
        wholesaler=wholesaler_data,
        store=store_data,
        order_items=order_items,
        total_price=float(order.total_price),
        fees=float(order.fees),
        products_total_price=float(order.products_total_price),
        products_total_quantity=order.products_total_quantity,
        status=order.status,
        status_reason=order.status_reason,
        final_completed_price=float(order.final_completed_price)
        if order.final_completed_price is not None
        else None,
        deliver_at=order.deliver_at.isoformat() if order.deliver_at else None,
        completed_at=order.completed_at.isoformat() if order.completed_at else None,
        created_at=order.created_at.isoformat(),
    )


# ============================================================================
# CRUD ENDPOINTS
# ============================================================================


@router.get("/", response=PaginatedOrderResponse, auth=AuthMiddleware)
def list_orders(
    request,
    page: int = 1,
    page_size: int = 20,
    status: Optional[str] = None,
    store_id: Optional[int] = None,
    wholesaler_id: Optional[int] = None,
) -> PaginatedOrderResponse:
    """
    List all orders with pagination and filtering.
    Requires authentication.
    """
    try:
        # Validate page_size
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        # Build queryset
        queryset = (
            Order.objects.filter(deleted_at__isnull=True)
            .select_related("wholesaler", "store")
            .prefetch_related("order_items", "order_items__product_item__product")
        )

        # Apply filters
        if status:
            queryset = queryset.filter(status=status)

        if store_id:
            queryset = queryset.filter(store_id=store_id)

        if wholesaler_id:
            queryset = queryset.filter(wholesaler_id=wholesaler_id)

        # Order by creation date (newest first)
        queryset = queryset.order_by("-created_at")

        # Paginate
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        # Convert to response format
        orders = [_build_order_response(order) for order in page_obj.object_list]

        return PaginatedOrderResponse(
            orders=orders,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except Exception as e:
        logger.exception(f"Error listing orders: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.get("/{order_id}", response=OrderOut, auth=AuthMiddleware)
def get_order(request, order_id: int) -> OrderOut:
    """
    Get a specific order by ID.
    Requires authentication.
    """
    try:
        order = get_object_or_404(
            Order.objects.select_related("wholesaler", "store").prefetch_related(
                "order_items", "order_items__product_item__product"
            ),
            id=order_id,
            deleted_at__isnull=True,
        )

        return _build_order_response(order)

    except Exception as e:
        logger.exception(f"Error getting order {order_id}: {str(e)}")
        raise HttpError(500, "Internal server error")


@router.post("/", response=OrderOut, auth=AuthMiddleware)
def create_order(request, order_in: OrderIn) -> OrderOut:
    """
    Create a new order.
    Requires authentication.
    """
    with transaction.atomic():
        store = get_object_or_404(
            Store, id=order_in.store_id, owner=request.auth, deleted_at__isnull=True
        )

        wholesaler = get_object_or_404(
            Wholesaler, id=order_in.wholesaler_id, deleted_at__isnull=True
        )

        products_total_price = 0.00
        products_total_quantity = 0
        order_items_to_create = []

        for item_data in order_in.items:
            item = get_object_or_404(
                Item,
                id=item_data.item_id,
                wholesaler=wholesaler,
                deleted_at__isnull=True,
            )
            if not item:
                raise HttpError(404, f"Item not found for product: {item_data.item_id}")

            # Validate minimum order quantity
            if item_data.quantity < item.minimum_order_quantity:
                raise HttpError(
                    400,
                    f"الكمية المطلوبة للمنتج {item.product.title} أقل من الحد الأدنى المطلوب: {item_data.quantity} < {item.minimum_order_quantity}",
                )

            # Validate maximum order quantity (if set)
            if (
                item.maximum_order_quantity
                and item_data.quantity > item.maximum_order_quantity
            ):
                raise HttpError(
                    400,
                    f"الكمية المطلوبة للمنتج {item.product.title} أكبر من الحد الأقصى المسموح: {item_data.quantity} > {item.maximum_order_quantity}",
                )

            price_per_unit = item.base_price.__float__()
            total_item_price = price_per_unit * item_data.quantity
            products_total_price += total_item_price
            products_total_quantity += item_data.quantity

            # minus the quantity from the product
            if item.inventory_count < item_data.quantity:
                raise HttpError(
                    400,
                    f"الكمية المتاحة للمنتج {item.product.title} أقل من الكمية المطلوبة: {item.inventory_count} < {item_data.quantity}",
                )

            item.inventory_count -= item_data.quantity
            item.save()

            # add transaction to the item
            InventoryTransaction.objects.create(
                item=item,
                transaction_type=InventoryTransactionType.SUBTRACTION,
                quantity=item_data.quantity,
                notes="Inventory adjusted via order creation",
            )

            order_items_to_create.append(
                OrderItem(
                    product_item=item,
                    quantity=item_data.quantity,
                    price_per_unit=price_per_unit,
                    total_price=total_item_price,
                )
            )

        # Fees
        fees = products_total_price * (settings.FEES_PERCENTAGE / 100)

        total_price = products_total_price + fees

        # Parse deliver_at if provided
        deliver_at = None
        if order_in.deliver_at:
            from datetime import datetime

            deliver_at = datetime.fromisoformat(
                order_in.deliver_at.replace("Z", "+00:00")
            )

        order = Order.objects.create(
            store=store,
            wholesaler=wholesaler,
            total_price=total_price,
            fees=fees,
            deliver_at=deliver_at,
            products_total_price=products_total_price,
            products_total_quantity=products_total_quantity,
        )

        for order_item in order_items_to_create:
            order_item.order = order
        OrderItem.objects.bulk_create(order_items_to_create)

        # Send notification to the store owner
        try:
            send_notification(
                wholesaler.user.id,
                "طلب جديد",
                f"لديك طلب جديد من {store.name}",
            )
        except Exception:
            pass

        # Retrieve the created order with its items to return in the response
        created_order_with_items = (
            Order.objects.select_related("store")
            .prefetch_related("order_items__product_item__product")
            .get(id=order.id)
        )

        return _build_order_response(created_order_with_items)
