from django.contrib import admin
from django.utils.html import format_html
from django.db.models import Q
from django.urls import path
from django.shortcuts import render
from django.core.paginator import Paginator
from django.utils.safestring import mark_safe
from .models import Store, Order, OrderItem, OrderStatus


class StoreAdmin(admin.ModelAdmin):
    list_display = ("name", "owner", "city", "state", "country", "created_at")
    list_filter = ("country", "state", "city", "created_at")
    search_fields = (
        "name",
        "description",
        "address",
        "owner__username",
        "owner__email",
        "owner__phone",
    )
    raw_id_fields = ("owner",)
    autocomplete_fields = ["city", "state", "country"]
    readonly_fields = ("created_at", "updated_at")

    fieldsets = (
        (None, {"fields": ("name", "owner", "description")}),
        (
            "Location",
            {
                "fields": ("address", "city", "state", "country"),
            },
        ),
        (
            "Timestamps",
            {
                "fields": ("created_at", "updated_at", "deleted_at"),
                "classes": ("collapse",),
            },
        ),
    )


# Register the model with the admin site
admin.site.register(Store, StoreAdmin)


class OrderItemInline(admin.TabularInline):
    model = OrderItem
    extra = 0
    readonly_fields = ("product_item", "quantity")
    can_delete = False

    def has_add_permission(self, request, obj=None):
        return False


class OrderAdmin(admin.ModelAdmin):
    list_display = ("id", "store", "wholesaler", "created_at", "status")
    list_filter = ("status", "wholesaler", "created_at")
    search_fields = ("store__name", "wholesaler__title")
    raw_id_fields = ("store", "wholesaler")
    autocomplete_fields = ["store", "wholesaler"]
    readonly_fields = (
        "created_at",
        "updated_at",
        "deleted_at",
        "status_updated_at",
        "status_updated_by",
        "products_total_price",
        "products_total_quantity",
    )
    inlines = [OrderItemInline]

    fieldsets = (
        (
            None,
            {
                "fields": (
                    "store",
                    "wholesaler",
                    "total_price",
                    "fees",
                    "deliver_at",
                    "status",
                    "status_reason",
                    "status_updated_at",
                    "status_updated_by",
                    "products_total_price",
                    "products_total_quantity",
                )
            },
        ),
        (
            "Timestamps",
            {
                "fields": ("created_at", "updated_at", "deleted_at"),
                "classes": ("collapse",),
            },
        ),
    )


# Create proxy model for active orders
class ActiveOrder(Order):
    class Meta:
        proxy = True
        verbose_name = "Active Order"
        verbose_name_plural = "Active Orders"


class ActiveOrdersAdmin(admin.ModelAdmin):
    """Specialized admin for non-completed orders with enhanced features"""

    change_list_template = "admin/active_orders_change_list.html"

    def get_queryset(self, request):
        """Filter to show only non-completed orders"""
        qs = super().get_queryset(request)
        return (
            qs.filter(
                deleted_at__isnull=True,
                status__in=[
                    OrderStatus.PENDING,
                    OrderStatus.PROCESSING,
                    OrderStatus.SHIPPED,
                ],
            )
            .select_related("store", "store__owner", "wholesaler", "wholesaler__user")
            .prefetch_related("order_items__product_item__product")
        )

    def changelist_view(self, request, extra_context=None):
        """Custom changelist view with enhanced functionality"""
        # Get filter parameters
        wholesaler_filter = request.GET.get("wholesaler", "")
        status_filter = request.GET.get("status", "")
        date_from = request.GET.get("date_from", "")
        date_to = request.GET.get("date_to", "")
        search = request.GET.get("search", "")

        # Build queryset
        queryset = self.get_queryset(request)

        # Apply filters
        if wholesaler_filter:
            queryset = queryset.filter(wholesaler__id=wholesaler_filter)

        if status_filter:
            queryset = queryset.filter(status=status_filter)

        if date_from:
            try:
                from datetime import datetime

                date_from_obj = datetime.strptime(date_from, "%Y-%m-%d")
                queryset = queryset.filter(created_at__gte=date_from_obj)
            except ValueError:
                pass

        if date_to:
            try:
                from datetime import datetime

                date_to_obj = datetime.strptime(date_to, "%Y-%m-%d")
                queryset = queryset.filter(created_at__lte=date_to_obj)
            except ValueError:
                pass

        if search:
            queryset = queryset.filter(
                Q(store__name__icontains=search)
                | Q(store__owner__username__icontains=search)
                | Q(store__owner__phone__icontains=search)
                | Q(wholesaler__title__icontains=search)
                | Q(wholesaler__user__username__icontains=search)
                | Q(wholesaler__user__phone__icontains=search)
            )

        # Order by most recent first
        queryset = queryset.order_by("-created_at")

        # Pagination
        paginator = Paginator(queryset, 25)  # Show 25 orders per page
        page_number = request.GET.get("page")
        page_obj = paginator.get_page(page_number)

        # Get available wholesalers for filter dropdown
        from wholesalers.models import Wholesaler

        wholesalers = Wholesaler.objects.filter(deleted_at__isnull=True).values(
            "id", "title"
        )

        # Prepare context
        context = {
            "orders": page_obj,
            "page_obj": page_obj,
            "is_paginated": page_obj.has_other_pages(),
            "wholesalers": wholesalers,
            "status_choices": OrderStatus.choices,
            "filters": {
                "wholesaler": wholesaler_filter,
                "status": status_filter,
                "date_from": date_from,
                "date_to": date_to,
                "search": search,
            },
            "title": "Active Orders Management",
            "has_filters": any(
                [wholesaler_filter, status_filter, date_from, date_to, search]
            ),
            "total_orders": paginator.count,
            "current_page_count": len(page_obj),
            "total_pages": paginator.num_pages,
            "pending_count": queryset.filter(status=OrderStatus.PENDING).count(),
        }

        if extra_context:
            context.update(extra_context)

        return render(request, self.change_list_template, context)

    def get_urls(self):
        """Add custom URLs"""
        urls = super().get_urls()
        custom_urls = [
            path(
                "active/",
                self.admin_site.admin_view(self.changelist_view),
                name="active_orders",
            ),
        ]
        return custom_urls + urls

    # Display methods for list view
    def store_info(self, obj):
        return format_html(
            "<strong>{}</strong><br><small>Owner: {}</small>",
            obj.store.name,
            obj.store.owner.get_full_name() or obj.store.owner.username,
        )

    store_info.short_description = "Store"

    def wholesaler_info(self, obj):
        return format_html(
            "<strong>{}</strong><br><small>User: {}</small>",
            obj.wholesaler.title,
            obj.wholesaler.user.get_full_name() or obj.wholesaler.user.username,
        )

    wholesaler_info.short_description = "Wholesaler"

    def store_owner_phone(self, obj):
        """Display store owner's phone number"""
        try:
            phone = obj.store.owner.phone
            if phone:
                return format_html(
                    '<span class="phone-number" data-phone="{}">{}</span>',
                    phone,
                    phone,
                )
            else:
                return "No phone"
        except Exception as e:
            return f"Error: {str(e)}"

    store_owner_phone.short_description = "Store Owner Phone"

    def wholesaler_owner_phone(self, obj):
        """Display wholesaler owner's phone number"""
        try:
            phone = obj.wholesaler.user.phone
            if phone:
                return format_html(
                    '<span class="phone-number" data-phone="{}">{}</span>',
                    phone,
                    phone,
                )
            else:
                return "No phone"
        except Exception as e:
            return f"Error: {str(e)}"

    wholesaler_owner_phone.short_description = "Wholesaler Owner Phone"

    def phone_numbers(self, obj):
        store_phone = obj.store.owner.phone or "N/A"
        wholesaler_phone = obj.wholesaler.user.phone or "N/A"
        return format_html(
            """
            <div class="phone-numbers">
                <div class="phone-item">
                    <strong>Store:</strong> 
                    <span class="phone-number" data-phone="{}">{}</span>
                </div>
                <div class="phone-item">
                    <strong>Wholesaler:</strong>
                    <span class="phone-number" data-phone="{}">{}</span>
                </div>
            </div>
            """,
            store_phone,
            store_phone,
            store_phone,
            wholesaler_phone,
            wholesaler_phone,
            wholesaler_phone,
        )

    phone_numbers.short_description = "Phone Numbers"

    def order_details(self, obj):
        return format_html(
            """
            <div class="order-details">
                <div><strong>Total:</strong> ${:.2f}</div>
                <div><strong>Items:</strong> {}</div>
                <div><strong>Status:</strong> <span class="status-badge status-{}">{}</span></div>
                <div><strong>Date:</strong> {}</div>
            </div>
            """,
            obj.total_price,
            obj.products_total_quantity,
            obj.status.lower(),
            obj.get_status_display(),
            obj.created_at.strftime("%Y-%m-%d %H:%M"),
        )

    order_details.short_description = "Order Details"

    # Admin configuration
    list_display = (
        "id",
        "store_info",
        "wholesaler_info",
        "store_owner_phone",
        "wholesaler_owner_phone",
        "order_details",
    )
    list_display_links = ("id",)
    list_per_page = 25
    ordering = ["-created_at"]

    # Remove the standard filters since we have custom ones
    list_filter = []
    search_fields = []

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    class Media:
        css = {"all": ("admin/css/active_orders.css",)}
        js = ("admin/js/active_orders.js",)


# Register both admin classes
admin.site.register(Order, OrderAdmin)
admin.site.register(ActiveOrder, ActiveOrdersAdmin)
