### Akedly API Tests with Environment Variables
# This file contains HTTP requests to test the Akedly API directly
# Create a .env file or set up an environment in your HTTP client (like REST Client for VS Code)

@baseUrl = https://api.akedly.io/api/v1

# Replace these with your actual values or set up environment variables
@apiKey = {{$dotenv AKEDLY_API_KEY}}
@pipelineId = {{$dotenv AKEDLY_PIPELINE_ID}}
@phoneNumber = {{$dotenv TEST_PHONE_NUMBER}}

# Variables to store response values (for REST Client in VS Code)
@transactionId = {{createTransaction.response.body.data.transactionID}}
@transactionReqId = {{activateTransaction.response.body.data._id}}

### 1. Create a new transaction
# This initiates the OTP verification process
# @name createTransaction
POST {{baseUrl}}/transactions
Content-Type: application/json

{
  "APIKey": "{{apiKey}}",
  "pipelineID": "{{pipelineId}}",
  "verificationAddress": {
    "phoneNumber": "{{phoneNumber}}"
  }
}

### 2. Activate the transaction
# After creating a transaction, you need to activate it to send the OTP
# @name activateTransaction
POST {{baseUrl}}/transactions/activate/{{transactionId}}

### 3. Verify the OTP
# After receiving the OTP on the phone, verify it
# Replace the OTP value with the code you received
POST {{baseUrl}}/transactions/verify/{{transactionReqId}}
Content-Type: application/json

{
  "otp": "123456"
}

### 4. Check transaction status (optional)
# You can check the status of a transaction
GET {{baseUrl}}/transactions/{{transactionId}}

### Sample Response Structures

# 1. Create Transaction Response:
# {
#   "status": "success",
#   "data": {
#     "transactionID": "abc123xyz",
#     "status": "pending",
#     "createdAt": "2023-05-15T12:34:56.789Z"
#   }
# }

# 2. Activate Transaction Response:
# {
#   "status": "success",
#   "data": {
#     "_id": "req123xyz",
#     "status": "active",
#     "channel": "whatsApp", // or "sms"
#     "message": "OTP sent successfully"
#   }
# }

# 3. Verify OTP Response:
# {
#   "status": "success",
#   "data": {
#     "verified": true,
#     "message": "OTP verified successfully"
#   }
# }

# 4. Transaction Status Response:
# {
#   "status": "success",
#   "data": {
#     "transactionID": "abc123xyz",
#     "status": "completed", // or "pending", "active", "failed"
#     "createdAt": "2023-05-15T12:34:56.789Z",
#     "completedAt": "2023-05-15T12:40:00.000Z"
#   }
# }
