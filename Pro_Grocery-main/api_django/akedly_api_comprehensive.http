# Akedly API Comprehensive Test File
# ===============================
#
# This file provides a comprehensive set of HTTP requests to test the Akedly API
# for OTP (One-Time Password) verification services.
#
# Akedly is a service that provides phone number verification through OTP codes
# sent via WhatsApp or SMS.
#
# Documentation: https://api.akedly.io/docs (if available)

# Environment Variables
# --------------------
# Create a .env file with these variables or set them in your HTTP client

@baseUrl = https://api.akedly.io/api/v1

# Replace these with your actual values or set up environment variables
@apiKey = {{$dotenv AKEDLY_API_KEY}}
@pipelineId = {{$dotenv AKEDLY_PIPELINE_ID}}
@phoneNumber = {{$dotenv TEST_PHONE_NUMBER}}

# Variables to store response values (for REST Client in VS Code)
@transactionId = {{createTransaction.response.body.data.transactionID}}
@transactionReqId = {{activateTransaction.response.body.data._id}}

# =================================================================
# 1. TRANSACTION MANAGEMENT
# =================================================================

### 1.1 Create a new transaction
# This initiates the OTP verification process
# @name createTransaction
POST {{baseUrl}}/transactions
Content-Type: application/json

{
  "APIKey": "{{apiKey}}",
  "pipelineID": "{{pipelineId}}",
  "verificationAddress": {
    "phoneNumber": "{{phoneNumber}}"
  }
}

### 1.2 Activate the transaction
# After creating a transaction, you need to activate it to send the OTP
# @name activateTransaction
POST {{baseUrl}}/transactions/activate/{{transactionId}}

### 1.3 Verify the OTP
# After receiving the OTP on the phone, verify it
# Replace the OTP value with the code you received
POST {{baseUrl}}/transactions/verify/{{transactionReqId}}
Content-Type: application/json

{
  "otp": "123456"
}

### 1.4 Check transaction status
# You can check the status of a transaction
GET {{baseUrl}}/transactions/{{transactionId}}

# =================================================================
# 2. ADVANCED USAGE (if supported by Akedly API)
# =================================================================

### 2.1 Create transaction with additional options
# Some APIs allow additional configuration options
POST {{baseUrl}}/transactions
Content-Type: application/json

{
  "APIKey": "{{apiKey}}",
  "pipelineID": "{{pipelineId}}",
  "verificationAddress": {
    "phoneNumber": "{{phoneNumber}}"
  },
  "options": {
    "language": "en",
    "channel": "whatsapp",  // Prefer WhatsApp if available
    "expiryMinutes": 10     // OTP expires after 10 minutes
  }
}

### 2.2 Resend OTP (if supported)
# Some APIs allow resending the OTP if the user didn't receive it
POST {{baseUrl}}/transactions/resend/{{transactionId}}
Content-Type: application/json

{
  "APIKey": "{{apiKey}}"
}

# =================================================================
# 3. ERROR HANDLING TESTS
# =================================================================

### 3.1 Create transaction with invalid API key
# This should return an authentication error
POST {{baseUrl}}/transactions
Content-Type: application/json

{
  "APIKey": "invalid_api_key",
  "pipelineID": "{{pipelineId}}",
  "verificationAddress": {
    "phoneNumber": "{{phoneNumber}}"
  }
}

### 3.2 Create transaction with invalid phone number
# This should return a validation error
POST {{baseUrl}}/transactions
Content-Type: application/json

{
  "APIKey": "{{apiKey}}",
  "pipelineID": "{{pipelineId}}",
  "verificationAddress": {
    "phoneNumber": "invalid_phone"
  }
}

### 3.3 Verify with incorrect OTP
# This should return a verification failure
POST {{baseUrl}}/transactions/verify/{{transactionReqId}}
Content-Type: application/json

{
  "otp": "000000"
}

# =================================================================
# NOTES AND SAMPLE RESPONSES
# =================================================================

# WORKFLOW:
# 1. Create a transaction -> Get transactionID
# 2. Activate the transaction -> Get transaction_req_id and sends OTP to phone
# 3. User receives OTP on their phone
# 4. Verify the OTP using transaction_req_id and the received OTP code

# SAMPLE RESPONSES:

# 1. Create Transaction Response:
# {
#   "status": "success",
#   "data": {
#     "transactionID": "abc123xyz",
#     "status": "pending",
#     "createdAt": "2023-05-15T12:34:56.789Z"
#   }
# }

# 2. Activate Transaction Response:
# {
#   "status": "success",
#   "data": {
#     "_id": "req123xyz",
#     "status": "active",
#     "channel": "whatsApp", // or "sms"
#     "message": "OTP sent successfully"
#   }
# }

# 3. Verify OTP Response:
# {
#   "status": "success",
#   "data": {
#     "verified": true,
#     "message": "OTP verified successfully"
#   }
# }

# 4. Transaction Status Response:
# {
#   "status": "success",
#   "data": {
#     "transactionID": "abc123xyz",
#     "status": "completed", // or "pending", "active", "failed"
#     "createdAt": "2023-05-15T12:34:56.789Z",
#     "completedAt": "2023-05-15T12:40:00.000Z"
#   }
# }

# ERROR RESPONSE EXAMPLE:
# {
#   "status": "error",
#   "error": {
#     "code": "INVALID_PHONE",
#     "message": "The provided phone number is invalid"
#   }
# }
