# Gomla API Documentation

This document describes the REST API endpoints for the wholesaler dashboard functionality (Gomla) that replicate the features from the web views for the Flutter mobile app.

## Base URL

All endpoints are prefixed with `/api/gomla/`

## Authentication

All endpoints require JWT authentication via the `Authorization: Bearer <token>` header. The user must be associated with a wholesaler account.

## Endpoints Overview

### Dashboard

- `GET /dashboard` - Get dashboard statistics and data

### Orders Management

- `GET /orders` - List orders with filtering and pagination
- `GET /orders/{order_id}` - Get order details
- `PUT /orders/{order_id}/status` - Update order status
- `DELETE /orders/{order_id}/items/{item_id}` - Remove item from order
- `PUT /orders/{order_id}/items/{item_id}/quantity` - Decrease item quantity

### Items Management

- `GET /items` - List items with filtering and pagination
- `GET /items/{item_id}` - Get item details
- `PUT /items/{item_id}` - Update item
- `DELETE /items/{item_id}` - Delete item

### Bulk Operations

- `GET /products/available` - List products available for adding to inventory
- `POST /items/bulk-add` - Bulk add items
- `PUT /items/bulk-update` - Bulk update items

## Detailed Endpoint Documentation

### Dashboard

#### GET /dashboard

Get wholesaler dashboard data including statistics, recent orders, and sales data.

**Response:**

```json
{
  "stats": {
    "total_orders": 150,
    "pending_orders": 25,
    "total_items": 500,
    "low_stock_items": 12
  },
  "recent_orders": [
    {
      "id": 123,
      "store": {
        "id": 45,
        "name": "متجر الأمل",
        "address": "شارع الملك فهد، الرياض",
        "owner_username": "ahmed_store"
      },
      "status": "pending",
      "total_price": 1250.5,
      "created_at": "2024-01-15T10:30:00Z"
    }
  ],
  "daily_sales": [
    {
      "date": "2024-01-15",
      "sales": 2500.75
    }
  ]
}
```

### Orders Management

#### GET /orders

List all orders for the wholesaler with filtering and pagination.

**Query Parameters:**

- `page` (int, default: 1) - Page number
- `page_size` (int, default: 20, max: 100) - Items per page
- `status` (string, optional) - Filter by status: "pending", "processing", "shipped", "delivered", "cancelled"
- `search` (string, optional) - Search in store name, owner username, or order ID
- `date_from` (string, optional) - Filter from date (YYYY-MM-DD format)
- `date_to` (string, optional) - Filter to date (YYYY-MM-DD format)

**Response:**

```json
{
  "orders": [
    {
      "id": 123,
      "store": {
        "id": 45,
        "name": "متجر الأمل",
        "address": "شارع الملك فهد، الرياض",
        "owner_username": "ahmed_store"
      },
      "status": "pending",
      "total_price": 1250.5,
      "fees": 15.63,
      "products_total_price": 1234.87,
      "products_total_quantity": 25,
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "order_items": [
        {
          "id": 456,
          "product": {
            "id": 789,
            "name": "أرز بسمتي",
            "title": "أرز بسمتي فاخر 5 كيلو",
            "barcode": "1234567890123",
            "company_name": "شركة الأرز الذهبي",
            "category_name": "حبوب"
          },
          "quantity": 10,
          "price_per_unit": 25.5,
          "total_price": 255.0
        }
      ]
    }
  ],
  "stats": {
    "total_orders": 150,
    "pending_orders": 25,
    "completed_orders": 100
  },
  "total_count": 150,
  "page": 1,
  "page_size": 20,
  "total_pages": 8,
  "has_next": true,
  "has_previous": false
}
```

#### GET /orders/{order_id}

Get detailed information about a specific order.

**Response:** Same as individual order object in the orders list.

#### PUT /orders/{order_id}/status

Update the status of an order.

**Request Body:**

```json
{
  "status": "processing"
}
```

**Valid statuses:** "pending", "processing", "shipped", "delivered", "cancelled"

**Response:**

```json
{
  "message": "تم تحديث حالة الطلب #123 بنجاح",
  "old_status": "pending",
  "new_status": "processing"
}
```

#### DELETE /orders/{order_id}/items/{item_id}

Remove an item from an order and restore inventory.

**Request Body:**

```json
{
  "reason": "المنتج غير متوفر في المخزون"
}
```

**Response:**

```json
{
  "message": "تم حذف المنتج \"أرز بسمتي\" من الطلب #123 بنجاح. تم استرداد 10 قطعة إلى المخزون.",
  "restored_quantity": 10,
  "updated_order": {
    /* Updated order object */
  }
}
```

#### PUT /orders/{order_id}/items/{item_id}/quantity

Decrease the quantity of an item in an order and restore the difference to inventory.

**Request Body:**

```json
{
  "new_quantity": 5,
  "reason": "تقليل الكمية - توفر جزئي"
}
```

**Response:**

```json
{
  "message": "تم تقليل كمية \"أرز بسمتي\" من 10 إلى 5. تم استرداد 5 قطعة إلى المخزون.",
  "old_quantity": 10,
  "new_quantity": 5,
  "restored_quantity": 5,
  "updated_order": {
    /* Updated order object */
  }
}
```

### Items Management

#### GET /items

List all items/inventory for the wholesaler with filtering and pagination.

**Query Parameters:**

- `page` (int, default: 1) - Page number
- `page_size` (int, default: 20, max: 100) - Items per page
- `search` (string, optional) - Search in product name, title, or barcode
- `company` (int, optional) - Filter by company ID
- `category` (int, optional) - Filter by category ID
- `stock` (string, optional) - Filter by stock level: "low" (<10), "out" (=0), "available" (>0)
- `sort` (string, optional) - Sort field: "-created_at", "created_at", "product**name", "-product**name", "inventory_count", "-inventory_count", "base_price", "-base_price"

**Response:**

```json
{
  "items": [
    {
      "id": 123,
      "product": {
        "id": 789,
        "name": "أرز بسمتي",
        "title": "أرز بسمتي فاخر 5 كيلو",
        "barcode": "1234567890123",
        "company": {
          "id": 45,
          "name": "شركة الأرز الذهبي",
          "title": "شركة الأرز الذهبي المحدودة"
        },
        "category": {
          "id": 12,
          "name": "حبوب",
          "title": "حبوب ومواد غذائية أساسية"
        }
      },
      "base_price": 25.5,
      "inventory_count": 100,
      "minimum_order_quantity": 1,
      "maximum_order_quantity": 50,
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ],
  "stats": {
    "total_items": 500,
    "low_stock_items": 12,
    "out_of_stock_items": 3
  },
  "companies": [
    {
      "id": 45,
      "name": "شركة الأرز الذهبي",
      "title": "شركة الأرز الذهبي المحدودة"
    }
  ],
  "categories": [
    {
      "id": 12,
      "name": "حبوب",
      "title": "حبوب ومواد غذائية أساسية"
    }
  ],
  "total_count": 500,
  "page": 1,
  "page_size": 20,
  "total_pages": 25,
  "has_next": true,
  "has_previous": false
}
```

#### GET /items/{item_id}

Get detailed information about a specific item.

**Response:** Same as individual item object in the items list.

#### PUT /items/{item_id}

Update an existing item.

**Request Body:**

```json
{
  "base_price": 27.5,
  "inventory_count": 150,
  "minimum_order_quantity": 2,
  "maximum_order_quantity": 100
}
```

All fields are optional. Only provided fields will be updated.

**Response:** Updated item object.

#### DELETE /items/{item_id}

Delete an item (soft delete).

**Response:**

```json
{
  "message": "تم حذف المنتج \"أرز بسمتي\" بنجاح",
  "deleted_item_id": 123
}
```

### Bulk Operations

#### GET /products/available

List products available for adding to inventory (not already in wholesaler's inventory).

**Query Parameters:**

- `page` (int, default: 1) - Page number
- `page_size` (int, default: 20, max: 100) - Items per page
- `search` (string, optional) - Search in product name, title, or barcode
- `company` (int, optional) - Filter by company ID
- `category` (int, optional) - Filter by category ID

**Response:**

```json
{
  "products": [
    {
      "id": 789,
      "name": "أرز بسمتي",
      "title": "أرز بسمتي فاخر 5 كيلو",
      "barcode": "1234567890123",
      "company": {
        "id": 45,
        "name": "شركة الأرز الذهبي",
        "title": "شركة الأرز الذهبي المحدودة"
      },
      "category": {
        "id": 12,
        "name": "حبوب",
        "title": "حبوب ومواد غذائية أساسية"
      }
    }
  ],
  "companies": [
    /* Company list */
  ],
  "categories": [
    /* Category list */
  ],
  "total_count": 250,
  "page": 1,
  "page_size": 20,
  "total_pages": 13,
  "has_next": true,
  "has_previous": false
}
```

#### POST /items/bulk-add

Bulk add items with default values.

**Request Body:**

```json
{
  "product_ids": [789, 790, 791]
}
```

**Response:**

```json
{
  "created_items": [
    {
      "id": 123,
      "product": {
        /* Product details */
      },
      "base_price": 0.0,
      "inventory_count": 0,
      "minimum_order_quantity": 1,
      "maximum_order_quantity": null,
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ],
  "failed_items": [
    {
      "product_id": 792,
      "product_name": "منتج موجود",
      "error": "المنتج موجود بالفعل في المخزون"
    }
  ],
  "success_count": 3,
  "failure_count": 1
}
```

#### PUT /items/bulk-update

Bulk update multiple items.

**Request Body:**

```json
{
  "item_ids": [123, 124, 125],
  "base_price": 25.5,
  "inventory_count": 100,
  "minimum_order_quantity": 2,
  "maximum_order_quantity": 50
}
```

All update fields are optional. Only provided fields will be updated for all selected items.

**Response:**

```json
{
  "updated_items": [
    {
      /* Updated item objects */
    }
  ],
  "success_count": 3,
  "failure_count": 0
}
```

## Error Handling

All endpoints return appropriate HTTP status codes:

- `200 OK` - Successful request
- `400 Bad Request` - Invalid request data or parameters
- `401 Unauthorized` - Missing or invalid authentication token
- `403 Forbidden` - User is not a wholesaler or lacks permissions
- `404 Not Found` - Requested resource not found
- `500 Internal Server Error` - Server error

Error responses include Arabic error messages:

```json
{
  "detail": "ليس لديك صلاحية للوصول إلى لوحة التحكم"
}
```

## Features Implemented

✅ **Dashboard Statistics**: Total orders, pending orders, total items, low stock items
✅ **Recent Orders**: Last 5 orders with store information
✅ **Sales Analytics**: Daily sales data for the last 7 days
✅ **Order Management**: List, filter, search, and view order details
✅ **Order Status Updates**: Change order status with validation
✅ **Order Item Management**: Remove items and decrease quantities with inventory restoration
✅ **Inventory Management**: List, filter, search, and manage items
✅ **Item CRUD Operations**: Create, read, update, delete items
✅ **Bulk Operations**: Bulk add and update items
✅ **Product Search**: Search available products for adding to inventory
✅ **Inventory Tracking**: Automatic inventory transaction logging
✅ **Fee Calculations**: Automatic fee recalculation when order items change
✅ **Soft Deletes**: Respect soft delete patterns throughout
✅ **Pagination**: Consistent pagination across all list endpoints
✅ **Authentication**: JWT-based authentication with wholesaler validation
✅ **Error Handling**: Comprehensive error handling with Arabic messages

## Integration Notes

- All endpoints follow the existing Django Ninja API patterns used throughout the TagerPlus application
- Authentication is handled via the existing JWT middleware (`AuthMiddleware`)
- Error handling follows standard HTTP status code conventions
- The endpoints are registered under the `/api/gomla/` prefix
- Soft deletes are respected (deleted items/orders/wholesalers are excluded)
- Database queries are optimized with `select_related` and `prefetch_related` for performance
- Inventory transactions are automatically created when inventory changes
- Order fees are automatically recalculated when order items are modified
- All business logic matches the web views implementation
