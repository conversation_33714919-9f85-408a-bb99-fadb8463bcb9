# Pro Grocery API Test File
# ========================
#
# This file provides comprehensive HTTP requests to test the Pro Grocery API
# for authentication and product retrieval functionality.
#
# Base URL: http://localhost:8000/api/v2/
# (Adjust the base URL according to your development environment)

# Environment Variables
# --------------------
# Create a .env file with these variables or set them in your HTTP client

@baseUrl = http://localhost:8000/api/v2
@authToken = {{login.response.body.token}}

# Test user credentials (replace with actual test data)
@testPhone = ***********
@testPassword = ***********#S
@testName = Seif
@testEmail = <EMAIL>

# =================================================================
# 1. AUTHENTICATION ENDPOINTS
# =================================================================

### 1.1 User Login
# Authenticate user with phone and password
# @name login
POST {{baseUrl}}/login
Content-Type: application/json

{
  "phone": "{{testPhone}}",
  "password": "{{testPassword}}"
}

### 1.2 User Registration
# Register a new user account
POST {{baseUrl}}/register
Content-Type: application/json

{
  "name": "{{testName}}",
  "phone": "{{testPhone}}",
  "password": "{{testPassword}}",
  "email": "{{testEmail}}"
}

### 1.3 Get Current User Profile
# Retrieve current user information (requires authentication)
GET {{baseUrl}}/me
Authorization: Bearer {{authToken}}

### 1.4 Update Current User Profile
# Update user profile information (requires authentication)
PUT {{baseUrl}}/me
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "first_name": "Updated First Name",
  "last_name": "Updated Last Name",
  "email": "<EMAIL>"
}

# =================================================================
# 2. PRODUCTS ENDPOINTS
# =================================================================

### 2.1 Get Products (Basic)
# Retrieve products with default pagination
GET {{baseUrl}}/home/<USER>

### 2.2 Get Products with Pagination
# Retrieve products with custom pagination
GET {{baseUrl}}/home/<USER>

### 2.3 Get Products by Region
# Filter products by specific region
GET {{baseUrl}}/home/<USER>

### 2.4 Get Products by Wholesaler
# Filter products by specific wholesaler
GET {{baseUrl}}/home/<USER>

### 2.5 Get Products by Category
# Filter products by category
GET {{baseUrl}}/home/<USER>

### 2.6 Get Products by Company
# Filter products by company
GET {{baseUrl}}/home/<USER>

### 2.7 Search Products
# Search products by name, title, or description
GET {{baseUrl}}/home/<USER>

### 2.8 Get Products with Multiple Filters
# Combine multiple filters
GET {{baseUrl}}/home/<USER>

### 2.9 Get Products (Authenticated)
# Retrieve products with authentication (if required)
GET {{baseUrl}}/home/<USER>
Authorization: Bearer {{authToken}}

### 2.10 Get Products with Semantic Search
# Test semantic search functionality
GET {{baseUrl}}/home/<USER>
Authorization: Bearer {{authToken}}

# =================================================================
# 3. ERROR HANDLING TESTS
# =================================================================

### 3.1 Login with Invalid Credentials
# This should return an authentication error
POST {{baseUrl}}/login
Content-Type: application/json

{
  "phone": "invalid_phone",
  "password": "wrong_password"
}

### 3.2 Login with Missing Fields
# This should return a validation error
POST {{baseUrl}}/login
Content-Type: application/json

{
  "phone": "{{testPhone}}"
}

### 3.3 Register with Invalid Phone
# This should return a validation error
POST {{baseUrl}}/register
Content-Type: application/json

{
  "name": "{{testName}}",
  "phone": "invalid_phone",
  "password": "{{testPassword}}"
}

### 3.4 Get Products with Invalid Region
# This should return an error or empty results
GET {{baseUrl}}/home/<USER>

### 3.5 Get Products with Invalid Page
# This should return an error or default to page 1
GET {{baseUrl}}/home/<USER>

### 3.6 Get Products with Invalid Page Size
# This should return an error or default to max page size
GET {{baseUrl}}/home/<USER>

### 3.7 Access Protected Endpoint Without Token
# This should return an authentication error
GET {{baseUrl}}/me

### 3.8 Access Protected Endpoint with Invalid Token
# This should return an authentication error
GET {{baseUrl}}/me
Authorization: Bearer invalid_token_here

# =================================================================
# 4. PERFORMANCE TESTS
# =================================================================

### 4.1 Large Page Size Test
# Test with maximum allowed page size
GET {{baseUrl}}/home/<USER>

### 4.2 Complex Search Test
# Test with complex search terms
GET {{baseUrl}}/home/<USER>

### 4.3 Multiple Filter Combination Test
# Test with all filters combined
GET {{baseUrl}}/home/<USER>

# =================================================================
# 5. CACHE TESTING
# =================================================================

### 5.1 Cache Test - First Request
# First request should hit the database
GET {{baseUrl}}/home/<USER>

### 5.2 Cache Test - Second Request (Same Parameters)
# Second request should be served from cache
GET {{baseUrl}}/home/<USER>

### 5.3 Cache Test - Different Parameters
# Different parameters should not use cache
GET {{baseUrl}}/home/<USER>

# =================================================================
# 6. SAMPLE RESPONSES
# =================================================================

# LOGIN RESPONSE EXAMPLE:
# {
#   "success": true,
#   "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
#   "user_id": 1,
#   "phone": "+201122342626",
#   "is_phone_verified": true,
#   "wholesaler_id": null
# }

# REGISTER RESPONSE EXAMPLE:
# {
#   "success": true,
#   "user_id": 1,
#   "phone": "+201122342626",
#   "message": "User registered successfully",
#   "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
# }

# PRODUCTS RESPONSE EXAMPLE:
# {
#   "products": [
#     {
#       "id": 1,
#       "name": "Apple",
#       "title": "Fresh Red Apple",
#       "barcode": "*********",
#       "slug": "fresh-red-apple",
#       "description": "Fresh and juicy red apples",
#       "image_url": "https://example.com/apple.jpg",
#       "company_id": 1,
#       "category_id": 1,
#       "company": {
#         "id": 1,
#         "name": "Fresh Fruits Co.",
#         "logo_url": "https://example.com/logo.jpg"
#       },
#       "category": {
#         "id": 1,
#         "name": "Fruits",
#         "image_url": "https://example.com/fruits.jpg"
#       },
#       "unit": "kg",
#       "unit_count": "1.0",
#       "base_price": "5.99",
#       "other_price": "7.99"
#     }
#   ],
#   "total_count": 100,
#   "page": 1,
#   "page_size": 20,
#   "total_pages": 5,
#   "has_next": true,
#   "has_previous": false
# }

# ERROR RESPONSE EXAMPLE:
# {
#   "detail": "Invalid phone number or password"
# }

# =================================================================
# 7. TESTING WORKFLOW
# =================================================================

# RECOMMENDED TESTING SEQUENCE:
# 1. Test registration with new user
# 2. Test login with registered user
# 3. Test get current user profile (with auth token)
# 4. Test update user profile
# 5. Test get products with various filters
# 6. Test error scenarios
# 7. Test performance and caching

# NOTES:
# - Replace placeholder values with actual test data
# - The API uses JWT tokens for authentication
# - Products endpoint supports semantic search if pgvector is configured
# - Caching is enabled for products endpoint (30 minutes)
# - Maximum page size is 100 items
# - All endpoints return JSON responses
# - Error responses include appropriate HTTP status codes 