### Akedly API Tests
# This file contains HTTP requests to test the Akedly API directly
# You'll need to replace the placeholders with your actual values

@baseUrl = https://api.akedly.io/api/v1
@apiKey = 459a32274b08e62826c2d61024879ad2ce70ecb2e82b62cf7b85d31a6e156d0a
@pipelineId = 67fb4a768611e4b768594924
@phoneNumber = +201122342626

### 1. Create a new transaction
# This initiates the OTP verification process
POST {{baseUrl}}/transactions
Content-Type: application/json

{
  "APIKey": "{{apiKey}}",
  "pipelineID": "{{pipelineId}}",
  "verificationAddress": {
    "phoneNumber": "{{phoneNumber}}"
  }
}

### 2. Activate the transaction
# After creating a transaction, you need to activate it to send the OTP
# Replace {transactionID} with the ID received from the previous request
POST {{baseUrl}}/transactions/activate/fff68cfa3ee5dd36eff44edb01a8f26f311e661bcb152bf1c3324e321c037013

### 3. Verify the OTP
# After receiving the OTP on the phone, verify it
# Replace {transactionReqId} with the transaction_req_id received from the activation response
POST {{baseUrl}}/transactions/verify/681700109f9cfd6b2754be7f
Content-Type: application/json

{
  "otp": "672105"
}

### 4. Check transaction status (optional)
# You can check the status of a transaction
# Replace {transactionID} with the transaction ID
GET {{baseUrl}}/transactions/{transactionID}

### Notes:
# 1. The Akedly API sends OTP codes via WhatsApp or SMS
# 2. You need a valid API key and pipeline ID from your Akedly account
# 3. The phone number must be in international format with country code
# 4. The OTP code is sent to the provided phone number
# 5. The transaction flow is: Create -> Activate -> Verify

curl "https://top-dragon-23019.upstash.io/set/foo/bar" \
  -H "Authorization: Bearer AVnrAAIjcDFiZmM5NDkyYjdkYjg0NDgwYThlMmQ2ODBiMGJiZTQ5MXAxMA"