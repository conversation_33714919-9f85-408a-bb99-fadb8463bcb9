---
description: 
globs: 
alwaysApply: true
---
# Pro_Grocery Project Intelligence

## Project Identity & Focus
- **Primary Project**: Flutter app in root directory (`lib/`)
- **Support Context**: Django API in `api_django/` for backend reference
- **Dual Purpose**: Functional grocery app + reusable e-commerce template
- **App Display Name**: "Tager Plus" (from main.dart)
- **Package Name**: `grocery` (from pubspec.yaml)

## Critical Development Patterns

### File Structure Intelligence
- **Feature-Based Organization**: Each major feature has its own `views/[feature]/` directory
- **Component Hierarchy**: `core/components/` for reusable widgets
- **Asset Organization**: `assets/icons/` (SVG), `assets/images/` (PNG), `assets/fonts/` (Gilroy family)
- **API Layer**: `lib/api/` and `lib/services/` for business logic

### State Management (GetX)
- **Controllers**: Business logic and reactive state management
- **Routing**: GetX named routes in `core/routes/`
- **Initial Route**: `AppRoutes.onboarding` (from main.dart)
- **Theme**: `AppTheme.defaultTheme` centralized theming

### UI & Design Patterns
- **Font Family**: Custom Gilroy fonts (Bold, Light, Medium, Regular)
- **Icon Strategy**: SVG icons for scalability
- **Material Design**: Base framework with custom theming
- **Credit Card UI**: flutter_credit_card for payment interfaces

## Technical Dependencies
- **GetX ^4.6.6**: Core state management and routing
- **Dio**: HTTP client (version needs specification)
- **Cached Network Image ^3.4.0**: Image loading optimization
- **Form Field Validator ^1.1.0**: Input validation
- **Flutter SVG ^2.0.10**: Vector graphics support
- **Animations ^2.0.11**: UI transitions

## Backend Integration Context
- **Django Structure**: accounts, products, stores, wholesalers apps
- **API Style**: RESTful design expected
- **Development Tools**: uv, Makefile, Docker support
- **Database**: PostgreSQL recommended

## Development Workflow Patterns
```bash
# Flutter Commands
flutter pub get
flutter run
flutter build apk
flutter build ios

# Django Commands (from api_django/)
make install
make migrate  
make runserver
```

## Known Issues & Technical Debt
- **Dio Version**: Not specified in pubspec.yaml - needs explicit version
- **API Integration**: Flutter-Django connection needs verification
- **Testing Coverage**: Limited test implementation
- **Error Handling**: Needs centralized error management
- **Documentation**: Inline code documentation needed

## Architecture Decisions
- **API-First Design**: Backend-agnostic frontend approach
- **Offline-First Strategy**: Local storage with sync capabilities
- **Modular Features**: Each feature can be developed independently
- **Template Flexibility**: Code structure supports easy customization

## User Experience Priorities
- **Mobile-First**: Optimized for smartphone usage
- **Performance**: 60fps animations, <3s startup
- **Accessibility**: Consider screen readers and accessibility features
- **Responsive**: Adaptive layouts for different screen sizes

## Business Logic Patterns
- **Shopping Cart**: Local state with server sync
- **Authentication**: JWT token management expected
- **Order Flow**: Cart → Checkout → Payment → Tracking
- **Product Discovery**: Categories, search, filters, recommendations

## Security Considerations
- **Secure Storage**: Sensitive data encryption
- **HTTPS Only**: Encrypted API communication
- **Input Validation**: Client and server-side validation
- **Token Management**: JWT refresh and expiration handling

## Memory Bank Usage
- **Always Read First**: Check memory-bank/ before starting work
- **Update After Changes**: Keep documentation current
- **Focus Areas**: activeContext.md and progress.md for current state
- **Architecture Changes**: Update systemPatterns.md for major changes

## Development Preferences
- **Code Quality**: Use flutter_lints rules consistently
- **Git Workflow**: Keep commits atomic and descriptive
- **Asset Management**: Optimize images, use SVG when possible
- **Performance**: Profile memory usage and rendering performance
- **Testing**: Write tests for business logic and critical user flows

## Template Customization Points
- **Branding**: Theme colors, fonts, app name, icons
- **Features**: Enable/disable features based on business needs
- **API Endpoints**: Configure base URLs and authentication methods
- **Business Logic**: Customize shopping flow, payment options, delivery methods
- **UI Components**: Modify layouts while maintaining component consistency 