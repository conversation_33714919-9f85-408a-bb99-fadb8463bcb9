# ProductSelectionPage & CompanySelectionPage: Detailed Explanation

This document provides a comprehensive overview of the `ProductSelectionPage` and `CompanySelectionPage` in the wholesaler item workflow, including their responsibilities, user experience, and the API routes they interact with.

---

## 1. CompanySelectionPage

### Purpose
- Allows the user to select a company when creating a new wholesaler item.
- Selecting a company filters the available products in the subsequent product selection step.
- **Note:** When editing an item, company selection is disabled to prevent changing the core identity of the item.

### Location
- **File:** `lib/features_old/wholesaler/company_selection/company_selection_page.dart`
- **Controller:** `CompanySelectionController`

### Features
- Search bar for filtering companies by name.
- List of available companies, selectable.
- Selecting a company resets the product selection in the item form.
- Loading indicator during API fetch.

### API Route(s)
- **GET /api/companies**
  - Fetches the list of companies available to the wholesaler.
  - Supports search/filter query parameters (e.g., `?search=Acme`).
  - Returns a paginated list of company objects.

#### Example Request
```http
GET /api/companies?search=Acme
```

#### Example Response
```json
{
  "results": [
    { "id": 1, "name": "Acme Corp" },
    { "id": 2, "name": "Acme Trading" }
  ],
  "count": 2
}
```

---

## 2. ProductSelectionPage

### Purpose
- Allows the user to select a product for the wholesaler item.
- If a company is selected, only products from that company are shown.
- Selecting a product with an associated company auto-selects that company if not already set.
- **Note:** When editing an item, product selection is disabled.

### Location
- **File:** `lib/features_old/wholesaler/product_selection/product_selection_page.dart`
- **Controller:** `ProductSelectionController`

### Features
- Search bar for filtering products by name or code.
- List of available products, selectable.
- Filtering by selected company (if any).
- Loading indicator during API fetch.

### API Route(s)
- **GET /api/products**
  - Fetches the list of products.
  - Supports search/filter query parameters (e.g., `?search=Milk`).
  - Supports filtering by company (e.g., `?company_id=1`).
  - Returns a paginated list of product objects.

#### Example Request (with company filter)
```http
GET /api/products?company_id=1&search=Milk
```

#### Example Response
```json
{
  "results": [
    { "id": 10, "name": "Milk 1L", "company_id": 1 },
    { "id": 11, "name": "Milk 2L", "company_id": 1 }
  ],
  "count": 2
}
```

---

## 3. User Experience Notes
- Both selection pages use search and filtering to handle large datasets efficiently.
- Loading indicators are shown while fetching data from the API.
- Selections are highlighted and update the item form accordingly.
- When editing an item, both company and product selection are disabled to prevent accidental changes to the item's identity.

---

## 4. Related Files
- `company_selection_page.dart`, `company_selection_controller.dart`
- `product_selection_page.dart`, `product_selection_controller.dart`
- `item_form_page.dart`, `item_form_controller.dart`

---

## 5. Workflow Summary
1. User opens the item form page.
2. (Optional) User selects a company via CompanySelectionPage.
3. User selects a product via ProductSelectionPage (filtered by company if selected).
4. Selections are reflected in the item form for further steps. 