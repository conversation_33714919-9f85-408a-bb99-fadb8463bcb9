# Technical Context: Pro_Grocery Technology Stack

## Frontend Technology Stack

### Flutter Framework
- **Version**: SDK >=3.5.0 <4.0.0 (Latest stable)
- **Target Platforms**: iOS and Android
- **Rendering**: Skia-based rendering engine for consistent UI
- **Language**: Dart with null safety enabled

### Key Dependencies

#### State Management & Navigation
- **GetX (^4.6.6)**: Reactive state management, dependency injection, and routing
- **Cupertino Icons (^1.0.8)**: iOS-style icons

#### UI & Visuals
- **Flutter SVG (^2.0.10)**: SVG image rendering for scalable icons
- **Cached Network Image (^3.4.0)**: Efficient image loading and caching
- **Animations (^2.0.11)**: Smooth transitions and micro-interactions
- **Flutter Credit Card (^3.0.3)**: Payment card input UI components

#### Network & Data Management
- **Dio**: HTTP client for API communication (configured with interceptors)
- **SharedPreferences**: Local storage for user preferences and region data

#### Validation & Forms
- **Form Field Validator (^1.1.0)**: Input validation utilities

#### Development Tools
- **Flutter Lints (^4.0.0)**: Code quality and style enforcement
- **Flutter Native Splash (^2.2.3+1)**: Native splash screen generation
- **Flutter Launcher Icons (^0.13.1)**: App icon generation

### Project Configuration

#### App Identity
- **Package Name**: `grocery`
- **Display Name**: "Tager Plus"
- **Version**: 1.0.0+1
- **Bundle ID**: com.tagerplus.com.grocery (Android)

#### Asset Management
- **Images**: `assets/images/` directory (PNG format for photos)
- **Icons**: `assets/icons/` directory (SVG format for scalability)
- **Fonts**: Custom Gilroy font family (Bold, Light, Medium, Regular)

#### Platform Configuration
- **Android**: 
  - Target SDK configured in `build.gradle.kts`
  - Custom launcher icons and splash screen
  - ProGuard rules for release builds
  - Minimum SDK level for modern features
- **iOS**: 
  - Xcode project configuration
  - App Store deployment ready
  - iOS 12+ compatibility
  - CocoaPods integration

## Enhanced Backend Integration

### Django Framework
- **Framework**: Django (Python web framework)
- **Database**: PostgreSQL (recommended for production)
- **API Style**: RESTful API design with consistent patterns
- **Location**: `api_django/` directory

#### Django Apps Structure
- **Accounts**: User authentication and profile management
- **Products**: Product catalog and inventory management
- **Stores**: Store management and configuration
- **Wholesalers**: B2B functionality and wholesale operations
- **Core**: Shared functionality and API documentation

#### Enhanced API Endpoints (Current Integration)
- **Authentication APIs**:
  - `POST /api/v2/login` - User authentication with JWT
  - `POST /api/v2/register` - User registration
- **Regional APIs** ✨ NEW:
  - `GET /api/v2/regions/` - Hierarchical region data
- **Product APIs**:
  - `GET /api/v2/products/{id}/` - Region-aware product details
  - Product pricing filtered by selected region

#### Development Tools
- **uv**: Modern Python package manager
- **Makefile**: Build automation and development commands
- **Docker**: Containerization for deployment
- **Requirements Lock Files**: Dependency version management

## Enhanced Service Architecture

### Global Service Management ✨ NEW
```dart
// AppServices - Service Locator Pattern
class AppServices {
  static final RegionService regionService = RegionService();
  static final AuthService authService = AuthService();
  // Additional services...
}
```

### Regional Data Management ✨ NEW
- **RegionService**: Centralized region state management
- **SharedPreferences**: Persistent region storage
- **24-Hour Caching**: Optimized region data caching
- **Hierarchical Regions**: Country/State/District support

### API Service Layer
- **BaseApiService**: Common HTTP client configuration
- **RegionsApiService**: Regional data fetching ✨ NEW
- **ProductDetailsApiService**: Region-aware product data
- **AuthApiService**: Authentication management

## Development Environment

### Required Tools
1. **Flutter SDK**: Latest stable version (3.5.0+)
2. **Dart SDK**: Included with Flutter
3. **Android Studio/VS Code**: IDE with Flutter plugins
4. **Xcode**: For iOS development (macOS only)
5. **Python 3.8+**: For Django backend development
6. **PostgreSQL**: Database for backend (production)

### Enhanced Development Workflow
```bash
# Flutter Development
flutter pub get          # Install dependencies
flutter run              # Run app on connected device
flutter build apk        # Build Android APK
flutter build ios        # Build iOS app
flutter test             # Run unit tests

# Django Development (Enhanced)
cd api_django/
make install             # Install Python dependencies
make migrate             # Run database migrations  
make runserver           # Start development server
make test                # Run backend tests
```

### Development Scripts
```bash
# Region management testing
flutter run --dart-define=API_BASE_URL=http://localhost:8000

# Build with regional configuration
flutter build apk --release --dart-define=ENABLE_REGIONS=true
```

## Architecture Constraints

### Technical Limitations
1. **Platform Limitations**: 
   - iOS: App Store review requirements
   - Android: Google Play Store policies
   - Memory constraints on older devices
   - Regional availability restrictions

2. **Network Dependencies**:
   - Requires internet for region selection
   - Regional pricing needs API connectivity
   - Offline mode limited to cached data
   - API rate limiting considerations

3. **Performance Requirements**:
   - 60fps smooth animations
   - <3 second app startup time
   - <500ms API response times
   - Efficient region caching

### Regional Constraints ✨ NEW
1. **Geographic Data**:
   - Region hierarchy must be maintained
   - Cache invalidation strategies required
   - Fallback for region API failures
   - Multi-language region name support

2. **Pricing Logic**:
   - Region-specific wholesaler availability
   - Dynamic pricing based on geography
   - Currency considerations per region
   - Tax calculations by location

### Security Requirements (Enhanced)
1. **Data Protection**:
   - GDPR compliance for user data
   - Regional data privacy regulations
   - Secure storage of sensitive information
   - Encrypted communication with backend

2. **Authentication**:
   - JWT token management with refresh
   - Biometric authentication support
   - Session timeout handling
   - Multi-device session management

## Integration Points

### Enhanced API Communication
- **Base URL**: Configurable per environment
- **Authentication**: JWT Bearer tokens with automatic refresh
- **Request Format**: JSON with standard HTTP methods
- **Response Format**: JSON with consistent error handling
- **File Uploads**: Multipart form data for images
- **Regional Context**: Region ID passed with product requests ✨ NEW

### Third-Party Services (Planned)
- **Payment Gateways**: Stripe/PayPal integration points
- **Push Notifications**: Firebase Cloud Messaging
- **Analytics**: Firebase Analytics/Crashlytics
- **Maps**: Google Maps for delivery tracking
- **Regional Services**: Geographic data providers ✨ NEW

## Deployment Configuration

### Mobile App Distribution
- **Android**: Google Play Store + APK direct distribution
- **iOS**: App Store + TestFlight for beta testing
- **CI/CD**: GitHub Actions for automated builds
- **Regional Builds**: Location-specific app configurations ✨ NEW

### Backend Deployment (Enhanced)
- **Production**: Docker containers on cloud platforms
- **Staging**: Separate environment for testing
- **Database**: Managed PostgreSQL service
- **Static Files**: CDN for media and assets
- **Regional APIs**: Geographic data service integration ✨ NEW

## Performance Considerations

### Mobile App Optimization (Enhanced)
1. **Bundle Size**: Tree shaking and code splitting
2. **Memory Usage**: Efficient image loading and disposal
3. **Battery Life**: Background task optimization
4. **Network Usage**: Request batching and caching
5. **Regional Data**: 24-hour cache for region information ✨ NEW
6. **Geographic Performance**: Location-aware API optimization ✨ NEW

### API Performance (Enhanced)
1. **Database Optimization**: Efficient queries and indexing
2. **Regional Caching**: Redis for session and regional data ✨ NEW
3. **Load Balancing**: Horizontal scaling for high traffic
4. **Monitoring**: APM tools for performance tracking
5. **Geographic Distribution**: CDN for regional performance ✨ NEW

## Quality Assurance

### Enhanced Testing Strategy
- **Unit Tests**: Business logic validation
- **Widget Tests**: UI component testing including regional components ✨ NEW
- **Integration Tests**: End-to-end user journeys with regional pricing ✨ NEW
- **API Tests**: Backend endpoint validation including region APIs ✨ NEW
- **Regional Testing**: Geographic data and pricing validation ✨ NEW

### Code Quality (Enhanced)
- **Linting**: Flutter lints for code consistency
- **Code Review**: Pull request review process
- **Documentation**: Inline comments and README files
- **Version Control**: Git with semantic versioning
- **Service Documentation**: API and service layer documentation ✨ NEW

## Production Readiness

### Monitoring & Analytics ✨ NEW
- **Performance Monitoring**: API response times and app performance
- **Error Tracking**: Crashlytics integration for error reporting
- **User Analytics**: Region selection patterns and user behavior
- **Business Metrics**: Regional pricing effectiveness tracking

### Security in Production
- **Environment Variables**: Secure configuration management
- **API Security**: Rate limiting and request validation
- **Data Encryption**: At-rest and in-transit encryption
- **Regional Compliance**: Local data protection regulation compliance ✨ NEW

### Scalability Considerations
- **Horizontal Scaling**: Service architecture supports scaling
- **Database Optimization**: Efficient regional data storage
- **Cache Strategy**: Multi-layer caching for performance
- **Geographic Distribution**: Regional API deployment strategies ✨ NEW 