# TagerPlus App

## Folder Structure

There is two projects will be inside one app in the end, first is the tagerplus app and the second is the wholesaler app, the wholesaler app is a dependent app on the tagerplus app, and the wholesaler app will be a separate app in the future, but for now it's a dependent app on the tagerplus app.

so we will call the UI of wholesaler app as wholesaler UI and the UI of tagerplus app as tagerplus UI.

every app has its own lib folder, and the shared code will be in the lib folder of the tagerplus app.

we will use the wholesaler UI inside of the tagerplus app, and we will use the tagerplus UI components inside of the wholesaler app.

the output folder is the android folder, and it will contain the build of the tagerplus app, and the wholesaler app will be a module inside of the tagerplus app.
